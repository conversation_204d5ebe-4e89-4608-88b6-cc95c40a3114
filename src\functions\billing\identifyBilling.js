export async function identify(account, customers, contracts) {
  const customer = customers.Items.find((e) =>
    e?.accounts?.map((e) => e?.account_id).includes(account)
  );
  const contract = contracts.Items.find(
    (contract) =>
      contract.customer.id === customer?.id &&
      contract.name.toLowerCase().includes("billing")
  );

  return {
    customer_name: customer?.names?.name || customer?.names?.fantasy_name,
    customer_id: customer?.id,
    contract_id: contract?.id,
  };
}
