import { json, message } from '../../shared/response';
import { getCorsMiddleware } from '../../middleware/corsMiddleware';
import { getCookieConfig } from '../../config/environments';

/**
 * Create cookie clearing header
 */
function createClearCookieHeader(name) {
  const cookieConfig = getCookieConfig();

  return `${name}=; HttpOnly; Secure=${cookieConfig.secure}; SameSite=${cookieConfig.sameSite}; Max-Age=0; Path=${cookieConfig.path}; Domain=${cookieConfig.domain}`;
}

/**
 * Handler to logout user and clear cookies
 */
const logoutHandler = async (event, context) => {
  try {
    // Create headers to clear authentication cookies
    const clearAuthCookie = createClearCookieHeader('dsm_auth_token');
    const clearRefreshCookie = createClearCookieHeader('dsm_refresh_token');

    const response = await json(await message('success', 'Logged out successfully'), 200);
    
    // Add cookie clearing headers
    response.multiValueHeaders = {
      'Set-Cookie': [clearAuthCookie, clearRefreshCookie]
    };

    return response;

  } catch (error) {
    console.error('Error during logout:', error);
    return await json(await message('error', error.message), 500);
  }
};

// Apply CORS middleware
export const handler = getCorsMiddleware('auth')(logoutHandler);
