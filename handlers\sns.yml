sns-publish:
  handler: src/functions/sns/publish.handler
  name: ${env:STAGE}-sns-publish${env:VERSION}
  description: Função para publicar uma mensagem no SNS do switch role
  memorySize: 128
  events:
    - http:
        path: /sns/publish
        method: post
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}


