const { responseWithCookies, responseWithBadRequest, responseWithError } = require('../../shared/response-cjs');
const { verifyToken, generateToken, isValidRefreshToken } = require('../../shared/auth/jwt-utils-secrets');
const { getRefreshTokenFromCookies, createC<PERSON><PERSON><PERSON>eader, getCookieConfig } = require('../../shared/auth/cookie-utils-cjs');

/**
 * Endpoint POST /auth/refresh
 * Verifica o refresh token no cookie e gera um novo access token
 */
exports.handler = async (event, context) => {
  try {
    console.log('Refresh token endpoint called');
    
    // Extrai refresh token dos cookies
    const refreshToken = getRefreshTokenFromCookies(event);
    
    if (!refreshToken) {
      return responseWithBadRequest('Refresh token não encontrado nos cookies');
    }
    
    // Verifica se o refresh token é válido
    let decoded;
    try {
      decoded = verifyToken(refreshToken);

      // Verifica se é realmente um refresh token
      if (decoded.type !== 'refresh') {
        return responseWithBadRequest('Token fornecido não é um refresh token válido');
      }

    } catch (error) {
      return responseWithBadRequest(`Refresh token inválido: ${error.message}`);
    }

    // Gera novo access token preservando metadados da conversão
    const newAccessToken = generateToken({
      sub: decoded.sub || decoded.userId,
      userId: decoded.sub || decoded.userId,
      email: decoded.email,
      role: decoded.role,
      permissions: decoded.permissions || [],
      name: decoded.name,
      // Preserva metadados se existirem
      tokenSource: decoded.tokenSource || 'internal',
      cognitoSub: decoded.cognitoSub,
      originalTokenUse: decoded.originalTokenUse,
      // Adiciona timestamp de refresh para auditoria
      refreshedAt: Math.floor(Date.now() / 1000)
    });
    
    // Cria header de cookie para o novo access token
    const cookieConfig = getCookieConfig();
    const accessTokenCookie = createCookieHeader(
      cookieConfig.ACCESS_TOKEN.name,
      newAccessToken,
      cookieConfig.ACCESS_TOKEN
    );
    
    // Dados de resposta
    const responseData = {
      message: 'Token renovado com sucesso',
      token: newAccessToken, // Retorna o token no corpo também para compatibilidade
      user: {
        id: decoded.sub || decoded.userId,
        email: decoded.email,
        role: decoded.role,
        name: decoded.name
      },
      expiresIn: '8h',
      refreshedAt: new Date().toISOString()
    };
    
    console.log(`Token renovado com sucesso para usuário: ${decoded.email}`);
    
    return responseWithCookies(responseData, [accessTokenCookie]);
    
  } catch (error) {
    console.error('Erro no endpoint refresh:', error);
    return responseWithError('Erro interno do servidor ao renovar token');
  }
};

/**
 * Função auxiliar para validar dados do usuário no refresh token
 * @param {Object} decoded - Token decodificado
 * @returns {boolean} True se os dados são válidos
 */
const isValidUserData = (decoded) => {
  return decoded && 
         (decoded.sub || decoded.userId) && 
         decoded.email &&
         typeof decoded.email === 'string' &&
         decoded.email.includes('@');
};

/**
 * Função auxiliar para criar payload do novo token
 * @param {Object} decoded - Dados do refresh token
 * @returns {Object} Payload para o novo access token
 */
const createNewTokenPayload = (decoded) => {
  return {
    sub: decoded.sub || decoded.userId,
    email: decoded.email,
    role: decoded.role || 'user',
    permissions: Array.isArray(decoded.permissions) ? decoded.permissions : [],
    name: decoded.name || decoded.email,
    refreshedAt: Math.floor(Date.now() / 1000),
    // Preserva outros campos relevantes se existirem
    ...(decoded.department && { department: decoded.department }),
    ...(decoded.company && { company: decoded.company })
  };
};
