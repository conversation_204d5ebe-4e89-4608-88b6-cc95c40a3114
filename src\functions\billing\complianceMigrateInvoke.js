import { message, json } from "../../shared/response";

const AWS = require("aws-sdk");
const region = "us-east-1";

async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

export const handler = async (event, context) => {
  try {
    const lambda = new AWS.Lambda({
      region,
    });

    const res = await lambda
      .invoke({
        FunctionName:
          process.env.FINOPS_STAGE + "-dsm-back-end-compliance-migrate",
      })
      .promise()
      .catch(async (err) => {
        console.log(
          "Couldn't invoke lambda function: ",
          process.env.FINOPS_STAGE + "-dsm-back-end-compliance-migrate"
        );
        return await sendDataToUser(500, "error", err);
      });

    console.log({ res });

    return await sendDataToUser(200, "success", { res });
  } catch (error) {
    console.log(error);
    return await sendDataToUser(500, "error", error);
  }
};
