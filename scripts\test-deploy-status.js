/**
 * <PERSON>ript para testar se o deploy das correções foi realizado
 */

const axios = require('axios');

const BASE_URL = 'https://pdu9dbemj1.execute-api.us-east-1.amazonaws.com/dev';

async function testDeployStatus() {
  console.log('🔍 Testando status do deploy das correções...\n');
  
  const origin = 'https://dev.dsm.darede.com.br';
  
  // Teste 1: Preflight do endpoint problemático
  console.log('1️⃣ Testando preflight OPTIONS /read/id/user');
  try {
    const preflightResponse = await axios.options(`${BASE_URL}/read/id/user`, {
      headers: {
        'Origin': origin,
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type,DynamoDB'
      },
      validateStatus: () => true,
      timeout: 10000
    });
    
    console.log(`   Status: ${preflightResponse.status}`);
    console.log(`   CORS Origin: ${preflightResponse.headers['access-control-allow-origin'] || 'Não definido'}`);
    
    if (preflightResponse.status === 200) {
      console.log('   ✅ PREFLIGHT FUNCIONANDO - Deploy realizado!');
    } else if (preflightResponse.status === 403) {
      console.log('   ❌ Deploy ainda não realizado (authorizer ativo)');
    } else {
      console.log(`   ⚠️  Status inesperado: ${preflightResponse.status}`);
    }
  } catch (error) {
    if (error.response?.data?.message === 'Missing Authentication Token') {
      console.log('   ❌ Deploy ainda não realizado (API Gateway authorizer ativo)');
    } else {
      console.log(`   ❌ Erro: ${error.message}`);
    }
  }
  
  // Teste 2: Preflight do endpoint de refresh
  console.log('\n2️⃣ Testando preflight OPTIONS /auth/refresh');
  try {
    const refreshPreflightResponse = await axios.options(`${BASE_URL}/auth/refresh`, {
      headers: {
        'Origin': origin,
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type'
      },
      validateStatus: () => true,
      timeout: 10000
    });
    
    console.log(`   Status: ${refreshPreflightResponse.status}`);
    console.log(`   CORS Origin: ${refreshPreflightResponse.headers['access-control-allow-origin'] || 'Não definido'}`);
    
    if (refreshPreflightResponse.status === 200 && 
        refreshPreflightResponse.headers['access-control-allow-origin'] === origin) {
      console.log('   ✅ PREFLIGHT REFRESH FUNCIONANDO - CORS corrigido!');
    } else if (refreshPreflightResponse.headers['access-control-allow-origin'] === '*') {
      console.log('   ⚠️  Ainda retornando wildcard (*) - correção pendente');
    } else {
      console.log('   ❌ Preflight ainda não funcionando');
    }
  } catch (error) {
    if (error.response?.data?.message === 'Missing Authentication Token') {
      console.log('   ❌ Deploy ainda não realizado');
    } else {
      console.log(`   ❌ Erro: ${error.message}`);
    }
  }
  
  // Teste 3: Endpoint de teste CORS (se foi deployado)
  console.log('\n3️⃣ Testando endpoint de teste /test/cors');
  try {
    const corsTestResponse = await axios.get(`${BASE_URL}/test/cors`, {
      headers: {
        'Origin': origin
      },
      validateStatus: () => true,
      timeout: 10000
    });
    
    console.log(`   Status: ${corsTestResponse.status}`);
    
    if (corsTestResponse.status === 200) {
      console.log('   ✅ ENDPOINT DE TESTE FUNCIONANDO - Deploy completo!');
    } else {
      console.log('   ❌ Endpoint de teste ainda não disponível');
    }
  } catch (error) {
    if (error.response?.data?.message === 'Missing Authentication Token') {
      console.log('   ❌ Endpoint de teste ainda não deployado');
    } else {
      console.log(`   ❌ Erro: ${error.message}`);
    }
  }
  
  // Teste 4: Requisição real ao endpoint protegido
  console.log('\n4️⃣ Testando requisição real /read/id/user');
  try {
    const realResponse = await axios.get(`${BASE_URL}/read/id/user`, {
      headers: {
        'Origin': origin,
        'DynamoDB': 'dev-permissions'
      },
      validateStatus: () => true,
      timeout: 10000
    });
    
    console.log(`   Status: ${realResponse.status}`);
    console.log(`   CORS Origin: ${realResponse.headers['access-control-allow-origin'] || 'Não definido'}`);
    
    if (realResponse.status === 401) {
      console.log('   ✅ ENDPOINT PROTEGIDO FUNCIONANDO - Retorna 401 (Unauthorized) como esperado');
    } else if (realResponse.status === 403) {
      console.log('   ❌ Ainda bloqueado por CORS');
    } else {
      console.log(`   ⚠️  Status inesperado: ${realResponse.status}`);
    }
  } catch (error) {
    console.log(`   ❌ Erro: ${error.message}`);
  }
  
  console.log('\n📊 Resumo do Status do Deploy:');
  console.log('='.repeat(50));
}

// Aguardar 5 minutos e executar teste
console.log('⏳ Aguardando 5 minutos para testar o deploy...');
console.log(`🕐 Teste será executado às ${new Date(Date.now() + 5 * 60 * 1000).toLocaleTimeString()}`);

setTimeout(async () => {
  console.log('\n🚀 Iniciando teste do deploy...\n');
  await testDeployStatus();
}, 5 * 60 * 1000); // 5 minutos

// Também permitir execução imediata se chamado com argumento
if (process.argv.includes('--now')) {
  console.log('🚀 Executando teste imediatamente...\n');
  testDeployStatus();
}
