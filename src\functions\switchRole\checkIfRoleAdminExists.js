import { CustomerEntity } from "../../entities/customer-entity";

import { DynamoDB } from "aws-sdk";
import { sendEmail } from "../../shared/emails";
import { getEmailTemplate } from "../../shared/emails/templates/failToCreateSwitchRole";
import { checkDaredeFullRole } from "../../shared/checkDaredeFullRole";

const dynamoDB = new DynamoDB.DocumentClient();
const TABLE_NAME = `${process.env.FINOPS_STAGE}-customers`;
const customerEntity = new CustomerEntity(dynamoDB, TABLE_NAME);
const MAX_RETRIES = 3;

const subject = "Falha na criação da switch-role";
const emails = [process.env.EMAIL_SWITCH_ROLES];
const from = "<EMAIL>";

exports.handler = async () => {
  try {
    let items = await customerEntity.scanDynamoDB();
    let accountsFailed = [];

    console.log(`Total de contas: ${items.length}`);

    for (const item of items) {
      const accounts = item.accounts || [];

      for (
        let accountIndex = 0;
        accountIndex < accounts.length;
        accountIndex++
      ) {
        const account = accounts[accountIndex];
        const roleExists = account.role_exists || false;
        const retries = account.retries ? account.retries : 0;

        if (!roleExists && retries < MAX_RETRIES) {
          console.log(`Verificando a role para a conta: ${account.account_id}`);
          console.log("retries first", account.retries);

          const responseMessage = await checkDaredeFullRole(account.account_id);
          console.log("Resposta da verificação:", responseMessage);

          if (responseMessage.success) {
            account.role_exists = true;

            await customerEntity.updateDynamoDBAccount(
              item.id,
              accountIndex,
              account
            );
          } else {
            account.retries = retries + 1;

            await customerEntity.updateDynamoDBAccount(
              item.id,
              accountIndex,
              account
            );
            console.log(
              `Incrementado retry para a conta: ${account.account_id} (Tentativas: ${account.retries})`
            );

            if (account.retries >= MAX_RETRIES) {
              accountsFailed.push(account);
            }
          }
        }
      }
    }

    if (accountsFailed.length > 0) {
      const html = getEmailTemplate(accountsFailed);
      await sendEmail(html, subject, emails, from);
      console.log(
        `Email enviado após ${MAX_RETRIES} tentativas falhas para as contas: ${accountsFailed}`
      );
    }
    console.log("Processamento concluído.");
  } catch (error) {
    console.error("Erro ao processar registros:", error);
    throw error;
  }
};
