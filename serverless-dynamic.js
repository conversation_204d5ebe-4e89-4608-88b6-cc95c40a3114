module.exports = () => {
  const glob = require('glob');
  const fs = require('fs');
  const YAML = require('yamljs');

  const commonLayers = [
    { Ref: 'AwsCoreLambdaLayer' },
    { Ref: 'UtilsLambdaLayer' },
  ];

  const files = glob.sync('./handlers/**/**.yml');
  const merged = files
    .map((file) => fs.readFileSync(`${file}`, 'utf8'))
    .map((raw) => YAML.parse(raw))
    .reduce((result, handler) => Object.assign(result, handler), {});

  console.info('Merging files:');
  console.info(files);

  Object.keys(merged).forEach((functionName) => {
    const functionConfig = merged[functionName];

    if (functionConfig.layers) {
      functionConfig.layers = functionConfig.layers
        .filter(layer => layer !== null)
        .map(layer => {
          if (typeof layer === 'object' && layer.Ref) {
            return layer;
          }

          if (typeof layer === 'string') {
            return { Ref: layer };
          }

          return layer;
        });

      commonLayers.forEach(layer => {
        const alreadyExists = functionConfig.layers.some(existingLayer =>
          existingLayer.Ref === layer.Ref
        );
        if (!alreadyExists) {
          functionConfig.layers.push(layer);
        }
      });
    } else {
      functionConfig.layers = [...commonLayers];
    }
  });

  return merged;
};
