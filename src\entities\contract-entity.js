export class ContractEntity {
  clientDynamo = null;
  constructor(clientDynamo) {
    this.clientDynamo = clientDynamo;
  }

  generateInputGetContracts(
    { active = 1, type_hours = "", customer_id = "", customer_itsm = "" } = {
      active: 1,
      type_hours: "",
      customer_id: "",
      customer_itsm: "",
    }
  ) {
    let params = {
      TableName: `${process.env.FINOPS_STAGE}-contracts`,
      IndexName: "list-index",
      KeyConditionExpression: "active = :active",
      ExpressionAttributeValues: {
        ":active": isNaN(active) ? 1 : active,
      },
    };

    if (type_hours) {
      params["IndexName"] = "active-type_hours-index";

      params["KeyConditionExpression"] =
        "active = :active AND type_hours = :type_hours";

      params["ExpressionAttributeValues"] = {
        ":active": active,
        ":type_hours": type_hours,
      };
    }

    if (customer_id) {
      params["IndexName"] = "customer_id-index";

      params["KeyConditionExpression"] = "customer_id = :customer_id";

      params["ExpressionAttributeValues"] = {
        ":customer_id": customer_id,
      };
    }
    if (customer_itsm) {
      params["IndexName"] = "customer_itsm-index";

      params["KeyConditionExpression"] = "customer_itsm = :customer_itsm";

      params["ExpressionAttributeValues"] = {
        ":customer_itsm": parseInt(customer_itsm),
      };
    }

    return params;
  }

  generateInputGetContractByName({ name = "" } = { name: "" }) {
    let params = {
      TableName: `${process.env.FINOPS_STAGE}-contracts`,
      IndexName: "name-index",
      ExpressionAttributeNames: {
        "#name": "name",
      },
      KeyConditionExpression: "#name = :name",
      ExpressionAttributeValues: {
        ":name": name,
      },
    };

    return params;
  }

  async getContracts(input) {
    let items = [];
    let lastEvaluatedKey = null;
  
    do {
      const params = {
        ...input,
      };
      if (lastEvaluatedKey) {
        params.ExclusiveStartKey = lastEvaluatedKey;
      }

      const response = await this.clientDynamo.query(params).promise();
  
      if (response.Items) {
        items = items.concat(response.Items);
      }
      lastEvaluatedKey = response.LastEvaluatedKey;
    } while (lastEvaluatedKey);
  

    return items;
  }
}
