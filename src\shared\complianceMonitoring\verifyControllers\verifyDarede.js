export async function verifyDarede(iam, roles) {
  for (let i = 0; i < roles.Roles.length; i++) {
    if (roles.Roles[i].RoleName === "darede") {
      let policies = await iam
        .listAttachedRolePolicies({ RoleName: "darede" })
        .promise();

      const existsReadOnlyAccess = policies?.AttachedPolicies?.find(
        (p) => p.PolicyName === "ReadOnlyAccess"
      );

      if (existsReadOnlyAccess) {
        console.log("Role darede exists and has ReadOnlyAccess attached");
        return true;
      } else {
        console.log(
          "Role darede exists but doesn't have ReadOnlyAccess attached"
        );
        return false;
      }
    }
  }
}
