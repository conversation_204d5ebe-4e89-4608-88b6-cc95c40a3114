send-email:
  handler: src/functions/email/send-email.handler
  name: ${self:custom.dotenv.STAGE}-send-email${self:custom.dotenv.VERSION}
  description: Função para envio de email
  memorySize: 128
  events:
    - http:
        path: /email/send-email
        method: post
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}


