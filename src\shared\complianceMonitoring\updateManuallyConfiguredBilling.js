import { S3, STS } from "aws-sdk";
import { put, readOne } from "../../model/dynamo";

export async function updateManuallyConfiguredBilling(
  customerId,
  contract,
  payerAccountList
) {
  const customer = await readOne(
    `${process.env.FINOPS_STAGE}-customers`,
    customerId
  );

  const payerAccountFound = customer?.Item?.accounts.find((account) =>
    payerAccountList.includes(account.account_id)
  );
  if (payerAccountFound) {
    const updatedBillingContract = await updateContract(
      contract,
      payerAccountFound.account_id
    );
    return updatedBillingContract;
  }
  return false;
}

export async function getCredentials() {
  const sts = new STS({
    region: process.env.REGION_LOCATION,
  });
  const { Credentials } = await sts
    .assumeRole({
      RoleArn: process.env.BILLING_ROLE_ARN,
      RoleSessionName: "destination-billing-customer-bucket",
    })
    .promise();
  return Credentials;
}

export async function getBucketPolicy(clientCredentials) {
  const s3Client = new S3({
    region: process.env.REGION_LOCATION,
    credentials: {
      accessKeyId: clientCredentials.AccessKeyId,
      secretAccessKey: clientCredentials.SecretAccessKey,
      sessionToken: clientCredentials.SessionToken,
    },
  });
  const bucketName = `${process.env.FINOPS_STAGE}-destination-billing-customer`;

  const bucketPolicy = await s3Client
    .getBucketPolicy({ Bucket: bucketName })
    .promise();
  return bucketPolicy;
}
export function listPayerAccounts(bucketPolicy) {
  const policy = JSON.parse(bucketPolicy.Policy);
  const principal = policy.Statement[0].Principal.AWS;
  const payerAccountList = [];
  principal.map((role) => payerAccountList.push(role.split(":")[4]));

  return payerAccountList;
}

async function updateContract(contract, payerAccount) {
  try {
    await put(`${process.env.FINOPS_STAGE}-contracts`, contract.id, {
      billingConfigured: { Action: "PUT", Value: true },
      payer_account: { Action: "PUT", Value: payerAccount },
    });
    console.log("Contract updated successfully");
    return true;
  } catch (error) {
    console.log(error);
    return false;
  }
}
