import { accessKeysCustomerAccount } from "../../accessKeysCustomerAccount";
import { verifyOUDenyLeaveOrg } from "./verifyOUDenyLeaveOrg";
import { verifyRootDenyLeavOrg } from "./verifyRootDenyLeaveOrg";

const AWS = require("aws-sdk");
const region = "us-east-1";

export async function verifyDenyLeaveOrganization(account) {
  console.log("\nVerifying DenyLeaveOrganization policy...");
  const newSession = await accessKeysCustomerAccount(account);

  if (newSession) {
    const organizations = new AWS.Organizations({
      region,
      credentials: {
        accessKeyId: newSession.access_key_id,
        secretAccessKey: newSession.secret_key_id,
        sessionToken: newSession.session_token,
      },
      MaxResults: 3,
    });

    const roots = await organizations
      .listRoots({})
      .promise()
      .catch((err) => {
        console.log({ err });
        return false;
      });

    console.log({ roots }, JSON.stringify(roots));

    if (!roots) {
      console.log(`There is no root account in this org: rootId - ${rootId}`);
      return false;
    }

    const rootId = roots.Roots[0].Id;

    console.log({ rootId });

    const rootAccountHasCorrectTarget = await verifyRootDenyLeavOrg(
      organizations,
      rootId
    );

    // const orgUnitHaveCorrectTarget = await verifyOUDenyLeaveOrg(
    //   organizations,
    //   rootId
    // );

    console.log({ rootAccountHasCorrectTarget });
    // console.log({ rootAccountHasCorrectTarget }, { orgUnitHaveCorrectTarget });

    if (rootAccountHasCorrectTarget) {
      console.log("Policy DenyLeaveOrganization is defined!");
      return true;
    } else {
      console.log("Policy DenyLeaveOrganization is not defined!");
      return false;
    }
  } else {
    return false;
  }
}
