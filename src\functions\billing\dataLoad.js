// import { parseBody, parseHeaders } from '../../shared/parsers'
import { message, json } from "../../shared/response";
import { makeLambda } from "../../shared/services/lambda-service";
import { readAll } from "../../model/dynamo";

const lambda = makeLambda();

const ACCOUNT_ID = process.env.ACCOUNT_ID;
const STAGE = process.env.FINOPS_STAGE;
const REGION = process.env.AWS_REGION_LOCATION;

async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

export const handler = async (event, context) => {
  try {
    const customers = await readAll(`${STAGE}-customers`);

    const accounts = customers.Items.filter(
      (customer) =>
        customer?.accounts?.filter((account) => account?.payer).length
    )
      .map((customer) =>
        customer.accounts
          .filter((account) => account?.payer)
          .map((account) => account.account_id)
      )
      .flat();

    for (const account of accounts) {
      await invokeActualMonth(account);
      await invokeReatroactiveMonth(account);
    }

    return await sendDataToUser(
      200,
      "success",
      "enviado para fila com sucesso!"
    );
  } catch (error) {
    return await sendDataToUser(500, "success", error);
  }
};

async function invokeActualMonth(account) {
  const date = new Date();
  const month = date.getMonth() + 1;

  const payload = {
    year: date.getFullYear(),
    month: month,
    account,
  };

  const response = lambda
    .invoke({
      FunctionName: `arn:aws:lambda:${REGION}:${ACCOUNT_ID}:function:${STAGE}-dsm-back-end-billing-manual`,
      Payload: JSON.stringify(payload),
      InvocationType: "Event",
    })
    .promise();

  return response;
}

async function invokeReatroactiveMonth(account) {
  const date = new Date();

  let response = null;

  if (date.getDate() <= 10) {
    try {
      console.log("Executando script para integeração do mês anterior");
      date.setMonth(date.getMonth() - 1);
      const month = date.getMonth() + 1;

      const payload = {
        year: date.getFullYear(),
        month: month,
        account,
      };

      response = lambda
        .invoke({
          FunctionName: `arn:aws:lambda:${REGION}:${ACCOUNT_ID}:function:${STAGE}-dsm-back-end-billing-manual`,
          Payload: JSON.stringify(payload),
          InvocationType: "Event",
        })
        .promise();

      console.log(
        "Script para integeração do mês anterior rodando em paralelo"
      );
    } catch (error) {
      console.log(error);
      console.log(
        "Não foi possivel executar o script para integeração do mês anterior"
      );
    }
  }

  return response;
}
