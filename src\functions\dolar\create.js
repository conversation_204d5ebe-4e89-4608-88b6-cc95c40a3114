import { message, json } from '../../shared/response'
import { parseBody } from '../../shared/parsers'
import { create, put, readAll } from '../../model/dynamo'
import { format } from 'date-fns'

async function sendDataToUser (status, statusString, data) {
  const msg = await message(statusString, data)
  return await json(msg, status)
};

export const handler = async (event, context) => {
  try {
    const { value, month, year } = parseBody(event)

    const dates = {
      created_at: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
      updated_at: format(new Date(), 'yyyy-MM-dd HH:mm:ss')
    }

    const { Items } = await readAll(`${process.env.FINOPS_STAGE}-dolar`)

    const sameMonth = Items.find(obj => obj.month === month && obj.year === year)

    if (sameMonth) {
      const updated_dolar = {
        ...sameMonth,
        value: value,
        updated_at: format(new Date(), 'yyyy-MM-dd HH:mm:ss')
      }

      const obj = {}

      Object.entries(updated_dolar).forEach((o) => {
        if (o[0] !== 'id') {
          obj[o[0]] = { Action: 'PUT', Value: o[1] }
        }
      })

      console.log('Dólar já existente, realizando atualização no mesmo.')

      await put(`${process.env.FINOPS_STAGE}-dolar`, sameMonth.id, obj)

      return await sendDataToUser(200, 'success', 'Dólar já existente, realizando atualização no mesmo.')
    }

    const createObjDynamo = await create(`${process.env.FINOPS_STAGE}-dolar`, {
      ...dates,
      value,
      month,
      year
    })

    return await sendDataToUser(200, 'success', createObjDynamo)
  } catch (error) {
    return await sendDataToUser(500, 'error', error)
  };
}
