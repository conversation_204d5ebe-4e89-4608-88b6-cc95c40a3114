/**
 * Exemplo de como implementar a detecção automática de cookies HttpOnly no React
 * Substitua o código existente por este exemplo
 */

import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { initializeAuth, useAuthConfig } from './auth-config-detector';

// Configuração base do Axios
const api = axios.create({
  baseURL: 'https://pdu9dbemj1.execute-api.us-east-1.amazonaws.com/dev',
  headers: {
    'Content-Type': 'application/json'
  }
});

// Componente principal da aplicação
function App() {
  const [authConfig, setAuthConfig] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);

  // Inicializar autenticação quando o componente montar
  useEffect(() => {
    async function setupAuth() {
      try {
        console.log('🚀 Inicializando sistema de autenticação...');
        
        // Detectar e configurar suporte a cookies automaticamente
        const config = await initializeAuth(api);
        setAuthConfig(config);
        
        console.log('✅ Autenticação configurada:', {
          httpOnlySupported: config.httpOnlySupported,
          withCredentials: config.withCredentials,
          tokenStorage: config.tokenStorage
        });
        
      } catch (error) {
        console.error('❌ Erro ao configurar autenticação:', error);
        
        // Configuração fallback
        setAuthConfig({
          httpOnlySupported: false,
          withCredentials: false,
          tokenStorage: 'localStorage',
          fallbackToHeaders: true
        });
      } finally {
        setIsLoading(false);
      }
    }
    
    setupAuth();
  }, []);

  // Função de login
  const handleLogin = async (credentials) => {
    try {
      console.log('🔐 Fazendo login...');
      
      // 1. Fazer login (substitua pela sua lógica de login)
      const loginResponse = await api.post('/auth/login', credentials);
      const { token, user } = loginResponse.data;
      
      console.log('✅ Login bem-sucedido:', { user: user.email });
      
      // 2. Configurar token baseado no tipo de armazenamento
      if (authConfig.httpOnlySupported) {
        // Usar cookies HttpOnly
        console.log('🍪 Configurando token em cookies HttpOnly...');
        await api.post('/dev/auth/set-token', { token });
        console.log('✅ Token armazenado em cookies seguros');
      } else {
        // Usar localStorage + Authorization header
        console.log('💾 Configurando token em localStorage...');
        localStorage.setItem('access_token', token);
        api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        console.log('✅ Token armazenado em localStorage');
      }
      
      setUser(user);
      
    } catch (error) {
      console.error('❌ Erro no login:', error);
      throw error;
    }
  };

  // Função para fazer requisições autenticadas
  const fetchUserData = async () => {
    try {
      console.log('📊 Buscando dados do usuário...');
      
      // A configuração de cookies/headers já foi feita automaticamente
      const response = await api.get('/dev/cognito/read');
      
      console.log('✅ Dados recebidos:', {
        count: response.data.length,
        httpOnlySupported: authConfig.httpOnlySupported,
        withCredentials: api.defaults.withCredentials
      });
      
      return response.data;
      
    } catch (error) {
      console.error('❌ Erro ao buscar dados:', error);
      throw error;
    }
  };

  // Função de logout
  const handleLogout = async () => {
    try {
      console.log('🚪 Fazendo logout...');
      
      if (authConfig.httpOnlySupported) {
        // Limpar cookies HttpOnly
        await api.post('/dev/auth/logout');
        console.log('✅ Cookies limpos');
      } else {
        // Limpar localStorage
        localStorage.removeItem('access_token');
        delete api.defaults.headers.common['Authorization'];
        console.log('✅ localStorage limpo');
      }
      
      setUser(null);
      
    } catch (error) {
      console.error('❌ Erro no logout:', error);
    }
  };

  if (isLoading) {
    return (
      <div>
        <h2>🔄 Configurando autenticação...</h2>
        <p>Detectando suporte a cookies HttpOnly...</p>
      </div>
    );
  }

  return (
    <div>
      <h1>Sistema de Autenticação DSM</h1>
      
      {/* Status da configuração */}
      <div style={{ 
        padding: '10px', 
        margin: '10px 0', 
        backgroundColor: authConfig.httpOnlySupported ? '#d4edda' : '#fff3cd',
        border: `1px solid ${authConfig.httpOnlySupported ? '#c3e6cb' : '#ffeaa7'}`,
        borderRadius: '4px'
      }}>
        <h3>📋 Configuração de Autenticação</h3>
        <ul>
          <li>🍪 <strong>Cookies HttpOnly:</strong> {authConfig.httpOnlySupported ? '✅ Suportado' : '❌ Não suportado'}</li>
          <li>🔗 <strong>withCredentials:</strong> {authConfig.withCredentials ? '✅ Ativo' : '❌ Inativo'}</li>
          <li>💾 <strong>Armazenamento:</strong> {authConfig.tokenStorage}</li>
          <li>🔄 <strong>Fallback:</strong> {authConfig.fallbackToHeaders ? '✅ Ativo' : '❌ Inativo'}</li>
        </ul>
      </div>
      
      {/* Interface do usuário */}
      {user ? (
        <div>
          <h2>👤 Usuário logado: {user.email}</h2>
          <button onClick={fetchUserData}>📊 Buscar dados</button>
          <button onClick={handleLogout}>🚪 Logout</button>
        </div>
      ) : (
        <div>
          <h2>🔐 Fazer Login</h2>
          <button onClick={() => handleLogin({ email: '<EMAIL>', password: 'password' })}>
            Entrar
          </button>
        </div>
      )}
    </div>
  );
}

export default App;
