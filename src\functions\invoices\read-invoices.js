import { parseQueryString } from "../../shared/parsers";
import { message, json, responseWithBadRequest } from "../../shared/response";
import { InvoiceEntity } from "../../entities/invoice-entity";
import { makeDynamoDB } from "../../shared/services/dynamo-service";
async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

export const handler = async (event, context) => {
  const clientDynamo = makeDynamoDB();
  const invoiceEntity = new InvoiceEntity(clientDynamo);
  const { fullDate, payerAccount, startDate, endDate } =
    parseQueryString(event);

  try {
    let params;

    if (fullDate) {
      params = invoiceEntity.formatInvoicesParameters(fullDate);
    } else if (payerAccount) {
      params =
        invoiceEntity.formatInvoicesParametersByPayerAccount(payerAccount);
    } else {
      return responseWithBadRequest(
        "Missing required key 'fullDate' or 'payerAccount' in params"
      );
    }

    let invoiceList = await invoiceEntity.getInvoices(params);

    if (startDate && endDate) {
      invoiceList = invoiceEntity.filterInvoicesByRange(
        startDate,
        endDate,
        invoiceList
      );
    }

    return await sendDataToUser(200, "success", invoiceList);
  } catch (error) {
    console.log(error);
    return await sendDataToUser(500, "error", error);
  }
};
