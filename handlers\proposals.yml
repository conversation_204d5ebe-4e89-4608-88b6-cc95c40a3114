update-proposals-persist-data:
  handler: src/functions/proposals/update-persist-data.handler
  name: ${self:custom.dotenv.STAGE}-update-proposals-persist-data${self:custom.dotenv.VERSION}
  description: Função responsavel por persistir os dados de propostas não concluidas
  memorySize: 128
  events:
    - http:
        path: /proposals/persit
        method: put
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}

get-proposals:
  handler: src/functions/proposals/get-proposals.handler
  name: ${self:custom.dotenv.STAGE}-get-proposals${self:custom.dotenv.VERSION}
  description: Função para buscar as propostas do DSM
  memorySize: 128
  timeout: 30
  events:
    - http:
        path: /proposals
        method: get
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}


