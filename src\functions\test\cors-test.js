/**
 * Endpoint de teste para verificar configuração de CORS
 * Usado para debug de problemas de comunicação frontend-backend
 */

const { responseWithSuccess } = require('../../shared/response-cjs');
const { withAuthCors } = require('../../shared/cors/cors-middleware');

const corsTestHandler = async (event, context) => {
  console.log('🧪 CORS Test endpoint chamado');
  console.log('Headers recebidos:', event.headers);
  console.log('Origem:', event.headers?.Origin || event.headers?.origin);
  console.log('Método:', event.httpMethod);
  console.log('Path:', event.path);
  
  // Informações sobre cookies
  const cookies = event.headers?.Cookie || event.headers?.cookie;
  console.log('Cookies recebidos:', cookies);
  
  // Informações sobre o request
  const requestInfo = {
    method: event.httpMethod,
    path: event.path,
    origin: event.headers?.Origin || event.headers?.origin,
    userAgent: event.headers?.['User-Agent'] || event.headers?.['user-agent'],
    cookies: cookies ? 'Presentes' : 'Ausentes',
    timestamp: new Date().toISOString(),
    
    // Headers importantes para CORS
    corsHeaders: {
      origin: event.headers?.Origin || event.headers?.origin,
      accessControlRequestMethod: event.headers?.['Access-Control-Request-Method'],
      accessControlRequestHeaders: event.headers?.['Access-Control-Request-Headers'],
    },
    
    // Informações do ambiente
    environment: {
      nodeEnv: process.env.NODE_ENV,
      allowedOrigins: process.env.ALLOWED_ORIGINS,
      stage: process.env.STAGE
    }
  };
  
  console.log('✅ CORS Test - Informações coletadas:', requestInfo);
  
  return responseWithSuccess({
    message: 'CORS Test endpoint funcionando',
    status: 'success',
    data: requestInfo
  });
};

 
export const handler = async (event) => {
  return corsTestHandler(event);
};
