/**
 * Script para testar a API set-token com token válido
 */

// Carrega variáveis de ambiente do .env.dev
require('dotenv').config({ path: '.env.dev' });

const axios = require('axios');
const { generateToken } = require('../src/shared/auth/jwt-utils-secrets');

// Configurações
const API_URL = 'http://localhost:8000/dev/auth/set-token';

/**
 * Gera um token JWT válido para teste usando a biblioteca interna
 */
async function generateTestToken() {
  const payload = {
    sub: 'test-user-123',
    userId: 'test-user-123',
    email: '<EMAIL>',
    role: 'user',
    permissions: ['read', 'write'],
    name: '<PERSON><PERSON><PERSON><PERSON>'
  };

  return await generateToken(payload);
}

/**
 * Testa a API set-token
 */
async function testSetTokenAPI() {
  try {
    console.log('🧪 Testando API set-token com Secrets Manager\n');

    // Gera token válido
    const testToken = await generateTestToken();
    console.log('✅ Token de teste gerado');
    console.log(`   Token (primeiros 50 chars): ${testToken.substring(0, 50)}...`);

    // Decodifica para mostrar payload (usando jwt diretamente para decode)
    const jwt = require('jsonwebtoken');
    const decoded = jwt.decode(testToken);
    console.log('📋 Payload do token:');
    console.log(`   Email: ${decoded.email}`);
    console.log(`   Role: ${decoded.role}`);
    console.log(`   Expires: ${new Date(decoded.exp * 1000).toISOString()}\n`);

    // Configura axios
    const axiosConfig = {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:3000'
      }
    };

    console.log('🚀 Enviando requisição para API...');

    // Faz requisição
    const response = await axios.post(API_URL, {
      token: testToken
    }, axiosConfig);

    console.log('✅ Resposta recebida:');
    console.log(`   Status: ${response.status}`);
    console.log(`   Message: ${response.data.message}`);
    
    if (response.data.user) {
      console.log(`   User Email: ${response.data.user.email || 'N/A'}`);
      console.log(`   User Role: ${response.data.user.role || 'N/A'}`);
    }

    if (response.data.tokenInfo) {
      console.log(`   Token Source: ${response.data.tokenInfo.source || 'N/A'}`);
      console.log(`   Cookies Set: ${response.data.tokenInfo.cookiesSet || false}`);
    }

    // Verifica headers CORS
    console.log('\n🔍 Headers CORS:');
    console.log(`   Access-Control-Allow-Origin: ${response.headers['access-control-allow-origin'] || 'N/A'}`);
    console.log(`   Access-Control-Allow-Credentials: ${response.headers['access-control-allow-credentials'] || 'N/A'}`);

    // Verifica cookies
    if (response.headers['set-cookie']) {
      console.log('\n🍪 Cookies definidos:');
      response.headers['set-cookie'].forEach((cookie, index) => {
        console.log(`   ${index + 1}. ${cookie.substring(0, 100)}...`);
      });
    }

    console.log('\n🎉 Teste concluído com sucesso!');
    return true;

  } catch (error) {
    console.error('\n❌ Erro no teste:');
    
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Data: ${JSON.stringify(error.response.data, null, 2)}`);
      console.error(`   Headers: ${JSON.stringify(error.response.headers, null, 2)}`);
    } else if (error.request) {
      console.error('   Erro de rede - servidor não respondeu');
      console.error(`   Request: ${error.request}`);
    } else {
      console.error(`   Erro: ${error.message}`);
    }

    return false;
  }
}

/**
 * Testa token inválido
 */
async function testInvalidToken() {
  try {
    console.log('\n🧪 Testando token inválido...');

    const invalidToken = 'token.invalido.aqui';

    const response = await axios.post(API_URL, {
      token: invalidToken
    }, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:3000'
      }
    });

    console.log('⚠️ Token inválido foi aceito (não deveria)');
    console.log(`   Status: ${response.status}`);
    console.log(`   Data: ${JSON.stringify(response.data, null, 2)}`);

  } catch (error) {
    if (error.response && error.response.status >= 400) {
      console.log('✅ Token inválido rejeitado corretamente');
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Message: ${error.response.data.message || 'N/A'}`);
    } else {
      console.error('❌ Erro inesperado:', error.message);
    }
  }
}

/**
 * Verifica se o servidor está rodando
 */
async function checkServerStatus() {
  try {
    console.log('🔍 Verificando se o servidor está rodando...');
    
    const response = await axios.get('http://localhost:8000/dev/auth/config', {
      timeout: 5000,
      headers: {
        'Origin': 'http://localhost:3000'
      }
    });

    console.log('✅ Servidor está rodando');
    console.log(`   Status: ${response.status}`);
    return true;

  } catch (error) {
    console.error('❌ Servidor não está rodando ou não responde');
    console.error('   Execute: yarn dev');
    return false;
  }
}

/**
 * Executa todos os testes
 */
async function runAllTests() {
  console.log('🚀 Iniciando testes da API set-token\n');
  console.log('=' .repeat(60));

  // Verifica servidor
  const serverRunning = await checkServerStatus();
  if (!serverRunning) {
    process.exit(1);
  }

  console.log('\n' + '=' .repeat(60));

  // Teste principal
  const mainTestPassed = await testSetTokenAPI();

  // Teste de token inválido
  await testInvalidToken();

  console.log('\n' + '=' .repeat(60));
  console.log('📊 RESUMO DOS TESTES');
  console.log(`   ✅ Teste principal: ${mainTestPassed ? 'PASSOU' : 'FALHOU'}`);
  console.log(`   🔒 Fallback funcionando: ${mainTestPassed ? 'SIM' : 'NÃO'}`);
  console.log(`   🌐 CORS configurado: ${mainTestPassed ? 'SIM' : 'NÃO'}`);

  if (mainTestPassed) {
    console.log('\n🎉 API set-token está funcionando com Secrets Manager (fallback)!');
    console.log('💡 Para usar Secrets Manager completo, configure as permissões AWS.');
  } else {
    console.log('\n❌ API set-token tem problemas que precisam ser corrigidos.');
  }

  return mainTestPassed;
}

// Executa testes se o arquivo for executado diretamente
if (require.main === module) {
  runAllTests()
    .then(success => process.exit(success ? 0 : 1))
    .catch(error => {
      console.error('💥 Erro fatal nos testes:', error);
      process.exit(1);
    });
}

module.exports = {
  generateTestToken,
  testSetTokenAPI,
  testInvalidToken,
  checkServerStatus,
  runAllTests
};
