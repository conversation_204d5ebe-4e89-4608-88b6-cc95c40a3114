#!/usr/bin/env node

/**
 * Script para testar a verificação de assinatura do token Cognito
 */

const { CognitoJwtVerifier } = require('aws-jwt-verify');
const jwt = require('jsonwebtoken');

console.log('🔐 TESTE DE VERIFICAÇÃO DE ASSINATURA COGNITO');
console.log('=============================================\n');

// Configuração do verificador (mesma do set-token.js)
const cognitoVerifier = CognitoJwtVerifier.create({
  userPoolId: "us-east-1_VCf8aHRIZ",
  tokenUse: "id",
  clientId: "a612mhbbrvrh45ec6n8amqf5i"
});

/**
 * Testa verificação com token válido (simulado)
 */
async function testValidToken() {
  console.log('🧪 Teste 1: Token Cognito válido\n');
  
  // Token de exemplo dos logs anteriores (apenas para estrutura)
  const mockValidToken = "eyJraWQiOiJnMjhpYU5rc2xSYWpVcnlYMnBBZmhkSEZGcUdWQ1..."; // Token truncado
  
  try {
    console.log('🔍 Tentando verificar token válido...');
    console.log(`Token: ${mockValidToken.substring(0, 50)}...`);
    
    // Nota: Este teste falhará porque não temos um token real válido
    // Mas mostra como a verificação funciona
    const payload = await cognitoVerifier.verify(mockValidToken);
    
    console.log('✅ Token verificado com sucesso!');
    console.log('Payload:', {
      sub: payload.sub,
      email: payload.email,
      token_use: payload.token_use,
      iss: payload.iss
    });
    
    return true;
    
  } catch (error) {
    console.log('❌ Erro esperado (token truncado/inválido):', error.name);
    console.log('   Mensagem:', error.message);
    return false;
  }
}

/**
 * Testa verificação com token falsificado
 */
async function testFakeToken() {
  console.log('\n🧪 Teste 2: Token falsificado\n');
  
  // Cria um token fake com estrutura similar ao Cognito
  const fakePayload = {
    "sub": "fake-user-id",
    "aud": "a612mhbbrvrh45ec6n8amqf5i",
    "cognito:groups": ["us-east-1_VCf8aHRIZ_AzureAD"],
    "email_verified": false,
    "iss": "https://cognito-idp.us-east-1.amazonaws.com/us-east-1_VCf8aHRIZ",
    "cognito:username": "<EMAIL>",
    "token_use": "id",
    "auth_time": Math.floor(Date.now() / 1000),
    "exp": Math.floor(Date.now() / 1000) + 3600,
    "iat": Math.floor(Date.now() / 1000),
    "email": "<EMAIL>"
  };
  
  // Assina com chave falsa
  const fakeToken = jwt.sign(fakePayload, 'fake-secret-key', { algorithm: 'HS256' });
  
  try {
    console.log('🔍 Tentando verificar token falsificado...');
    console.log(`Token fake: ${fakeToken.substring(0, 50)}...`);
    
    const payload = await cognitoVerifier.verify(fakeToken);
    
    console.log('🚨 PROBLEMA: Token falsificado foi aceito!');
    console.log('Payload:', payload);
    
    return false; // Não deveria chegar aqui
    
  } catch (error) {
    console.log('✅ Token falsificado rejeitado corretamente!');
    console.log('   Erro:', error.name);
    console.log('   Mensagem:', error.message);
    return true;
  }
}

/**
 * Testa verificação com token expirado
 */
async function testExpiredToken() {
  console.log('\n🧪 Teste 3: Token expirado\n');
  
  // Cria um token com expiração no passado
  const expiredPayload = {
    "sub": "expired-user-id",
    "aud": "a612mhbbrvrh45ec6n8amqf5i",
    "iss": "https://cognito-idp.us-east-1.amazonaws.com/us-east-1_VCf8aHRIZ",
    "token_use": "id",
    "exp": Math.floor(Date.now() / 1000) - 3600, // 1 hora no passado
    "iat": Math.floor(Date.now() / 1000) - 7200, // 2 horas no passado
    "email": "<EMAIL>"
  };
  
  const expiredToken = jwt.sign(expiredPayload, 'fake-secret-key', { algorithm: 'HS256' });
  
  try {
    console.log('🔍 Tentando verificar token expirado...');
    console.log(`Token expirado: ${expiredToken.substring(0, 50)}...`);
    
    const payload = await cognitoVerifier.verify(expiredToken);
    
    console.log('🚨 PROBLEMA: Token expirado foi aceito!');
    return false;
    
  } catch (error) {
    console.log('✅ Token expirado rejeitado corretamente!');
    console.log('   Erro:', error.name);
    console.log('   Mensagem:', error.message);
    return true;
  }
}

/**
 * Mostra configuração do verificador
 */
function showVerifierConfig() {
  console.log('⚙️ CONFIGURAÇÃO DO VERIFICADOR');
  console.log('==============================\n');
  
  console.log('User Pool ID: us-east-1_VCf8aHRIZ');
  console.log('Client ID: a612mhbbrvrh45ec6n8amqf5i');
  console.log('Token Use: id');
  console.log('JWKS URI: https://cognito-idp.us-east-1.amazonaws.com/us-east-1_VCf8aHRIZ/.well-known/jwks.json');
  console.log('');
}

/**
 * Função principal
 */
async function main() {
  showVerifierConfig();
  
  const test1 = await testValidToken();
  const test2 = await testFakeToken();
  const test3 = await testExpiredToken();
  
  console.log('\n📊 RELATÓRIO FINAL');
  console.log('==================\n');
  
  console.log(`🧪 Teste token válido: ${test1 ? 'PASSOU' : 'FALHOU (esperado)'}`);
  console.log(`🛡️ Teste token falsificado: ${test2 ? 'PASSOU' : 'FALHOU'}`);
  console.log(`⏰ Teste token expirado: ${test3 ? 'PASSOU' : 'FALHOU'}`);
  
  const securityWorking = test2 && test3;
  
  if (securityWorking) {
    console.log('\n🎉 ✅ VERIFICAÇÃO DE ASSINATURA FUNCIONANDO!');
    console.log('✅ Tokens falsificados são rejeitados');
    console.log('✅ Tokens expirados são rejeitados');
    console.log('✅ Sistema seguro contra ataques de token');
  } else {
    console.log('\n⚠️ Problemas na verificação de segurança');
    console.log('🔧 Verifique a configuração do verificador');
  }
  
  console.log('\n💡 NOTA: Para testar com token real válido,');
  console.log('   use um token obtido do frontend em DEV');
}

// Executa apenas se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
}
