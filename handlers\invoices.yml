get-invoices:
  handler: src/functions/invoices/get-invoices.handler
  name: ${self:custom.dotenv.STAGE}-get-invoices${self:custom.dotenv.VERSION}
  description: Função para buscar os invoices dos clientes em um periodo de data
  memorySize: 128
  timeout: 30
  events:
    - schedule:
        rate: rate(1 day)
        name: ${self:custom.dotenv.STAGE}-get-invoices-schedule${self:custom.dotenv.VERSION}
    - http:
        path: /invoices
        method: post
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}

read-invoices:
  handler: src/functions/invoices/read-invoices.handler
  name: ${self:custom.dotenv.STAGE}-read-invoices${self:custom.dotenv.VERSION}
  description: Função para buscar os invoices dos clientes em um periodo de data
  memorySize: 128
  timeout: 30
  events:
    - http:
        path: /read-invoices
        method: get
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}


