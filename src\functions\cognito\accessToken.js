import { message, json } from "../../shared/response";
import { generateToken } from "../../providers/otrs-api-provider";

async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

export const handler = async (event) => {
  console.log("Event:", event);

  try {
    const accessToken = await generateToken();
    return await sendDataToUser(200, "success", accessToken);
  } catch (error) {
    console.log(error);
    return await sendDataToUser(500, "error", error);
  }
};
