import axios from 'axios'
import { getWafToken } from '../services/secreats-manager-service'

const DSM_API_URL = process.env.DSM_API_URL
export const dsmAPI = axios.create({
    baseURL: DSM_API_URL
})


const SECREAT_MANAGER = process.env.DSM_API_SECREAT_MANAGER

export async function getDSMToken() {
    const wafToken = await getWafToken()

    const payload = { "arnSecret": SECREAT_MANAGER }
    const headers = { 'x-waf-header': wafToken }

    const { data } = await dsmAPI.post('/cognito/token', payload, { headers })

    return data.data
}