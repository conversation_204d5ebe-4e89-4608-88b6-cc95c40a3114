const { StepFunctions, STS } = require("aws-sdk");
const { message, json } = require("../../shared/response");

async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}
const sts = new STS({
  region: "us-east-1",
});
export const handler = async (event) => {
  try {
    const {
      Credentials: { AccessKeyId, SecretAccessKey, SessionToken },
    } = await sts
      .assumeRole({
        RoleArn: "arn:aws:iam::405251468086:role/jump-access-roles",
        RoleSessionName: "cross_acct_lambda",
      })
      .promise();
    const stepfunctions = new StepFunctions({
      region: "us-east-1",
      accessKeyId: AccessKeyId,
      secretAccessKey: SecretAccessKey,
      sessionToken: SessionToken,
    });
    try {
      const res = await stepfunctions
        .startExecution({
          stateMachineArn: `arn:aws:states:us-east-1:405251468086:stateMachine:${process.env.FINOPS_STAGE}-SwitchRoleStateMachine`,
          input: event.body,
        })
        .promise();
      return await sendDataToUser(200, "success", res);
    } catch (e) {
      return await sendDataToUser(
        500,
        "Erro na execução da step function: ",
        e
      );
    }
  } catch (e) {
    return await sendDataToUser(500, "Erro na função de assume role: ", e);
  }
};
