# Aná<PERSON><PERSON> dos Erros do Frontend

## 🚨 Problemas Identificados

### 1. **Erro CORS Preflight - Status HTTP não OK**
```
Access to XMLHttpRequest at 'https://pdu9dbemj1.execute-api.us-east-1.amazonaws.com/dev/read/id/user' 
from origin 'https://dev.dsm.darede.com.br' has been blocked by CORS policy: 
Response to preflight request doesn't pass access control check: It does not have HTTP ok status.
```

**Causa:** O backend ainda tem o authorizer do API Gateway ativo no endpoint `/read/id/user`, impedindo que requisições OPTIONS passem pelo middleware customizado.

### 2. **Erro CORS Wildcard com Credentials**
```
Access to XMLHttpRequest at 'https://pdu9dbemj1.execute-api.us-east-1.amazonaws.com/dev/auth/refresh' 
from origin 'https://dev.dsm.darede.com.br' has been blocked by CORS policy: 
The value of the 'Access-Control-Allow-Origin' header in the response must not be the wildcard '*' 
when the request's credentials mode is 'include'.
```

**Causa:** O endpoint `/auth/refresh` está retornando `Access-Control-Allow-Origin: *` em vez da origem específica quando `withCredentials: true`.

## 🔧 Correções Necessárias

### **BACKEND (Prioridade Alta - Deploy Necessário)**

1. **Remover Authorizer do API Gateway**
   ```yaml
   # handlers/dynamodb.yml - linha 23
   # authorizer: ${self:custom.authorizer} # REMOVER ESTA LINHA
   ```

2. **Verificar Middleware CORS nos Endpoints de Auth**
   - Garantir que `/auth/refresh` usa `withAuthCors` corretamente
   - Verificar se não está retornando wildcard `*` com credentials

### **FRONTEND (Correções Imediatas)**

#### 1. **Arquivo: `src/services/httpOnlyAuthService.js`**

**Problema:** Interceptor pode estar causando loops ou conflitos.

```javascript
// Adicionar verificação de retry e timeout
async refreshToken() {
  try {
    await this.ensureInitialized();
    
    const response = await axios.post(`${this.baseURL}/auth/refresh`, {}, {
      withCredentials: true,
      timeout: 10000, // Timeout específico
      // Evitar interceptors para refresh
      skipInterceptors: true
    });
    
    return response.data;
  } catch (error) {
    logger.error('Falha na renovação do token', {
      error: error.message,
      status: error.response?.status
    });
    
    // Se erro de CORS, não tentar novamente
    if (error.message.includes('CORS') || error.code === 'ERR_NETWORK') {
      throw new Error('CORS_ERROR');
    }
    
    throw error;
  }
}
```

#### 2. **Arquivo: `src/service/apiDsmDynamo.js`**

**Problema:** Header `dynamodb` deve ser `DynamoDB` (maiúsculo).

```javascript
export const dynamoGetById = async (tableName, id) => {
  if (!id || id === 'null' || id === 'undefined') {
    console.warn('⚠️ dynamoGetById: ID inválido fornecido:', id);
    throw new Error('ID é obrigatório e deve ser válido');
  }

  if (!tableName) {
    console.warn('⚠️ dynamoGetById: tableName é obrigatório');
    throw new Error('tableName é obrigatório');
  }

  try {
    const apiUrl = await getApiUrl();
    const url = `${apiUrl}/read/id/${id}`;

    console.log('🔍 dynamoGetById: Fazendo requisição para:', url);

    const { data } = await Axios.get(url, {
      headers: {
        'DynamoDB': tableName, // ✅ CORRIGIDO: Era 'dynamodb', agora 'DynamoDB'
        ...httpOnlyAuthService.getLegacyAuthHeaders(),
      },
      withCredentials: true,
      timeout: 30000 // Adicionar timeout
    });

    return data.data.Item;
  } catch (error) {
    console.error('❌ dynamoGetById: Erro na requisição:', {
      tableName,
      id,
      error: error.message,
      status: error.response?.status
    });
    
    // Tratamento específico para erros de CORS
    if (error.message.includes('CORS') || error.code === 'ERR_NETWORK') {
      throw new Error(`CORS_ERROR: Verifique se o backend foi deployado com as correções`);
    }
    
    throw error;
  }
};
```

#### 3. **Adicionar Interceptor Global para Debug**

**Arquivo:** `src/services/axiosInterceptors.js` (criar se não existir)

```javascript
import axios from 'axios';
import { logger } from '../utils/logger';

// Interceptor de requisição
axios.interceptors.request.use(
  (config) => {
    // Garantir withCredentials em todas as requisições
    config.withCredentials = true;
    
    // Log para debug
    console.log('🔍 Requisição:', {
      url: config.url,
      method: config.method,
      withCredentials: config.withCredentials,
      headers: config.headers
    });
    
    return config;
  },
  (error) => {
    console.error('❌ Erro no interceptor de requisição:', error);
    return Promise.reject(error);
  }
);

// Interceptor de resposta
axios.interceptors.response.use(
  (response) => {
    console.log('✅ Resposta recebida:', {
      url: response.config.url,
      status: response.status,
      corsOrigin: response.headers['access-control-allow-origin']
    });
    return response;
  },
  (error) => {
    console.error('❌ Erro na resposta:', {
      message: error.message,
      code: error.code,
      status: error.response?.status,
      url: error.config?.url,
      corsError: error.message.includes('CORS')
    });
    
    // Tratamento específico para erros de CORS
    if (error.message.includes('CORS')) {
      logger.error('CORS Error detectado - Backend pode não estar deployado com correções');
    }
    
    return Promise.reject(error);
  }
);
```

#### 4. **Implementar Retry Logic**

**Arquivo:** `src/utils/retryRequest.js` (criar)

```javascript
export const retryRequest = async (requestFn, maxRetries = 3, delay = 1000) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await requestFn();
    } catch (error) {
      console.warn(`⚠️ Tentativa ${i + 1} falhou:`, error.message);
      
      // Não tentar novamente para erros de CORS
      if (error.message.includes('CORS') || error.code === 'ERR_NETWORK') {
        throw error;
      }
      
      if (i === maxRetries - 1) {
        throw error;
      }
      
      // Aguardar antes da próxima tentativa
      await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
    }
  }
};
```

## 🚀 Plano de Ação

### **Imediato (Frontend)**
1. ✅ Corrigir header `dynamodb` → `DynamoDB`
2. ✅ Adicionar timeouts nas requisições
3. ✅ Implementar tratamento específico para erros de CORS
4. ✅ Adicionar interceptors para debug

### **Crítico (Backend - Deploy Necessário)**
1. 🔥 **DEPLOY das correções do backend**
2. 🔥 Remover authorizer do endpoint `/read/id/user`
3. 🔥 Verificar CORS do endpoint `/auth/refresh`

### **Teste após Deploy**
1. Verificar se preflight OPTIONS funciona
2. Testar requisições com `withCredentials: true`
3. Confirmar que não há mais erros de CORS

## 📊 Status Atual

- **Frontend:** ✅ Configuração correta (`withCredentials: true`)
- **Backend:** ❌ **Correções não deployadas**
- **CORS:** ❌ **Preflight falhando por authorizer ativo**
- **Autenticação:** ❌ **Endpoints protegidos não acessíveis**

## 🎯 Resultado Esperado

Após o deploy do backend e correções do frontend:
- ✅ Preflight OPTIONS retorna 200
- ✅ CORS headers corretos (origem específica, não wildcard)
- ✅ Requisições autenticadas funcionando
- ✅ Cookies HttpOnly sendo enviados corretamente

**CRÍTICO:** O principal bloqueador é o deploy das correções do backend!
