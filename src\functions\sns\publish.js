import { parseBody } from "../../shared/parsers";
import { message, json } from "../../shared/response";
import { SNS } from "aws-sdk";

const sns = new SNS({
  region: process.env.AWS_REGION_LOCATION,
});

async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

export const handler = async (event, context) => {
  try {
    const { message, title, topic } = parseBody(event);

    const params = {
      Message: message,
      Subject: title,
      TopicArn: "arn:aws:sns:us-east-1:800844742271:" + topic,
    };

    const publishResponse = await sns.publish(params).promise();
    return sendDataToUser(200, "success", publishResponse);
  } catch (error) {
    return await sendDataToUser(500, "error", error);
  }
};
