/**
 * Script para verificar a estrutura do segredo JWT no AWS Secrets Manager
 */

const AWS = require('aws-sdk');

// Configuração do AWS SDK
const secretsManager = new AWS.SecretsManager({
  region: process.env.AWS_REGION_LOCATION || 'us-east-1'
});

const SECRET_ID = process.env.JWT_DECRIPTION_CREDENTIALS || 'arn:aws:secretsmanager:us-east-1:800844742271:secret:dsm-token-encription-credentials-Fk8enl';

/**
 * Verifica a estrutura do segredo JWT
 */
async function checkJWTSecretStructure() {
  try {
    console.log('🔍 Verificando estrutura do segredo JWT...\n');
    console.log(`📍 Secret ID: ${SECRET_ID}\n`);

    // Tenta recuperar o segredo
    console.log('🚀 Recuperando segredo do AWS Secrets Manager...');
    const result = await secretsManager.getSecretValue({ SecretId: SECRET_ID }).promise();

    let secret;
    if (result.SecretString) {
      secret = JSON.parse(result.SecretString);
    } else if (result.SecretBinary) {
      secret = JSON.parse(Buffer.from(result.SecretBinary, 'base64').toString());
    } else {
      throw new Error('Segredo não contém SecretString nem SecretBinary');
    }

    console.log('✅ Segredo recuperado com sucesso!\n');

    // Mostra a estrutura atual
    console.log('📋 ESTRUTURA ATUAL DO SEGREDO:');
    console.log('=' .repeat(50));
    console.log(JSON.stringify(secret, null, 2));
    console.log('=' .repeat(50));

    // Analisa os campos
    console.log('\n🔍 ANÁLISE DOS CAMPOS:');
    const fields = Object.keys(secret);
    console.log(`   Total de campos: ${fields.length}`);
    console.log(`   Campos disponíveis: ${fields.join(', ')}\n`);

    // Verifica campos esperados
    console.log('✅ CAMPOS ESPERADOS:');
    const expectedFields = [
      { name: 'secret', alternatives: ['jwt_secret', 'jwtSecret', 'key', 'JWT_SECRET'] },
      { name: 'algorithm', alternatives: ['alg', 'JWT_ALGORITHM'] },
      { name: 'issuer', alternatives: ['iss'] },
      { name: 'audience', alternatives: ['aud'] },
      { name: 'expiresIn', alternatives: ['exp'] },
      { name: 'refreshExpiresIn', alternatives: ['refresh_exp'] }
    ];

    expectedFields.forEach(field => {
      const found = secret[field.name];
      const alternative = field.alternatives.find(alt => secret[alt]);
      
      if (found) {
        console.log(`   ✅ ${field.name}: "${found}" (campo padrão)`);
      } else if (alternative) {
        console.log(`   🔄 ${field.name}: "${secret[alternative]}" (via ${alternative})`);
      } else {
        console.log(`   ❌ ${field.name}: NÃO ENCONTRADO (alternativas: ${field.alternatives.join(', ')})`);
      }
    });

    // Sugere correções
    console.log('\n💡 SUGESTÕES DE CORREÇÃO:');
    
    const hasSecret = secret.secret || secret.jwt_secret || secret.jwtSecret || secret.key || secret.JWT_SECRET;
    if (!hasSecret) {
      console.log('   ❌ CRÍTICO: Nenhum campo de secret encontrado!');
      console.log('   💡 Adicione um dos campos: secret, jwt_secret, jwtSecret, key, JWT_SECRET');
    }

    const hasAlgorithm = secret.algorithm || secret.alg || secret.JWT_ALGORITHM;
    if (!hasAlgorithm) {
      console.log('   ⚠️  Algorithm não encontrado, será usado padrão HS256');
      console.log('   💡 Considere adicionar: algorithm, alg, ou JWT_ALGORITHM');
    }

    // Estrutura recomendada
    console.log('\n📝 ESTRUTURA RECOMENDADA:');
    console.log('=' .repeat(50));
    const recommendedStructure = {
      secret: hasSecret || 'SEU_JWT_SECRET_AQUI',
      algorithm: hasAlgorithm || 'HS256',
      issuer: secret.issuer || secret.iss || 'dsm-api',
      audience: secret.audience || secret.aud || 'dsm-clients',
      expiresIn: secret.expiresIn || secret.exp || '8h',
      refreshExpiresIn: secret.refreshExpiresIn || secret.refresh_exp || '7d'
    };
    console.log(JSON.stringify(recommendedStructure, null, 2));
    console.log('=' .repeat(50));

    // Comando para atualizar
    console.log('\n🔧 COMANDO PARA ATUALIZAR O SEGREDO:');
    console.log(`aws secretsmanager update-secret \\`);
    console.log(`  --secret-id "${SECRET_ID}" \\`);
    console.log(`  --secret-string '${JSON.stringify(recommendedStructure)}'`);

    return true;

  } catch (error) {
    console.error('\n❌ ERRO AO VERIFICAR SEGREDO:');
    
    if (error.code === 'AccessDeniedException') {
      console.error('   🔒 Erro de permissão - usuário não tem acesso ao Secrets Manager');
      console.error('   💡 Configure as permissões AWS ou use fallback para variáveis de ambiente');
    } else if (error.code === 'ResourceNotFoundException') {
      console.error('   📍 Segredo não encontrado no AWS Secrets Manager');
      console.error('   💡 Verifique se o SECRET_ID está correto');
    } else {
      console.error(`   💥 Erro: ${error.message}`);
    }

    console.error('\n🔄 USANDO FALLBACK PARA VARIÁVEIS DE AMBIENTE:');
    console.error(`   JWT_SECRET: ${process.env.JWT_SECRET ? 'DEFINIDO' : 'NÃO DEFINIDO'}`);
    console.error(`   JWT_EXPIRES_IN: ${process.env.JWT_EXPIRES_IN || 'PADRÃO (8h)'}`);
    console.error(`   JWT_REFRESH_EXPIRES_IN: ${process.env.JWT_REFRESH_EXPIRES_IN || 'PADRÃO (7d)'}`);

    return false;
  }
}

/**
 * Testa o mapeamento de campos
 */
function testFieldMapping() {
  console.log('\n🧪 TESTANDO MAPEAMENTO DE CAMPOS:');
  
  const testCases = [
    { name: 'Estrutura Padrão', secret: { secret: 'test123', algorithm: 'HS256' } },
    { name: 'Estrutura Alternativa 1', secret: { jwt_secret: 'test123', alg: 'HS256' } },
    { name: 'Estrutura Alternativa 2', secret: { jwtSecret: 'test123', JWT_ALGORITHM: 'HS256' } },
    { name: 'Estrutura Mista', secret: { key: 'test123', issuer: 'test-api' } },
    { name: 'Estrutura Incompleta', secret: { other_field: 'value' } }
  ];

  testCases.forEach(testCase => {
    console.log(`\n   📋 ${testCase.name}:`);
    console.log(`      Input: ${JSON.stringify(testCase.secret)}`);
    
    // Simula o mapeamento
    const mapped = mapFields(testCase.secret);
    console.log(`      Mapped: secret=${!!mapped.secret}, algorithm=${mapped.algorithm}`);
  });
}

function mapFields(secret) {
  let jwtSecret = secret.secret;
  let algorithm = secret.algorithm;
  
  if (!jwtSecret) {
    jwtSecret = secret.jwt_secret || secret.jwtSecret || secret.key || secret.JWT_SECRET;
  }
  
  if (!algorithm) {
    algorithm = secret.alg || secret.JWT_ALGORITHM || 'HS256';
  }
  
  return { secret: jwtSecret, algorithm };
}

/**
 * Executa a verificação
 */
async function main() {
  console.log('🚀 VERIFICADOR DE ESTRUTURA DO SEGREDO JWT\n');
  console.log('=' .repeat(60));

  const success = await checkJWTSecretStructure();
  
  testFieldMapping();

  console.log('\n' + '=' .repeat(60));
  console.log('📊 RESUMO:');
  console.log(`   ✅ Verificação: ${success ? 'SUCESSO' : 'FALHOU'}`);
  console.log(`   🔧 Sistema: ${success ? 'SECRETS MANAGER' : 'FALLBACK ENV'}`);
  
  if (success) {
    console.log('\n🎉 Segredo verificado! Use as sugestões acima para corrigir a estrutura.');
  } else {
    console.log('\n💡 Configure as permissões AWS ou use variáveis de ambiente como fallback.');
  }

  return success;
}

// Executa se chamado diretamente
if (require.main === module) {
  main()
    .then(success => process.exit(success ? 0 : 1))
    .catch(error => {
      console.error('💥 Erro fatal:', error);
      process.exit(1);
    });
}

module.exports = { checkJWTSecretStructure, testFieldMapping };
