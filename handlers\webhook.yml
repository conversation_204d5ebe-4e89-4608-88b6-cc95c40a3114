webhook-send:
  handler: src/functions/webhook/webhookSend.handler
  name: ${env:STAGE}-webhook-send-notification${env:VERSION}
  description: Função para enviar notificação via Teams sobre solicitação de switch role
  memorySize: 128
  events:
    - http:
        path: /send/notification
        method: post
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}


