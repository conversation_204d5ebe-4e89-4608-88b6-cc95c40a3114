/**
 * Script para debugar o fluxo exato da API set-token
 */

// Carrega variáveis de ambiente do .env.dev
require('dotenv').config({ path: '.env.dev' });

const { generateToken, verifyToken } = require('../src/shared/auth/jwt-utils-secrets');
const axios = require('axios');

/**
 * Simula exatamente o fluxo da API set-token
 */
async function debugSetTokenFlow() {
  try {
    console.log('🔍 DEBUGANDO FLUXO DA API SET-TOKEN\n');
    console.log('=' .repeat(60));

    // 1. Gera token como o teste faz
    console.log('1️⃣ Gerando token de teste...');
    const testPayload = {
      sub: 'test-user-123',
      userId: 'test-user-123',
      email: '<EMAIL>',
      role: 'user',
      permissions: ['read', 'write'],
      name: '<PERSON><PERSON><PERSON><PERSON>'
    };

    const token = await generateToken(testPayload);
    console.log(`✅ Token gerado: ${token.substring(0, 50)}...`);

    // 2. Verifica se o token pode ser validado localmente
    console.log('\n2️⃣ Verificando token localmente...');
    const decoded = await verifyToken(token);
    console.log('✅ Token verificado localmente com sucesso!');
    console.log(`   Email: ${decoded.email}`);
    console.log(`   Role: ${decoded.role}`);

    // 3. Envia para a API e captura resposta detalhada
    console.log('\n3️⃣ Enviando para API set-token...');
    
    const apiUrl = 'http://localhost:8000/dev/auth/set-token';
    const requestData = { token };
    const requestConfig = {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:3000'
      },
      validateStatus: () => true // Aceita qualquer status para debug
    };

    console.log(`   URL: ${apiUrl}`);
    console.log(`   Payload: { token: "${token.substring(0, 30)}..." }`);
    console.log(`   Headers: ${JSON.stringify(requestConfig.headers, null, 2)}`);

    const response = await axios.post(apiUrl, requestData, requestConfig);

    console.log('\n4️⃣ Resposta da API:');
    console.log(`   Status: ${response.status}`);
    console.log(`   Status Text: ${response.statusText}`);
    console.log(`   Data: ${JSON.stringify(response.data, null, 2)}`);

    // 4. Analisa a resposta
    console.log('\n5️⃣ Análise da resposta:');
    
    if (response.status === 200) {
      console.log('✅ API respondeu com sucesso');
      console.log(`   Message: ${response.data.message}`);
      console.log(`   User: ${JSON.stringify(response.data.user, null, 2)}`);
      console.log(`   Token Info: ${JSON.stringify(response.data.tokenInfo, null, 2)}`);
      return true;
    } else {
      console.log('❌ API respondeu com erro');
      console.log(`   Erro: ${response.data.message || 'Mensagem não disponível'}`);
      
      // Analisa o tipo de erro
      if (response.data.message && response.data.message.includes('Cognito')) {
        console.log('🔍 PROBLEMA IDENTIFICADO: Token está sendo validado como Cognito!');
        console.log('💡 Isso significa que a validação JWT interna falhou na API.');
      }
      
      return false;
    }

  } catch (error) {
    console.error('\n❌ Erro no debug:');
    
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Data: ${JSON.stringify(error.response.data, null, 2)}`);
    } else if (error.request) {
      console.error('   Erro de rede - servidor não respondeu');
    } else {
      console.error(`   Erro: ${error.message}`);
    }
    
    return false;
  }
}

/**
 * Testa diferentes tipos de token
 */
async function testDifferentTokenTypes() {
  console.log('\n🧪 TESTANDO DIFERENTES TIPOS DE TOKEN\n');
  console.log('=' .repeat(60));

  const testCases = [
    {
      name: 'Token JWT Simples',
      payload: { sub: 'simple', email: '<EMAIL>' }
    },
    {
      name: 'Token JWT Completo',
      payload: {
        sub: 'complete-user',
        email: '<EMAIL>',
        role: 'admin',
        permissions: ['read', 'write', 'delete'],
        name: 'Complete User'
      }
    },
    {
      name: 'Token JWT Mínimo',
      payload: { sub: 'minimal' }
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n📋 Testando: ${testCase.name}`);
    
    try {
      const token = await generateToken(testCase.payload);
      console.log(`   ✅ Token gerado: ${token.substring(0, 30)}...`);
      
      const decoded = await verifyToken(token);
      console.log(`   ✅ Token verificado: ${decoded.sub}`);
      
      // Testa na API
      const response = await axios.post('http://localhost:8000/dev/auth/set-token', 
        { token }, 
        {
          headers: { 'Content-Type': 'application/json', 'Origin': 'http://localhost:3000' },
          validateStatus: () => true
        }
      );
      
      console.log(`   📡 API Response: ${response.status} - ${response.data.message || 'N/A'}`);
      
    } catch (error) {
      console.log(`   ❌ Erro: ${error.message}`);
    }
  }
}

/**
 * Executa debug completo
 */
async function runFullDebug() {
  console.log('🚀 DEBUG COMPLETO DA API SET-TOKEN\n');

  // Verifica se servidor está rodando
  try {
    await axios.get('http://localhost:8000/dev/auth/config', { timeout: 5000 });
    console.log('✅ Servidor está rodando\n');
  } catch (error) {
    console.error('❌ Servidor não está rodando. Execute: yarn dev\n');
    return false;
  }

  // Debug principal
  const mainResult = await debugSetTokenFlow();
  
  // Testes adicionais
  await testDifferentTokenTypes();

  console.log('\n' + '=' .repeat(60));
  console.log('📊 RESUMO DO DEBUG:');
  console.log(`   🎯 Fluxo principal: ${mainResult ? 'SUCESSO' : 'FALHOU'}`);
  console.log(`   🔧 JWT local: FUNCIONANDO`);
  console.log(`   📡 API: ${mainResult ? 'FUNCIONANDO' : 'COM PROBLEMAS'}`);

  if (!mainResult) {
    console.log('\n💡 PRÓXIMOS PASSOS:');
    console.log('   1. Verificar logs do servidor durante a requisição');
    console.log('   2. Verificar se há diferenças na configuração JWT entre geração e verificação');
    console.log('   3. Verificar se a API está usando a mesma configuração do Secrets Manager');
  }

  return mainResult;
}

// Executa se chamado diretamente
if (require.main === module) {
  runFullDebug()
    .then(success => process.exit(success ? 0 : 1))
    .catch(error => {
      console.error('💥 Erro fatal:', error);
      process.exit(1);
    });
}

module.exports = { debugSetTokenFlow, testDifferentTokenTypes, runFullDebug };
