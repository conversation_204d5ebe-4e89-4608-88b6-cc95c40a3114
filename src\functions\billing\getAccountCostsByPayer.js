import { getSecretAccounts } from "./secret";

import aws from "aws-sdk";
import AthenaExpress from "athena-express";

import { parseQueryString, parsePath } from "../../shared/parsers";
import {
  responseWithSuccess,
  responseWithError,
  responseWithBadRequest,
} from "../../shared/response";

function onlyNumber(value) {
  if (value) return value.toString().replace(/\D/gm, "");
  else return "";
}
const defaultCredentials = aws.config.credentials;

export const handler = async (event, context) => {
  try {
    let { year, month } = parseQueryString(event);
    let { payer } = parsePath(event);

    const { isInvoke } = event;
    if (isInvoke) {
      year = event.year;
      month = event.month;
      payer = event.payer;
    }

    if (!payer || !onlyNumber(payer))
      return responseWithBadRequest("invalid payer account");

    if (!year || !onlyNumber(year))
      return responseWithBadRequest("invalid year");

    if (!month || !onlyNumber(month))
      return responseWithBadRequest("invalid month");

    const secret = await getSecretAccounts("************");

    if (!secret)
      return responseWithBadRequest("payer account is not configured");

    aws.config.update({
      credentials: {
        accessKeyId: secret.access_key_id,
        secretAccessKey: secret.secret_key_id,
        sessionToken: secret.session_token,
      },
      region: process.env.AWS_REGION_LOCATION,
    });

    const athena = new AthenaExpress({
      aws,
      db: `default`,
      s3: `s3://prod-customer-billing-result`,
    });

    let whereCoditions = [];
    whereCoditions.push(`AND year='${year}'`);
    whereCoditions.push(`AND month='${month}'`);
    whereCoditions.push(`AND bill_payer_account_id = '${payer}'`);

    let query = await athena.query(`
            SELECT
                line_item_usage_account_id as account,
                ROUND(SUM(COALESCE(line_item_unblended_cost, 0)), 2) AS cost,
                ROUND(SUM(savings_plan_total_commitment_to_date), 2) AS savings_plan,
                line_item_legal_entity as entity,    
                month	
            FROM customer_billing
            WHERE 1=1
                ${whereCoditions.join(" ")}
            GROUP BY
                line_item_usage_account_id,
                month,
                line_item_legal_entity
            HAVING ROUND(SUM(COALESCE(line_item_unblended_cost, 0)), 2)  > 0
            ORDER BY line_item_usage_account_id;
        `);

    const entities = query.Items.reduce((result, account) => {
      const position = result.findIndex((item) => item.name === account.entity);

      if (position > -1) {
        result[position].cost += account.cost;
        result[position].savings_plan += account.savings_plan;
        result[position].accounts.push(account);
        return result;
      }

      result.push({
        name: account.entity,
        cost: account.cost,
        savings_plan: account.savings_plan,
        accounts: [account],
      });

      return result;
    }, []);
    assumeDSMAccount();
    return responseWithSuccess(entities, "Query successfully");
  } catch (error) {
    assumeDSMAccount();
    console.log(error);
    return responseWithError(error);
  }
};

function assumeDSMAccount() {
  aws.config.update({
    credentials: {
      accessKeyId: defaultCredentials.accessKeyId,
      sessionToken: defaultCredentials.sessionToken,
      secretAccessKey: defaultCredentials.secretAccessKey,
    },
    region: aws.config.region,
  });
}
