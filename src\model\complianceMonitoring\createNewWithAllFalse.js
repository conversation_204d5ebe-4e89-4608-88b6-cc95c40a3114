import { getCustomerName } from "../../shared/complianceMonitoring/getCustomerName";
import { putNoKey } from "../dynamo";
import { v4 } from "uuid";

export async function createNewWithAllFalse(obj, customer) {
  console.log({ complianceItems });
  const customerName = await getCustomerName(customer);
  const dynamoPutObj = {
    customerId: customer.id,
    customerName: customerName,
    paAccountId: obj.paAccountId,
    items: complianceItems,
    accountsStatus: {
      status: obj.accountsStatus,
      org: obj.organizations,
      scp: obj.scp,
      denyLeaveOrg: obj.denyLeaveOrg,
    },
  };

  console.log(`Creating customer ${customerName} with all false status...`);
  console.log(JSON.stringify(dynamoPutObj));

  await putNoKey(`${process.env.FINOPS_STAGE}-compliance-monitoring`, {
    ...dynamoPutObj,
    id: v4(),
  });
}

const complianceItems = [
  {
    name: "Trust Relationship da conta root configurado",
    status: false,
  },
  {
    name: "IAM Role de acesso Darede - darede-full (AdministratorAccess)",
    status: false,
  },
  {
    name: "IAM Role de acesso Darede - darede (ReadOnlyAccess)",
    status: false,
  },
  {
    name: "Habilitar conta como Organization",
    status: false,
  },
  {
    name: "Policy SCP habilitadas para todas as contas",
    status: false,
  },
  {
    name: "Policy DenyLeaveOrganization criada e atrelada à conta root",
    status: false,
  },
  {
    name: "Cost Explorer aberto pelo menos 1 vez",
    status: false,
  },
  {
    name: "MFA na conta root habilitado",
    status: false,
  },
  {
    name: "Método de pagamento configurado para invoice",
    status: false,
  },
  {
    name: "CNPJ configurado como 18.342.439/0001-68",
    status: false,
  },
  {
    name: "Herança habilitada nas TaxSettings",
    status: false,
  },
  {
    name: "Tag 'AccountType' com valor 'Critical'",
    status: false,
  },
  {
    name: "Billing configurado",
    status: false,
  },
];
