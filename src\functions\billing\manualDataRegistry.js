import { parseBody } from "../../shared/parsers";
import { message, json } from "../../shared/response";
import { create, put, readAll } from "../../model/dynamo";
import { getSecretAccounts } from "./secret";
import AthenaExpress from "athena-express";
import { v4 } from "uuid";
import { format } from "date-fns";
import { identify } from "./identifyBilling";

async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

//
async function insert(item) {
  return await create(`${process.env.FINOPS_STAGE}-finops`, item);
}

async function update(id, item) {
  const obj = {};
  Object.entries(item).forEach((o) => {
    if (o[0] !== "id" && (o[1] || typeof o[1] === "number")) {
      obj[o[0]] = { Action: "PUT", Value: o[1] };
    }
  });

  return await put(`${process.env.FINOPS_STAGE}-finops`, id, obj);
}

export const handler = async (event, context) => {
  const body = parseBody(event);
  console.log(body);
  const account = body.account;
  const contracts = await readAll(`${process.env.FINOPS_STAGE}-contracts`);
  const customers = await readAll(`${process.env.FINOPS_STAGE}-customers`);
  const finops = await readAll(`${process.env.FINOPS_STAGE}-finops`);

  const secretAccounts = [];
  const success = [];
  const errors = [];

  const aws = require("aws-sdk");

  try {
    const secret = await getSecretAccounts("************");

    if (Object.keys(secret)?.length) {
      console.log(secret);

      aws.config.update({
        credentials: {
          accessKeyId: secret.access_key_id,
          secretAccessKey: secret.secret_key_id,
          sessionToken: secret.session_token,
        },
        region: process.env.AWS_REGION_LOCATION,
      });

      const athena = new AthenaExpress({
        aws,
        db: `default`,
        s3: `s3://prod-customer-billing-result`,
      });

      let query;
      let existBundleDiscount = false;
      let existSppDiscount = false;
      let existEdpDiscount = false;
      let existSavingPlansDiscount = false;
      let existCredit = false;

      try {
        await athena.query(
          `SELECT discount_bundled_discount FROM customer_billing LIMIT 1`
        );
        existBundleDiscount = true;
      } catch (error) {
        existBundleDiscount = false;
        console.log("bundled discount not exists for this customers account");
      }

      try {
        await athena.query(
          `SELECT discount_spp_discount FROM customer_billing LIMIT 1`
        );
        console.log("spp discount exists");
        existSppDiscount = true;
      } catch (error) {
        existSppDiscount = false;
        console.log("spp discount not exists for this customers account");
      }

      try {
        await athena.query(
          `SELECT discount_edp_discount FROM customer_billing LIMIT 1`
        );
        existEdpDiscount = true;
      } catch (error) {
        existEdpDiscount = false;
        console.log("edp discount not exists for this customers account");
      }

      try {
        await athena.query(
          `SELECT savings_plan_total_commitment_to_date FROM customer_billing LIMIT 1`
        );
        existSavingPlansDiscount = true;
      } catch (error) {
        existSavingPlansDiscount = false;
        console.log("saving plans not exists");
      }
      try {
        await athena.query(
          `SELECT line_item_line_item_type FROM customer_billing
          LIMIT 1`
        );
        existCredit = true;
      } catch (error) {
        existCredit = false;
        console.log("credit not exists for this customers account");
      }

      console.log({
        existBundleDiscount,
        existSppDiscount,
        existEdpDiscount,
        existSavingPlansDiscount,
        existCredit,
      });

      let queryColumns = [];
      if (existBundleDiscount)
        queryColumns.push(
          "round(sum(discount_bundled_discount), 2) AS discount_bundled_discount"
        );
      if (existSppDiscount)
        queryColumns.push(
          "round(sum(discount_spp_discount), 2) AS discount_spp_discount"
        );
      if (existEdpDiscount)
        queryColumns.push(
          "round(sum(discount_edp_discount), 2) AS discount_edp_discount"
        );

      try {
        try {
          console.log("Realizando query...");
          query = await athena.query(`
                SELECT line_item_usage_account_id,
                round(sum(line_item_unblended_cost), 2) AS unblended_cost,
                month,
                bill_invoice_id,
                bill_billing_entity,
                bill_payer_account_id,
                line_item_currency_code,
                line_item_legal_entity,
                line_item_line_item_type,
                ${queryColumns.join(", ")},
                round(sum(
                  CASE WHEN line_item_line_item_type = 'Credit'
                    THEN line_item_unblended_cost
                    ELSE 0
                  END
                ), 2) credit,
                round(sum(
                  CASE WHEN line_item_line_item_type = 'SavingsPlanCoveredUsage'
                    THEN line_item_unblended_cost
                    ELSE 0
                  END
                ), 2) savings
                FROM customer_billing
                WHERE year='${body.year}' and month='${
            body.month
          }' and bill_payer_account_id='${account}'
                GROUP BY line_item_usage_account_id, month, bill_invoice_id, bill_billing_entity, bill_payer_account_id, line_item_currency_code, line_item_legal_entity, line_item_line_item_type
                ORDER by 2 DESC;
              `);
        } catch (error) {
          console.log(error);
        }
        let spp_discount = 0;
        let edp_discount = 0;
        let credit = 0;
        let savingPlans = 0;
        let bundled_discount = 0;
        let total_pos = 0;
        let total_neg = 0;
        const subAccounts = [];

        query.Items.forEach((queryData) => {
          savingPlans += queryData.savings || 0;
          bundled_discount += queryData.discount_bundled_discount || 0;
          edp_discount += queryData.discount_edp_discount || 0;
          credit += queryData.credit || 0;
          spp_discount += queryData.discount_spp_discount || 0;
          if (queryData.unblended_cost > 0) {
            total_pos += queryData.unblended_cost;
          } else {
            total_neg += queryData.unblended_cost;
          }

          subAccounts.push({
            invoice_id:
              queryData.bill_invoice_id || query.Items[0]?.bill_invoice_id,
            payer_account_id:
              queryData.bill_payer_account_id ||
              query.Items[0]?.bill_payer_account_id,
            sub_account: queryData.line_item_usage_account_id,
            cost: queryData.unblended_cost,
            spp_discount: queryData.discount_spp_discount || 0,
            edp_discount: queryData.discount_edp_discount || 0,
            bundled_discount: queryData.discount_bundled_discount || 0,
            credit: queryData.credit || 0,
            savingPlans: queryData.savings || 0,
            total_discount:
              Math.abs(queryData.discount_spp_discount || 0) +
              Math.abs(queryData.discount_edp_discount || 0) +
              Math.abs(queryData.discount_bundled_discount || 0) +
              Math.abs(queryData.credit || 0) +
              Math.abs(queryData.savings || 0),
            legal_entity: queryData.line_item_legal_entity,
            bill_entity: queryData.bill_billing_entity,
          });
        });
        console.log(
          "Desconto total",
          Math.abs(spp_discount) +
            Math.abs(edp_discount) +
            Math.abs(bundled_discount) +
            Math.abs(credit) +
            Math.abs(savingPlans)
        );
        const ids = await identify(account, customers, contracts);

        const item = {
          id: v4(),
          identifications: {
            ...ids,
          },
          year: parseInt(body.year),
          month: parseInt(body.month),
          total_pos: total_pos,
          total_neg: total_neg,
          total_spp_discount: spp_discount ? spp_discount : 0,
          total_edp_discount: edp_discount ? edp_discount : 0,
          total_bundled_discount: bundled_discount ? bundled_discount : 0,
          total_credit: credit ? credit : 0,
          total_saving_plans: savingPlans,
          total_discount:
            Math.abs(spp_discount) +
            Math.abs(edp_discount) +
            Math.abs(bundled_discount) +
            Math.abs(credit) +
            Math.abs(savingPlans),
          accounts: subAccounts,
          created_at: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
          updated_at: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
          invoice_id: query.Items[0]?.bill_invoice_id,
          payer_account: query.Items[0]?.bill_payer_account_id,
        };

        const exist = finops.Items.find(
          (e) =>
            e.payer_account == item.payer_account &&
            e.month.toString() == body.month.toString() &&
            e.year.toString() == body.year.toString()
        );

        if (exist) {
          console.log("Invoice já existe.");
          await update(exist.id, {
            accounts: item.accounts,
            total_pos: item.total_pos,
            total_neg: item.total_neg,
            updated_at: item.updated_at,
            invoice_id: item.invoice_id,
            total_bundled_discount: item.total_bundled_discount,
            total_credit: item.total_credit,
            total_edp_discount: item.total_edp_discount,
            total_spp_discount: item.total_spp_discount,
            total_discount: item.total_discount,
            identifications: item.identifications,
            total_saving_plans: item.total_saving_plans,
          });

          secretAccounts.push(exist);
        } else {
          console.log("Invoice será criada.");
          await insert(item);

          secretAccounts.push(item);
        }
      } catch (error) {
        console.log(error);
      }
      success.push(account);
    }
  } catch (error) {
    console.log(error);
    return await sendDataToUser(500, "error", error);
  }

  return await sendDataToUser(200, "success", {
    total_pos: secretAccounts.reduce((acc, cur) => acc + cur.total_pos, 0),
    total_neg: secretAccounts.reduce((acc, cur) => acc + cur.total_neg, 0),
    success,
    errors,
  });
};
