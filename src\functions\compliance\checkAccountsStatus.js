import { parseBody, parseHeaders } from "../../shared/parsers";
import { message, json } from "../../shared/response";
import { verifyRoles } from "../../shared/complianceMonitoring/verifyRoles";
import { verifyOrganizations } from "../../shared/complianceMonitoring/verifyControllers/verifyOrganizations";
import { verifySCP } from "../../shared/complianceMonitoring/verifyControllers/verifySCP";
import { checkCustomerStatus } from "../../shared/complianceMonitoring/checkCustomerStatus";
import { verifyDenyLeaveOrganization } from "../../shared/complianceMonitoring/verifyControllers/verifyDenyLeaveOrganization";
import { accessKeysCustomerAccount } from "../../shared/accessKeysCustomerAccount";

import { readAll, readOne } from "../../model/dynamo";
import { createNewComplianceItemDynamo } from "../../model/complianceMonitoring/createNewComplianceItemDynamo";
import { updateExistingComplianceItemDynamo } from "../../model/complianceMonitoring/updateExistingComplianceItemDynamo";
import { createNewWithAllFalse } from "../../model/complianceMonitoring/createNewWithAllFalse";
import { updateExistingWithAllFalseItems } from "../../model/complianceMonitoring/updateExistingWithAllFalse";
import { getCustomerName } from "../../shared/complianceMonitoring/getCustomerName";

let results = [];

async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

// export const main = async (customerId) => {
// -------------- uncomment this to run it locally

export const handler = async (event, context) => {
  // -------------- comment this to run it locally

  const body = parseBody(event); // coment this to run locally
  console.log({ body });
  const customerId = body.customerId;
  console.log({ customerId });

  if (customerId) {
    console.log({ customerId });
    let customer = await readOne(
      `${process.env.FINOPS_STAGE}-customers`,
      customerId
    );
    customer = customer.Item;

    let dynamoItems = await readAll(
      `${process.env.FINOPS_STAGE}-compliance-monitoring`
    );

    dynamoItems = dynamoItems.Items;

    const customerName = await getCustomerName(customer);
    let paAccountId = "";
    let accounts = customer.accounts;
    let accountsStatus = [];
    let statusObj = {
      customerId: customerId,
      customerName: customerName,
      trust: false,
      organizations: false,
      scp: false,
      denyLeaveOrg: false,
    };

    if (accounts && accounts.length > 0) {
      console.log(
        `\n\tCustomer: ${customerName} has ${accounts.length} accounts`
      );

      for (let z = 0; z < accounts.length; z++) {
        let org = false;
        let denyLeaveOrg = false;
        let scp = false;
        let trust = false;

        if (accounts[z]?.payer) {
          console.log("Payer Account Id: ", accounts[z].account_id);
          paAccountId = accounts[z].account_id;

          const newSession = await accessKeysCustomerAccount(paAccountId);

          if (newSession) {
            trust = true;
            org = await verifyOrganizations(accounts[z].account_id, accounts);

            if (org) {
              scp = await verifySCP(accounts[z].account_id);
              denyLeaveOrg = await verifyDenyLeaveOrganization(
                accounts[z].account_id
              );
            }

            statusObj = {
              ...statusObj,
              trust: trust,
              organizations: org,
              scp: scp,
              denyLeaveOrg: denyLeaveOrg,
            };

            console.log({ trust });

            const rolesResponse = await verifyRoles(accounts[z].account_id);
            accountsStatus.push(rolesResponse);
          }

          break; // so it doesn't iterate over unecessary accounts
        }
      }
    }
    results.push(statusObj);

    statusObj = { ...statusObj, paAccountId: paAccountId, accountsStatus };

    const items = await checkCustomerStatus(statusObj);

    console.log({ items });

    let complianceItem = dynamoItems.find((d) => d.customerId === customerId);

    if (complianceItem) {
      if (!statusObj.trust) {
        await updateExistingWithAllFalseItems(statusObj, complianceItem);
      } else {
        await updateExistingComplianceItemDynamo(
          statusObj,
          complianceItem,
          items
        );
      }
    } else {
      if (!statusObj.trust) {
        await createNewWithAllFalse(statusObj, customer);
      } else {
        await createNewComplianceItemDynamo(statusObj, customer);
      }
    }
  }

  return await sendDataToUser(200, "success");
};
