/**
 * Script de migração para Secrets Manager
 * Ajuda na transição das funções existentes para usar AWS Secrets Manager
 */

const fs = require('fs').promises;
const path = require('path');

/**
 * Mapeamento de imports antigos para novos
 */
const IMPORT_MAPPINGS = {
  "require('../../shared/auth/jwt-utils-cjs')": "require('../../shared/auth/jwt-utils-secrets')",
  "require('../shared/auth/jwt-utils-cjs')": "require('../shared/auth/jwt-utils-secrets')",
  "require('./shared/auth/jwt-utils-cjs')": "require('./shared/auth/jwt-utils-secrets')",
  "require('../../shared/api/api-client-cjs')": "require('../../shared/api/api-client-secrets')",
  "require('../shared/api/api-client-cjs')": "require('../shared/api/api-client-secrets')",
  "require('./shared/api/api-client-cjs')": "require('./shared/api/api-client-secrets')"
};

/**
 * Comentários para adicionar aos arquivos migrados
 */
const MIGRATION_COMMENTS = {
  jwt: '// Migrado para usar AWS Secrets Manager para credenciais JWT',
  api: '// Migrado para usar AWS Secrets Manager para credenciais de API',
  auth: '// Migrado para usar middleware de autenticação com Secrets Manager'
};

/**
 * Lista arquivos JavaScript recursivamente
 * @param {string} dir - Diretório para buscar
 * @returns {Promise<Array>} Lista de arquivos .js
 */
async function listJSFiles(dir) {
  const files = [];
  
  try {
    const entries = await fs.readdir(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      
      if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
        const subFiles = await listJSFiles(fullPath);
        files.push(...subFiles);
      } else if (entry.isFile() && entry.name.endsWith('.js')) {
        files.push(fullPath);
      }
    }
  } catch (error) {
    console.warn(`Aviso: Não foi possível ler diretório ${dir}: ${error.message}`);
  }
  
  return files;
}

/**
 * Analisa um arquivo para verificar se precisa de migração
 * @param {string} filePath - Caminho do arquivo
 * @returns {Promise<Object>} Análise do arquivo
 */
async function analyzeFile(filePath) {
  try {
    const content = await fs.readFile(filePath, 'utf8');
    
    const analysis = {
      path: filePath,
      needsMigration: false,
      hasJWTUtils: false,
      hasAPIClient: false,
      hasAuthMiddleware: false,
      imports: [],
      issues: []
    };
    
    for (const [oldImport, newImport] of Object.entries(IMPORT_MAPPINGS)) {
      if (content.includes(oldImport)) {
        analysis.needsMigration = true;
        analysis.imports.push({ old: oldImport, new: newImport });
        
        if (oldImport.includes('jwt-utils')) {
          analysis.hasJWTUtils = true;
        }
        if (oldImport.includes('api-client')) {
          analysis.hasAPIClient = true;
        }
      }
    }
    
    // Verifica uso de middleware de autenticação antigo
    if (content.includes('withAuth') && !content.includes('withSecureAuth')) {
      analysis.needsMigration = true;
      analysis.hasAuthMiddleware = true;
      analysis.issues.push('Usa middleware de autenticação antigo');
    }
    
    // Verifica uso direto de JWT_SECRET
    if (content.includes('process.env.JWT_SECRET') && !content.includes('getJWTCredentials')) {
      analysis.needsMigration = true;
      analysis.issues.push('Usa JWT_SECRET diretamente das variáveis de ambiente');
    }
    
    // Verifica uso direto de credenciais de API
    if (content.includes('process.env.DSM_API_') && !content.includes('getAPICredentials')) {
      analysis.needsMigration = true;
      analysis.issues.push('Usa credenciais de API diretamente das variáveis de ambiente');
    }
    
    return analysis;
    
  } catch (error) {
    return {
      path: filePath,
      needsMigration: false,
      error: error.message
    };
  }
}

/**
 * Migra um arquivo para usar Secrets Manager
 * @param {Object} analysis - Análise do arquivo
 * @param {boolean} dryRun - Se true, apenas simula a migração
 * @returns {Promise<Object>} Resultado da migração
 */
async function migrateFile(analysis, dryRun = false) {
  try {
    const content = await fs.readFile(analysis.path, 'utf8');
    let newContent = content;
    const changes = [];
    
    // Substitui imports
    for (const importChange of analysis.imports) {
      newContent = newContent.replace(importChange.old, importChange.new);
      changes.push(`Import: ${importChange.old} → ${importChange.new}`);
    }
    
    // Adiciona comentários de migração
    if (analysis.hasJWTUtils) {
      newContent = MIGRATION_COMMENTS.jwt + '\n' + newContent;
      changes.push('Adicionado comentário sobre migração JWT');
    }
    
    if (analysis.hasAPIClient) {
      newContent = MIGRATION_COMMENTS.api + '\n' + newContent;
      changes.push('Adicionado comentário sobre migração API');
    }
    
    if (analysis.hasAuthMiddleware) {
      newContent = MIGRATION_COMMENTS.auth + '\n' + newContent;
      changes.push('Adicionado comentário sobre migração Auth');
    }
    
    // Salva arquivo se não for dry run
    if (!dryRun && changes.length > 0) {
      await fs.writeFile(analysis.path, newContent, 'utf8');
    }
    
    return {
      path: analysis.path,
      success: true,
      changes,
      dryRun
    };
    
  } catch (error) {
    return {
      path: analysis.path,
      success: false,
      error: error.message
    };
  }
}

/**
 * Cria backup dos arquivos antes da migração
 * @param {Array} filePaths - Lista de arquivos para backup
 * @returns {Promise<string>} Diretório de backup criado
 */
async function createBackup(filePaths) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = path.join(process.cwd(), `backup-${timestamp}`);
  
  await fs.mkdir(backupDir, { recursive: true });
  
  for (const filePath of filePaths) {
    const relativePath = path.relative(process.cwd(), filePath);
    const backupPath = path.join(backupDir, relativePath);
    const backupDirPath = path.dirname(backupPath);
    
    await fs.mkdir(backupDirPath, { recursive: true });
    await fs.copyFile(filePath, backupPath);
  }
  
  console.log(`✅ Backup criado em: ${backupDir}`);
  return backupDir;
}

/**
 * Executa migração completa
 * @param {Object} options - Opções de migração
 */
async function executeMigration(options = {}) {
  const {
    sourceDir = './src',
    dryRun = false,
    createBackupFiles = true,
    verbose = false
  } = options;
  
  console.log('🚀 Iniciando migração para Secrets Manager\n');
  console.log(`📁 Diretório fonte: ${sourceDir}`);
  console.log(`🔍 Modo: ${dryRun ? 'Simulação (dry-run)' : 'Migração real'}`);
  console.log(`💾 Backup: ${createBackupFiles ? 'Sim' : 'Não'}\n`);
  
  try {
    // Lista todos os arquivos JS
    console.log('📋 Listando arquivos JavaScript...');
    const jsFiles = await listJSFiles(sourceDir);
    console.log(`   Encontrados ${jsFiles.length} arquivos\n`);
    
    // Analisa cada arquivo
    console.log('🔍 Analisando arquivos...');
    const analyses = [];
    
    for (const file of jsFiles) {
      const analysis = await analyzeFile(file);
      analyses.push(analysis);
      
      if (verbose && analysis.needsMigration) {
        console.log(`   📄 ${analysis.path}:`);
        if (analysis.imports.length > 0) {
          console.log(`      Imports: ${analysis.imports.length}`);
        }
        if (analysis.issues.length > 0) {
          console.log(`      Issues: ${analysis.issues.join(', ')}`);
        }
      }
    }
    
    const filesToMigrate = analyses.filter(a => a.needsMigration && !a.error);
    const filesWithErrors = analyses.filter(a => a.error);
    
    console.log(`   ✅ ${filesToMigrate.length} arquivos precisam de migração`);
    console.log(`   ⚠️ ${filesWithErrors.length} arquivos com erros de análise\n`);
    
    if (filesWithErrors.length > 0 && verbose) {
      console.log('❌ Arquivos com erros:');
      filesWithErrors.forEach(f => console.log(`   ${f.path}: ${f.error}`));
      console.log();
    }
    
    if (filesToMigrate.length === 0) {
      console.log('✅ Nenhum arquivo precisa de migração!');
      return;
    }
    
    // Cria backup se solicitado
    if (createBackupFiles && !dryRun) {
      await createBackup(filesToMigrate.map(f => f.path));
    }
    
    // Executa migração
    console.log(`🔄 ${dryRun ? 'Simulando' : 'Executando'} migração...`);
    const results = [];
    
    for (const analysis of filesToMigrate) {
      const result = await migrateFile(analysis, dryRun);
      results.push(result);
      
      if (verbose) {
        const status = result.success ? '✅' : '❌';
        console.log(`   ${status} ${result.path}`);
        if (result.changes && result.changes.length > 0) {
          result.changes.forEach(change => console.log(`      - ${change}`));
        }
        if (result.error) {
          console.log(`      Erro: ${result.error}`);
        }
      }
    }
    
    // Resumo final
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    console.log('\n📊 RESUMO DA MIGRAÇÃO');
    console.log(`   ✅ Sucessos: ${successful}`);
    console.log(`   ❌ Falhas: ${failed}`);
    console.log(`   📁 Total analisado: ${jsFiles.length} arquivos`);
    
    if (dryRun) {
      console.log('\n💡 Esta foi uma simulação. Execute novamente com --no-dry-run para aplicar as mudanças.');
    } else {
      console.log('\n🎉 Migração concluída!');
      console.log('\n📋 Próximos passos:');
      console.log('   1. Teste as funções migradas');
      console.log('   2. Execute: node scripts/test-secrets-manager.js');
      console.log('   3. Verifique os logs para confirmar uso do Secrets Manager');
      console.log('   4. Atualize variáveis de ambiente se necessário');
    }
    
  } catch (error) {
    console.error('💥 Erro durante a migração:', error);
    throw error;
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {
    dryRun: !args.includes('--no-dry-run'),
    verbose: args.includes('--verbose'),
    createBackupFiles: !args.includes('--no-backup'),
    sourceDir: args.find(arg => arg.startsWith('--dir='))?.split('=')[1] || './src'
  };
  
  executeMigration(options)
    .then(() => process.exit(0))
    .catch(error => {
      console.error('Erro fatal:', error);
      process.exit(1);
    });
}

module.exports = {
  analyzeFile,
  migrateFile,
  executeMigration,
  listJSFiles,
  createBackup
};
