const slsw = require('serverless-webpack');
const nodeExternals = require('webpack-node-externals');
const WebpackBar = require('webpackbar');

const isLocal = slsw.lib.webpack.isLocal;
const isWindows = process.platform === 'win32';
const disableWebpackBar = process.env.DISABLE_WEBPACK_BAR === 'true';

module.exports = {
  entry: slsw.lib.entries,
  target: 'node',
  mode: isLocal ? 'development' : 'production',
  optimization: {
    minimize: !isLocal
  },
  devtool: isLocal ? 'eval-cheap-module-source-map' : 'source-map',
  externals: [nodeExternals({
    allowlist: isLocal ? [] : undefined
  })],
  cache: isLocal && !isWindows ? {
    type: 'filesystem'
  } : false,
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: [
              [
                '@babel/preset-env',
                {
                  targets: { node: '18' },
                  useBuiltIns: 'usage',
                  corejs: 3
                }
              ]
            ]
          }
        }
      }
    ]
  },
  plugins: disableWebpackBar ? [] : [
    new WebpackBar({
      name: 'DSM Backend',
      color: '#00d4aa',
      reporters: ['basic'],
      profile: false
    })
  ],
  infrastructureLogging: {
    level: 'error'
  },
  stats: 'errors-warnings'
};