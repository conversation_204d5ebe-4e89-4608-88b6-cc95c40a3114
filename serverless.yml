service: ${self:custom.dotenv.STAGE}-${self:custom.dotenv.API_NAME}${self:custom.dotenv.VERSION}
variablesResolutionMode: 20210326
 
plugins:
  - serverless-webpack
  - serverless-offline
  - serverless-plugin-split-stacks
  - serverless-apigw-binary
 
provider:
  name: aws
  runtime: nodejs18.x
  stage: ${env:STAGE}
  region: ${env:AWS_REGION_LOCATION}
  timeout: 30 
  memorySize: 1024
  versionFunctions: false
  role: !GetAtt LambdaExecutionRole.Arn
 
  apiGateway:
    restApiId: !Ref ApiGatewayRestApi
    restApiRootResourceId: !GetAtt ApiGatewayRestApi.RootResourceId
 
  environment: ${file(env.${opt:stage,'dev'}.js)}
 
package:
  individually: true
  excludeDevDependencies: true
  patterns:
    - "!.git/**"
    - "!.vscode/**"
    - "!.test/**"
    - "!.env.dev"
    - "!.env.prod"
    - "!.env.hml"
 
layers:
  awsCore:
    path: layers/awsCoreLayer
    compatibleRuntimes:
      - nodejs18.x
  utils:
    path: layers/utilsLayer
    compatibleRuntimes:
      - nodejs18.x
  documentGeneration:
    path: layers/documentGenerationLayer
    compatibleRuntimes:
      - nodejs18.x
custom:
  dotenv: ${file(env.${opt:stage,'dev'}.js)}
  apigwBinary:
    types:
      - "image/jpeg"
      - "text/html"
      - "multipart/form-data"

  authorizer:
    name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
    arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

  webpack:
    packager: "npm"
  splitStacks:
    perFunction: true
    perType: true
  serverless-offline:
    httpPort: ${self:custom.dotenv.LOCAL_PORT, 8000}
    host: localhost
    stage: ${self:custom.dotenv.STAGE, 'local'}
    prefix: ''
    noAuth: false
    noTimeout: true
    resourceRoutes: true
    printOutput: true
    reloadHandler: true



functions: ${file(serverless-dynamic.js)}


resources:

  Resources:
    ApiGatewayRestApi:
      Type: AWS::ApiGateway::RestApi
      Properties:
        Name: ${self:service}
        Description: API Gateway para ${self:service}
        EndpointConfiguration:
          Types:
            - REGIONAL
        Policy:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal: '*'
              Action: 'execute-api:Invoke'
              Resource: '*'

    LambdaExecutionRole:
      Type: AWS::IAM::Role
      Properties:
        RoleName: ${self:custom.dotenv.STAGE}-dsm-back-end${self:custom.dotenv.VERSION}-${self:provider.region}-lambdaRole
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service: lambda.amazonaws.com
              Action: sts:AssumeRole
        ManagedPolicyArns:
          - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
          - arn:aws:iam::${env:ACCOUNT_ID}:policy/cross-account-jump-access
        Policies:
          - PolicyName: ${env:STAGE}-dsm-back-end-policy
            PolicyDocument:
              Version: '2012-10-17'
              Statement:
                - Effect: Allow
                  Action:
                    - lambda:*
                    - dynamodb:*
                    - cognito-idp:*
                    - secretsmanager:*
                    - s3:*
                    - textract:*
                    - sns:*
                    - sts:*
                    - states:*
                    - athena:*
                    - glue:*
                  Resource: "*"