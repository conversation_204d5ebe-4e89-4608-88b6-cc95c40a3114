import AthenaExpress from "athena-express";

import { parseBody, parseQueryString } from "../../shared/parsers";
import { message, json } from "../../shared/response";
import { getSecretAccounts } from "../billing/secret";
import { create } from "../../model/dynamo";
import { InvoiceEntity } from "../../entities/invoice-entity";
import { makeDynamoDB } from "../../shared/services/dynamo-service";

const AWS = require("aws-sdk");
const defaultCredentials = AWS.config.credentials;

async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

export const handler = async (event, context) => {
  const clientDynamo = makeDynamoDB();
  const invoicesEntity = new InvoiceEntity(clientDynamo);
  let body;
  try {
    const parsedBody = parseBody(event);
    if (parsedBody.month && parsedBody.year) body = parseBody(event);
    else throw new Error("Scheduled Event");
  } catch (error) {
    const queryParams = parseQueryString(event);
    let year = new Date().getFullYear();
    let month = new Date().getMonth();
    if (queryParams["last-month"]) {
      month -= 1;
    }
    if (month <= 0) {
      month += 12;
      year -= 1;
    }
    body = {
      year: year,
      month: month,
    };
  }
  console.log(body);
  const checkInvoicesInput = invoicesEntity.formatInvoicesParameters(
    `${body.year}-${body.month}`
  );
  const { Items } = await invoicesEntity.getInvoices(checkInvoicesInput);

  try {
    const secret = await getSecretAccounts("************");

    if (Object.keys(secret)?.length) {
      AWS.config.update({
        credentials: {
          accessKeyId: secret.access_key_id,
          secretAccessKey: secret.secret_key_id,
          sessionToken: secret.session_token,
        },
        region: process.env.AWS_REGION_LOCATION,
      });

      const athena = new AthenaExpress({
        aws: AWS,
        db: `default`,
        s3: `s3://prod-customer-billing-result`,
      });

      let query;
      let existBundleDiscount = true;
      let existSppDiscount = true;
      let existEdpDiscount = true;

      let queryColumns = [];
      if (existBundleDiscount)
        queryColumns.push(
          "round(sum(discount_bundled_discount), 2) AS discount_bundled_discount"
        );
      if (existSppDiscount)
        queryColumns.push(
          "round(sum(discount_spp_discount), 2) AS discount_spp_discount"
        );
      if (existEdpDiscount)
        queryColumns.push(
          "round(sum(discount_edp_discount), 2) AS discount_edp_discount"
        );

      try {
        console.log("Realizando repair table");
        athena.query("MSCK REPAIR TABLE customer_billing");

        try {
          console.log("Realizando query...");
          query = await athena.query(`
                SELECT line_item_usage_account_id,
                round(sum(line_item_unblended_cost), 2) AS unblended_cost,
                month,
                bill_invoice_id,
                bill_billing_entity,
                bill_payer_account_id,
                line_item_currency_code,
                line_item_legal_entity,
                line_item_line_item_type,
                ${queryColumns.join(", ")},
                round(sum(
                  CASE WHEN line_item_line_item_type = 'Credit'
                    THEN line_item_unblended_cost
                    ELSE 0
                  END
                ), 2) credit,
                round(sum(
                  CASE WHEN line_item_line_item_type = 'SavingsPlanCoveredUsage'
                    THEN line_item_unblended_cost
                    ELSE 0
                  END
                ), 2) savings,
                round(sum(
                  CASE WHEN line_item_line_item_type = 'Tax'
                    THEN line_item_unblended_cost
                    ELSE 0
                  END
                ), 2) tax
                FROM customer_billing
                WHERE year='${body.year}' and month='${body.month}'
                GROUP BY line_item_usage_account_id, month, bill_invoice_id, bill_billing_entity, bill_payer_account_id, line_item_currency_code, line_item_legal_entity, line_item_line_item_type
                ORDER by 2 DESC;
              `);
        } catch (error) {
          console.log(error);
        }

        const invoices = invoicesEntity.formatInvoices(query, body);
        assumeDSMAccount();
        const tableName = `${process.env.FINOPS_STAGE}-finops`;
        const tasks = [];

        for (let i = 0; i < invoices.length; i++) {
          if (invoices[i].invoice_id && invoices[i].invoice_id !== "") {
            if (Items.length) {
              const existInvoice = Items.find(
                (item) =>
                  item.invoice_id === invoices[i].invoice_id &&
                  item.payer_account === invoices[i].payer_account
              );
              if (!existInvoice) {
                console.log(
                  "Creating invoice...",
                  invoices[i].invoice_id,
                  body
                );
                tasks.push(create(tableName, invoices[i]));
              } else if (
                existInvoice.accounts.length !== invoices[i].accounts.length
              ) {
                console.log(
                  "Updating invoice...",
                  invoices[i].invoice_id,
                  body
                );
                tasks.push(
                  invoicesEntity.update(existInvoice.invoice_id, invoices[i])
                );
              } else {
                console.log(
                  "Invoice already exist",
                  invoices[i].invoice_id,
                  body
                );
              }
            } else {
              tasks.push(create(tableName, invoices[i]));
            }
          } else {
            console.log("Without invoice_id", invoices[i].payer_account);
          }
        }
        try {
          await Promise.all(tasks);
        } catch (error) {
          console.error("Error processing invoices:", error);
        }
        return await sendDataToUser(200, "success", invoices);
      } catch (error) {
        assumeDSMAccount();
        console.log(error);
        return await sendDataToUser(500, "error", error);
      }
    }
  } catch (error) {
    assumeDSMAccount();
    console.log(error);
    return await sendDataToUser(500, "error", error);
  }
};

function assumeDSMAccount() {
  AWS.config.update({
    credentials: {
      accessKeyId: defaultCredentials.accessKeyId,
      sessionToken: defaultCredentials.sessionToken,
      secretAccessKey: defaultCredentials.secretAccessKey,
    },
    region: AWS.config.region,
  });
}
