#!/usr/bin/env node

/**
 * Script para testar CORS em produção
 * Testa os endpoints que estão falhando nos logs
 */

const axios = require('axios');

const PRODUCTION_BASE_URL = 'https://pdu9dbemj1.execute-api.us-east-1.amazonaws.com/dev';
const ORIGIN = 'https://dev.dsm.darede.com.br';

// Endpoints que estão falhando
const FAILING_ENDPOINTS = [
  { path: '/auth/set-token', method: 'POST' },
  { path: '/cognito/read', method: 'GET' },
  { path: '/mfa', method: 'POST' }
];

/**
 * Testa requisição preflight OPTIONS
 */
async function testPreflightRequest(endpoint) {
  try {
    console.log(`\n🔍 Testando preflight: ${endpoint.method} ${endpoint.path}`);
    
    const config = {
      method: 'options',
      url: `${PRODUCTION_BASE_URL}${endpoint.path}`,
      headers: {
        'Origin': ORIGIN,
        'Access-Control-Request-Method': endpoint.method,
        'Access-Control-Request-Headers': 'Content-Type, Authorization, Cookie'
      },
      validateStatus: () => true
    };

    const response = await axios(config);
    
    console.log(`   Status: ${response.status}`);
    console.log(`   Access-Control-Allow-Origin: ${response.headers['access-control-allow-origin'] || 'AUSENTE'}`);
    console.log(`   Access-Control-Allow-Credentials: ${response.headers['access-control-allow-credentials'] || 'AUSENTE'}`);
    console.log(`   Access-Control-Allow-Methods: ${response.headers['access-control-allow-methods'] || 'AUSENTE'}`);
    
    const isWorking = response.status === 200 && 
                     response.headers['access-control-allow-origin'] === ORIGIN &&
                     response.headers['access-control-allow-credentials'] === 'true';
    
    console.log(`   Resultado: ${isWorking ? '✅ FUNCIONANDO' : '❌ PROBLEMA'}`);
    
    return isWorking;
    
  } catch (error) {
    console.log(`   ❌ ERRO: ${error.message}`);
    return false;
  }
}

/**
 * Testa requisição real
 */
async function testRealRequest(endpoint) {
  try {
    console.log(`\n🔍 Testando requisição real: ${endpoint.method} ${endpoint.path}`);
    
    const config = {
      method: endpoint.method.toLowerCase(),
      url: `${PRODUCTION_BASE_URL}${endpoint.path}`,
      headers: {
        'Origin': ORIGIN,
        'Content-Type': 'application/json'
      },
      validateStatus: () => true
    };
    
    // Adiciona body para POST
    if (endpoint.method === 'POST') {
      config.data = { test: 'data' };
    }

    const response = await axios(config);
    
    console.log(`   Status: ${response.status}`);
    console.log(`   Access-Control-Allow-Origin: ${response.headers['access-control-allow-origin'] || 'AUSENTE'}`);
    
    // Considera sucesso se não for erro de CORS (mesmo que seja 401, 403, etc.)
    const isNotCorsError = response.status !== 0 && 
                          response.headers['access-control-allow-origin'];
    
    console.log(`   Resultado: ${isNotCorsError ? '✅ CORS OK' : '❌ CORS PROBLEMA'}`);
    
    return isNotCorsError;
    
  } catch (error) {
    console.log(`   ❌ ERRO: ${error.message}`);
    return false;
  }
}

/**
 * Função principal
 */
async function main() {
  console.log('🚀 Testando CORS em Produção');
  console.log('============================');
  console.log(`Base URL: ${PRODUCTION_BASE_URL}`);
  console.log(`Origin: ${ORIGIN}\n`);
  
  let allWorking = true;
  
  for (const endpoint of FAILING_ENDPOINTS) {
    console.log(`\n📍 ENDPOINT: ${endpoint.method} ${endpoint.path}`);
    console.log('='.repeat(50));
    
    // Testa preflight
    const preflightWorking = await testPreflightRequest(endpoint);
    
    // Testa requisição real
    const realWorking = await testRealRequest(endpoint);
    
    const endpointWorking = preflightWorking && realWorking;
    console.log(`\n📊 RESUMO: ${endpointWorking ? '✅ FUNCIONANDO' : '❌ PROBLEMA'}`);
    
    if (!endpointWorking) {
      allWorking = false;
    }
  }
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 RESULTADO FINAL');
  console.log('='.repeat(60));
  
  if (allWorking) {
    console.log('✅ Todos os endpoints estão funcionando!');
    console.log('✅ CORS configurado corretamente em produção');
  } else {
    console.log('❌ Alguns endpoints ainda têm problemas');
    console.log('❌ Deploy pode ser necessário para aplicar correções');
  }
  
  console.log('\n🔧 PRÓXIMOS PASSOS:');
  if (!allWorking) {
    console.log('1. Fazer deploy das correções: npm run deploy');
    console.log('2. Aguardar propagação (2-5 minutos)');
    console.log('3. Executar este teste novamente');
  } else {
    console.log('1. Testar no frontend');
    console.log('2. Verificar logs de produção');
    console.log('3. Monitorar funcionamento');
  }
}

// Executa apenas se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testPreflightRequest,
  testRealRequest
};
