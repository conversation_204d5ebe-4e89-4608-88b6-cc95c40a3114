get-contracts:
  handler: src/functions/contracts/get-contracts.handler
  name: ${env:STAGE}-get-contracts${env:VERSION}
  description: Função para buscar os clientes do DSM
  memorySize: 128
  timeout: 30
  events:
    - http:
        path: /contracts
        method: get
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

get-reasons-to-deactivate-contract:
  handler: src/functions/contracts/get-reasons-to-deactivate-contract.handler
  name: ${env:STAGE}-get-reasons-to-deactivate-contract${env:VERSION}
  description: Função para fazer a listagem de opções dos motivos para desativar um contrato.
  memorySize: 128
  events:
    - http:
        path: /contracts/reasons-to-deactivate
        method: get
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

create-contract:
  handler: src/functions/contracts/create-contract.handler
  name: ${env:STAGE}-create-contract${env:VERSION}
  description: Função para realizar a criação de um contrato.
  memorySize: 128
  events:
    - http:
        path: /contracts/create
        method: post
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}


