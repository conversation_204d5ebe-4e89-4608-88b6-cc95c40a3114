#!/usr/bin/env node

/**
 * Script para testar o parsing do secret JWT
 */

console.log('🧪 TESTE DE PARSING DO SECRET JWT');
console.log('=================================\n');

/**
 * Simula o parsing do secret em formato texto
 */
function parseSecretText(secretText) {
  console.log('📄 Secret original:');
  console.log(secretText);
  console.log('');
  
  const credentials = {};
  
  // Converte formato "KEY=VALUE" para objeto
  secretText.split('\n').forEach(line => {
    const [key, value] = line.split('=');
    if (key && value) {
      credentials[key.trim()] = value.trim();
    }
  });
  
  return credentials;
}

/**
 * Testa o parsing
 */
function testParsing() {
  const mockSecretText = `JWT_SECRET=871c4c458f16a9807c5a53027a62bda3e4c01e06ab755aa243f5b0d4f447e5d9d35e24f9d8ac976c4bc1ce58291fd0796d39c93dce8343a46890542b8a29f3be
JWT_EXPIRES_IN=8h
JWT_REFRESH_EXPIRES_IN=7d`;

  console.log('🔍 Testando parsing do secret...\n');
  
  const parsed = parseSecretText(mockSecretText);
  
  console.log('✅ Resultado do parsing:');
  console.log(JSON.stringify(parsed, null, 2));
  console.log('');
  
  // Verifica se os campos esperados estão presentes
  const requiredFields = ['JWT_SECRET', 'JWT_EXPIRES_IN', 'JWT_REFRESH_EXPIRES_IN'];
  const missingFields = requiredFields.filter(field => !parsed[field]);
  
  if (missingFields.length === 0) {
    console.log('🎉 ✅ TODOS OS CAMPOS OBRIGATÓRIOS ENCONTRADOS!');
    console.log(`✅ JWT_SECRET: ${parsed.JWT_SECRET.substring(0, 20)}...`);
    console.log(`✅ JWT_EXPIRES_IN: ${parsed.JWT_EXPIRES_IN}`);
    console.log(`✅ JWT_REFRESH_EXPIRES_IN: ${parsed.JWT_REFRESH_EXPIRES_IN}`);
  } else {
    console.log('❌ Campos obrigatórios ausentes:', missingFields);
  }
  
  return parsed;
}

/**
 * Testa a geração de token com as credenciais
 */
function testTokenGeneration(credentials) {
  console.log('\n🔐 Testando geração de token...\n');
  
  try {
    const jwt = require('jsonwebtoken');
    
    const payload = {
      sub: 'test-user',
      email: '<EMAIL>',
      role: 'user'
    };
    
    const token = jwt.sign(payload, credentials.JWT_SECRET, {
      expiresIn: credentials.JWT_EXPIRES_IN,
      issuer: 'dsm-backend',
      audience: 'dsm-frontend'
    });
    
    console.log('✅ Token gerado com sucesso!');
    console.log(`Token: ${token.substring(0, 50)}...`);
    
    // Verifica se o token pode ser decodificado
    const decoded = jwt.decode(token);
    console.log('✅ Token decodificado:', {
      sub: decoded.sub,
      email: decoded.email,
      role: decoded.role,
      exp: new Date(decoded.exp * 1000).toISOString()
    });
    
    return true;
    
  } catch (error) {
    console.error('❌ Erro ao gerar token:', error.message);
    return false;
  }
}

/**
 * Função principal
 */
function main() {
  const credentials = testParsing();
  const tokenSuccess = testTokenGeneration(credentials);
  
  console.log('\n📊 RELATÓRIO FINAL');
  console.log('==================\n');
  
  if (tokenSuccess) {
    console.log('🎉 ✅ PARSING E GERAÇÃO DE TOKEN FUNCIONANDO!');
    console.log('✅ A correção do secret parsing está correta');
    console.log('✅ Pronto para testar em ambiente DEV');
  } else {
    console.log('❌ Problemas encontrados no teste');
    console.log('🔧 Verifique a implementação do parsing');
  }
}

// Executa apenas se chamado diretamente
if (require.main === module) {
  main();
}
