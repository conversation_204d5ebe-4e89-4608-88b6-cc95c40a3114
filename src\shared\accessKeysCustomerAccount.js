import { STS } from "aws-sdk";

export const accessKeysCustomerAccount = async (account) => {
  const region = process.env.AWS_REGION_LOCATION;

  let objPart = {};

  try {
    const stsDSMAccount = new STS({ region });
    const credentialsRootAccount = await stsDSMAccount
      .assumeRole({
        RoleArn: "arn:aws:iam::************:role/jump-access-roles",
        RoleSessionName: "darede-************",
      })
      .promise();

    const stsRootAccount = new STS({
      region,
      credentials: {
        accessKeyId: credentialsRootAccount.Credentials.AccessKeyId,
        secretAccessKey: credentialsRootAccount.Credentials.SecretAccessKey,
        sessionToken: credentialsRootAccount.Credentials.SessionToken,
      },
    });

    const credentialsCustomerAccount = await stsRootAccount
      .assumeRole({
        RoleArn: "arn:aws:iam::" + account + ":role/darede-full",
        RoleSessionName: "darede-" + account,
      })
      .promise();

    objPart.access_key_id = credentialsCustomerAccount.Credentials.AccessKeyId;
    objPart.secret_key_id =
      credentialsCustomerAccount.Credentials.SecretAccessKey;
    objPart.session_token = credentialsCustomerAccount.Credentials.SessionToken;

    return objPart;
  } catch (error) {
    console.log(
      `Error assuming role darede-full on customer account = ${account}`,
      {
        error,
      }
    );
    return null;
  }
};
