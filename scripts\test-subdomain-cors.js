#!/usr/bin/env node

/**
 * Script para testar configuração CORS com subdomínios
 * Testa se o backend aceita requisições de subdomínios de dsm.darede.com.br
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:8000/dev';

// Origens para testar
const TEST_ORIGINS = [
  'https://dsm.darede.com.br',
  'https://dev.dsm.darede.com.br',
  'https://staging.dsm.darede.com.br',
  'https://test.dsm.darede.com.br',
  'https://admin.dsm.darede.com.br',
  'https://api.dsm.darede.com.br',
  'http://localhost:3000',
  'http://localhost:8080',
  'https://malicious.com', // Deve ser rejeitada
  'https://dsm.darede.com.br.evil.com' // Deve ser rejeitada
];

// Endpoints para testar
const TEST_ENDPOINTS = [
  { path: '/auth/config', method: 'GET' },
  { path: '/auth/check-cookie-support', method: 'GET' },
  { path: '/cognito/read', method: 'GET' }
];

/**
 * Faz uma requisição com origem específica
 */
async function makeRequestWithOrigin(method, path, origin) {
  try {
    const config = {
      method: method.toLowerCase(),
      url: `${BASE_URL}${path}`,
      headers: {
        'Origin': origin,
        'Content-Type': 'application/json'
      },
      validateStatus: () => true // Não rejeitar por status HTTP
    };

    const response = await axios(config);
    
    return {
      status: response.status,
      headers: response.headers,
      data: response.data
    };
  } catch (error) {
    return {
      status: 0,
      error: error.message,
      headers: {},
      data: null
    };
  }
}

/**
 * Testa requisição preflight OPTIONS
 */
async function testPreflightRequest(origin, endpoint) {
  try {
    const config = {
      method: 'options',
      url: `${BASE_URL}${endpoint.path}`,
      headers: {
        'Origin': origin,
        'Access-Control-Request-Method': endpoint.method,
        'Access-Control-Request-Headers': 'Content-Type, Authorization, Cookie'
      },
      validateStatus: () => true
    };

    const response = await axios(config);
    
    return {
      status: response.status,
      headers: response.headers,
      corsHeaders: {
        allowOrigin: response.headers['access-control-allow-origin'],
        allowCredentials: response.headers['access-control-allow-credentials'],
        allowMethods: response.headers['access-control-allow-methods'],
        allowHeaders: response.headers['access-control-allow-headers']
      }
    };
  } catch (error) {
    return {
      status: 0,
      error: error.message,
      headers: {},
      corsHeaders: {}
    };
  }
}

/**
 * Verifica se uma origem deve ser permitida
 */
function shouldBeAllowed(origin) {
  // Localhost sempre permitido em desenvolvimento
  if (origin.includes('localhost')) return true;
  
  // dsm.darede.com.br e seus subdomínios
  if (origin === 'https://dsm.darede.com.br') return true;
  if (origin.includes('.dsm.darede.com.br')) return true;
  
  return false;
}

/**
 * Testa CORS para uma origem específica
 */
async function testOriginCors(origin) {
  console.log(`\n🔍 Testando origem: ${origin}`);
  const shouldAllow = shouldBeAllowed(origin);
  console.log(`   Esperado: ${shouldAllow ? '✅ PERMITIDO' : '❌ REJEITADO'}`);
  
  let allPassed = true;
  
  for (const endpoint of TEST_ENDPOINTS) {
    console.log(`\n   📍 Endpoint: ${endpoint.method} ${endpoint.path}`);
    
    // Testa preflight
    const preflight = await testPreflightRequest(origin, endpoint);
    console.log(`      Preflight: ${preflight.status}`);
    
    if (shouldAllow) {
      if (preflight.corsHeaders.allowOrigin === origin) {
        console.log(`      ✅ Access-Control-Allow-Origin: ${preflight.corsHeaders.allowOrigin}`);
      } else {
        console.log(`      ❌ Access-Control-Allow-Origin: ${preflight.corsHeaders.allowOrigin || 'AUSENTE'}`);
        allPassed = false;
      }
      
      if (preflight.corsHeaders.allowCredentials === 'true') {
        console.log(`      ✅ Access-Control-Allow-Credentials: true`);
      } else {
        console.log(`      ❌ Access-Control-Allow-Credentials: ${preflight.corsHeaders.allowCredentials || 'AUSENTE'}`);
        allPassed = false;
      }
    } else {
      if (!preflight.corsHeaders.allowOrigin || preflight.status === 403) {
        console.log(`      ✅ Origem rejeitada corretamente`);
      } else {
        console.log(`      ❌ Origem deveria ser rejeitada mas foi permitida`);
        allPassed = false;
      }
    }
    
    // Testa requisição real
    const actual = await makeRequestWithOrigin(endpoint.method, endpoint.path, origin);
    console.log(`      Requisição real: ${actual.status}`);
  }
  
  return allPassed;
}

/**
 * Função principal
 */
async function main() {
  console.log('🚀 Testando configuração CORS com subdomínios');
  console.log('================================================');
  
  let allTestsPassed = true;
  
  for (const origin of TEST_ORIGINS) {
    const passed = await testOriginCors(origin);
    if (!passed) {
      allTestsPassed = false;
    }
  }
  
  console.log('\n📊 RESUMO DOS TESTES');
  console.log('===================');
  
  if (allTestsPassed) {
    console.log('✅ Todos os testes passaram!');
    console.log('✅ Configuração CORS está funcionando corretamente');
  } else {
    console.log('❌ Alguns testes falharam');
    console.log('❌ Verifique a configuração CORS');
  }
  
  console.log('\n🔧 CONFIGURAÇÃO ATUAL:');
  console.log('- Subdomínios de dsm.darede.com.br: PERMITIDOS');
  console.log('- Localhost (desenvolvimento): PERMITIDOS');
  console.log('- Outras origens: REJEITADAS');
}

// Executa apenas se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  makeRequestWithOrigin,
  testPreflightRequest,
  testOriginCors,
  shouldBeAllowed
};
