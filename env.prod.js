const env = require('dotenv').config({ path: './.env.prod' })
module.exports = env.parsed || {
  STAGE: 'prod',
  API_NAME: 'dsm-back-end',
  VERSION: 'v1',
  AWS_REGION_LOCATION: 'us-east-1',
  ACCOUNT_ID: '************',
  JWT_DECRIPTION_CREDENTIALS: 'arn:aws:secretsmanager:us-east-1:************:secret:dsm-token-encription-credentials-Fk8enl',
  DSM_API_SECREAT_MANAGER: 'arn:aws:secretsmanager:us-east-1:************:secret:dsm-api-credentials-XXXXX',
  DSM_API_WAF_ARN: 'arn:aws:secretsmanager:us-east-1:************:secret:dsm-waf-token-XXXXX',
  USER_POOL_ID: 'us-east-1',
  AWS_API_GATEWAY_COGNITO_NAME: 'dsm-cognito-authorizer'
}
