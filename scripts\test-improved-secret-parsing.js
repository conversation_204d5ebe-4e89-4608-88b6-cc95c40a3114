#!/usr/bin/env node

/**
 * Script para testar o parsing melhorado do secret JWT
 */

const { parse: parseEnv } = require('dotenv');

console.log('🔧 TESTE DE PARSING MELHORADO DO SECRET JWT');
console.log('==========================================\n');

/**
 * Testa parsing com dotenv.parse()
 */
function testDotenvParsing() {
  console.log('🧪 Teste 1: Parsing com dotenv.parse()\n');
  
  const mockSecretText = `JWT_SECRET=871c4c458f16a9807c5a53027a62bda3e4c01e06ab755aa243f5b0d4f447e5d9d35e24f9d8ac976c4bc1ce58291fd0796d39c93dce8343a46890542b8a29f3be
JWT_EXPIRES_IN=8h
JWT_REFRESH_EXPIRES_IN=7d`;

  console.log('📄 Secret original:');
  console.log(mockSecretText);
  console.log('');
  
  try {
    const credentials = parseEnv(mockSecretText);
    
    console.log('✅ Parsing com dotenv.parse() bem-sucedido:');
    console.log(JSON.stringify(credentials, null, 2));
    
    return credentials;
    
  } catch (error) {
    console.error('❌ Erro no parsing com dotenv:', error.message);
    return null;
  }
}

/**
 * Testa parsing com casos edge
 */
function testEdgeCases() {
  console.log('\n🧪 Teste 2: Casos edge\n');
  
  const edgeCaseSecret = `# Comentário
JWT_SECRET=test-secret-key
# Outro comentário

JWT_EXPIRES_IN=8h
INVALID_LINE_WITHOUT_EQUALS
JWT_REFRESH_EXPIRES_IN=7d
EMPTY_VALUE=
=EMPTY_KEY`;

  console.log('📄 Secret com casos edge:');
  console.log(edgeCaseSecret);
  console.log('');
  
  try {
    const credentials = parseEnv(edgeCaseSecret);
    
    console.log('✅ Parsing de casos edge:');
    console.log(JSON.stringify(credentials, null, 2));
    
    // Verifica se comentários foram ignorados
    const hasComments = Object.keys(credentials).some(key => key.startsWith('#'));
    console.log(`📝 Comentários ignorados: ${!hasComments ? '✅' : '❌'}`);
    
    return credentials;
    
  } catch (error) {
    console.error('❌ Erro no parsing de casos edge:', error.message);
    return null;
  }
}

/**
 * Testa validação de campos obrigatórios
 */
function testFieldValidation(credentials) {
  console.log('\n🧪 Teste 3: Validação de campos obrigatórios\n');
  
  const requiredFields = ['JWT_SECRET', 'JWT_EXPIRES_IN', 'JWT_REFRESH_EXPIRES_IN'];
  const missingFields = requiredFields.filter(field => !credentials[field]);
  
  console.log('🔍 Campos obrigatórios:', requiredFields);
  console.log('📋 Campos encontrados:', Object.keys(credentials));
  console.log('❌ Campos ausentes:', missingFields);
  
  if (missingFields.length === 0) {
    console.log('✅ Todos os campos obrigatórios presentes!');
    return true;
  } else {
    console.log('❌ Campos obrigatórios ausentes:', missingFields.join(', '));
    return false;
  }
}

/**
 * Testa fallback para parsing manual
 */
function testManualFallback() {
  console.log('\n🧪 Teste 4: Fallback para parsing manual\n');
  
  // Secret com formato que pode causar problemas no dotenv
  const problematicSecret = `JWT_SECRET=key-with-special-chars!@#$%^&*()
JWT_EXPIRES_IN=8h
JWT_REFRESH_EXPIRES_IN=7d`;

  console.log('📄 Secret problemático:');
  console.log(problematicSecret);
  console.log('');
  
  // Simula fallback manual
  function manualParsing(secretText) {
    const credentials = {};
    secretText.split('\n').forEach(line => {
      const trimmedLine = line.trim();
      if (!trimmedLine || trimmedLine.startsWith('#')) return;
      
      const equalIndex = trimmedLine.indexOf('=');
      if (equalIndex === -1) return;
      
      const key = trimmedLine.substring(0, equalIndex).trim();
      const value = trimmedLine.substring(equalIndex + 1).trim();
      
      if (key && value) {
        credentials[key] = value;
      }
    });
    
    return credentials;
  }
  
  try {
    const credentials = manualParsing(problematicSecret);
    
    console.log('✅ Fallback manual funcionando:');
    console.log(JSON.stringify(credentials, null, 2));
    
    return credentials;
    
  } catch (error) {
    console.error('❌ Erro no fallback manual:', error.message);
    return null;
  }
}

/**
 * Compara performance entre métodos
 */
function testPerformance() {
  console.log('\n🧪 Teste 5: Comparação de performance\n');
  
  const testSecret = `JWT_SECRET=871c4c458f16a9807c5a53027a62bda3e4c01e06ab755aa243f5b0d4f447e5d9d35e24f9d8ac976c4bc1ce58291fd0796d39c93dce8343a46890542b8a29f3be
JWT_EXPIRES_IN=8h
JWT_REFRESH_EXPIRES_IN=7d`;

  const iterations = 1000;
  
  // Teste dotenv.parse()
  console.time('dotenv.parse()');
  for (let i = 0; i < iterations; i++) {
    parseEnv(testSecret);
  }
  console.timeEnd('dotenv.parse()');
  
  // Teste parsing manual
  console.time('parsing manual');
  for (let i = 0; i < iterations; i++) {
    const credentials = {};
    testSecret.split('\n').forEach(line => {
      const [key, value] = line.split('=');
      if (key && value) {
        credentials[key.trim()] = value.trim();
      }
    });
  }
  console.timeEnd('parsing manual');
}

/**
 * Função principal
 */
function main() {
  const dotenvResult = testDotenvParsing();
  const edgeCaseResult = testEdgeCases();
  const validationResult = testFieldValidation(dotenvResult || {});
  const fallbackResult = testManualFallback();
  
  testPerformance();
  
  console.log('\n📊 RELATÓRIO FINAL');
  console.log('==================\n');
  
  console.log(`✅ Parsing dotenv: ${dotenvResult ? 'SUCESSO' : 'FALHA'}`);
  console.log(`✅ Casos edge: ${edgeCaseResult ? 'SUCESSO' : 'FALHA'}`);
  console.log(`✅ Validação: ${validationResult ? 'SUCESSO' : 'FALHA'}`);
  console.log(`✅ Fallback manual: ${fallbackResult ? 'SUCESSO' : 'FALHA'}`);
  
  const allTestsPassed = dotenvResult && edgeCaseResult && validationResult && fallbackResult;
  
  if (allTestsPassed) {
    console.log('\n🎉 ✅ PARSING MELHORADO FUNCIONANDO PERFEITAMENTE!');
    console.log('✅ dotenv.parse() mais robusto que parsing manual');
    console.log('✅ Validação de campos obrigatórios implementada');
    console.log('✅ Fallback manual como backup');
    console.log('✅ Tratamento de casos edge (comentários, linhas vazias)');
  } else {
    console.log('\n⚠️ Alguns testes falharam');
    console.log('🔧 Verifique a implementação');
  }
  
  console.log('\n💡 RECOMENDAÇÃO: A melhoria com dotenv.parse() é válida!');
}

// Executa apenas se chamado diretamente
if (require.main === module) {
  main();
}
