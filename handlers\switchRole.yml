invoke-state-machine:
  handler: src/functions/switchRole/invokeStateMachine.handler
  name: ${env:STAGE}-invoke-state-machine${env:VERSION}
  description: Função para chamar a Step Function que contém a State Machine para troca de role
  memorySize: 128
  events:
    - http:
        path: /state-machine
        method: post
        cors:
          origin: "*"
          headers:
            - Content-Type
            - X-Amz-Date
            - Authorization
            - X-Api-Key
            - X-Amz-Security-Token
            - x-waf-header
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

check-darede-full-role:
  handler: src/functions/switchRole/checkDaredeFullRole.handler
  name: ${env:STAGE}-check-darede-full-role
  description: Função para criar role na conta do cliente
  memorySize: 128
  timeout: 900
  events:
    - http:
        path: /check-darede-full-role
        method: post
        cors:
          origin: "*"
          headers:
            - Content-Type
            - X-Amz-Date
            - Authorization
            - X-Api-Key
            - X-Amz-Security-Token
            - x-waf-header
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

read-switch-role-data:
  handler: src/functions/switchRole/readSwitchRoleData.handler
  name: ${env:STAGE}-read-switch-role-data-v2${env:VERSION}
  description: Função para leitura de dados de switch role da tabela do DynamoDB
  memorySize: 128
  events:
    - http:
        path: /read/switch-role
        method: get
        cors:
          origin: "*"
          headers:
            - DynamoDB
            - Content-Type
            - X-Amz-Date
            - Authorization
            - X-Api-Key
            - X-Amz-Security-Token
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

check-solicitation-existence:
  handler: src/functions/switchRole/checkSolicitationExistence.handler
  name: ${env:STAGE}-check-solicitation-existence${env:VERSION}
  description: Função para checar se existe uma solicitação de switch role para o usuário e conta informados
  memorySize: 128
  events:
    - http:
        path: /read/switch-role-exists/{user}/{account}
        method: get
        cors:
          origin: "*"
          headers:
            - DynamoDB
            - Content-Type
            - X-Amz-Date
            - Authorization
            - X-Api-Key
            - X-Amz-Security-Token
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

check-if-role-admin-exists:
  handler: src/functions/switchRole/checkIfRoleAdminExists.handler
  name: ${env:STAGE}-check-if-role-admin-exists
  description: Função para checar e retentar criar switch-role em caso de falha
  memorySize: 128
  timeout: 300
  events:
    - schedule:
        rate: cron(0 3 * * ? *)
        enabled: true
    - http:
        path: /check-if-role-admin-exists
        method: get
        cors:
          origin: "*"
          headers:
            - Content-Type
            - X-Amz-Date
            - Authorization
            - X-Api-Key
            - X-Amz-Security-Token
            - x-waf-header
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

