import { putNo<PERSON><PERSON> } from "../dynamo";

export async function updateExistingWithAllFalseItems(obj, complianceItem) {
  console.log({ complianceItems });
  const dynamoPutObj = {
    ...complianceItem,
    items: complianceItems,
    customerName: obj.customerName,
    paAccountId: obj.paAccountId,
    accountsStatus: {
      status: obj.accountsStatus,
      org: obj.organizations,
      scp: obj.scp,
      denyLeaveOrg: obj.denyLeaveOrg,
    },
  };

  console.log(`Updating customer ${obj.customerName} with all false status...`);
  console.log(JSON.stringify(dynamoPutObj));

  await putNo<PERSON><PERSON>(
    `${process.env.FINOPS_STAGE}-compliance-monitoring`,
    dynamoPutObj
  );
}

const complianceItems = [
  {
    name: "Trust Relationship da conta root configurado",
    status: false,
  },
  {
    name: "IAM Role de acesso Darede - darede-full (AdministratorAccess)",
    status: false,
  },
  {
    name: "IAM Role de acesso Darede - darede (ReadOnlyAccess)",
    status: false,
  },
  {
    name: "Habilitar conta como Organization",
    status: false,
  },
  {
    name: "Policy SCP habilitadas para todas as contas",
    status: false,
  },
  {
    name: "Policy DenyLeaveOrganization criada e atrelada à conta root",
    status: false,
  },
  {
    name: "Cost Explorer aberto pelo menos 1 vez",
    status: false,
  },
  {
    name: "MFA na conta root habilitado",
    status: false,
  },
  {
    name: "Método de pagamento configurado para invoice",
    status: false,
  },
  {
    name: "CNPJ configurado como 18.342.439/0001-68",
    status: false,
  },
  {
    name: "Herança habilitada nas TaxSettings",
    status: false,
  },
  {
    name: "Tag 'AccountType' com valor 'Critical'",
    status: false,
  },
  {
    name: "Billing configurado",
    status: false,
  },
];
