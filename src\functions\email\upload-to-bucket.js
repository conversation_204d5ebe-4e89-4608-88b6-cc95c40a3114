import { makeS3 } from "../../shared/services/s3-service";
import { errorValidator } from "../../shared/validators";

import {
  STATUS_CODE,
  responseWithError,
  responseWithSuccess,
  responseWithBadRequest,
} from "../../shared/response";

const s3 = makeS3();

const BUCKET_NAME = process.env.EMAIL_AUTOMATION_BUCKET_NAME;
exports.handler = async (event) => {
  try {
    if (event.Records.length > 0) {
      const { body } = event.Records[0];
      const { data, fileName, contentType } = JSON.parse(body);

      let params = {
        Bucket: BUCKET_NAME,
        Body: data,
        Key: fileName,
      };

      if (contentType.includes("image")) {
        params.ContentEncoding = "base64";
        params.Body = Buffer.from(
          params.Body.replace(/^data:image\/\w+;base64,/, ""),
          "base64"
        );
      }

      const responseS3 = await s3.upload(params).promise();

      return responseWithSuccess(responseS3, "Upload realizado com sucesso");
    }

    return responseWithBadRequest(
      "Não foi possivel realizar o upload do arquivo"
    );
  } catch (error) {
    const { statusCode, message } = errorValidator(error);

    if (STATUS_CODE.BAD_REQUEST === statusCode)
      return responseWithBadRequest(message);

    return responseWithError(message);
  }
};
