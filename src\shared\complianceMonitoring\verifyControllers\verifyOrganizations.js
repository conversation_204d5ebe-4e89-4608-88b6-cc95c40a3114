import { accessKeysCustomerAccount } from "../../accessKeysCustomerAccount";

const AWS = require("aws-sdk");

const region = "us-east-1";

export async function verifyOrganizations(account, customerAccounts) {
  console.log("\nVerifying organization...");
  const newSession = await accessKeysCustomerAccount(account);

  if (newSession) {
    let organizations = new AWS.Organizations({
      region,
      credentials: {
        accessKeyId: newSession.access_key_id,
        secretAccessKey: newSession.secret_key_id,
        sessionToken: newSession.session_token,
      },
      MaxResults: 3,
    });

    let res = await organizations
      .describeAccount({ AccountId: account })
      .promise()
      .catch(() => {
        return false;
      });

    console.log("Describe Org Account: ", { res });

    if (!res) {
      return false;
    }

    return true;
  }
  return false;
}
