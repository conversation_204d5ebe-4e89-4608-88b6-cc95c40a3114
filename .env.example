# AWS Configuration
AWS_REGION_LOCATION=us-east-1
ACCOUNT_ID=************

# API Configuration
API_NAME=dsm-back-end
VERSION=v1
STAGE=dev
LOCAL_PORT=3001

# AWS API Gateway
AWS_API_GATEWAY_REST_ID=
AWS_API_GATEWAY_REST_ROOT_RESOURCE_ID=
LAYER_BUCKET=

# AWS Secrets Manager ARNs - CRITICAL FOR SECURITY
JWT_DECRIPTION_CREDENTIALS=arn:aws:secretsmanager:us-east-1:************:secret:dsm-token-encription-credentials-Fk8enl
DSM_API_SECREAT_MANAGER=arn:aws:secretsmanager:us-east-1:************:secret:dsm-api-credentials-XXXXX
DSM_API_WAF_ARN=arn:aws:secretsmanager:us-east-1:************:secret:dsm-waf-token-XXXXX
AWS_SECRET_GOOGLE_AUTH_TOKEN=arn:aws:secretsmanager:us-east-1:************:secret:google-auth-token-XXXXX
AWS_SECRET_VALUE_ACCOUNTS=arn:aws:secretsmanager:us-east-1:************:secret:aws-accounts-XXXXX
AWS_SECRET_CREDENTIALS_OTRS_API=arn:aws:secretsmanager:us-east-1:************:secret:otrs-api-credentials-XXXXX

# Cognito Configuration
USER_POOL_ID=us-east-1_XXXXXXXXX
AWS_API_GATEWAY_COGNITO_NAME=dsm-cognito-authorizer

# External APIs - Environment Specific
# Production
DSM_API_URL_PROD=https://api.dsm.com.br
# Homologation
DSM_API_URL_HML=https://api.hml.dsm.com.br
# Development
DSM_API_URL_DEV=https://api.dev.dsm.com.br

# Other APIs
CRM_API_BASE_URL=https://crm.api.example.com
API_OTRS_BASE_URL=https://otrs.api.example.com

# IAM Roles
ARN_JUMP_ACCESS=arn:aws:iam::************:role/jump-access-roles
ROOT_ACCOUNT_ID=************
ROLE_NAME=darede-full

# Contract Management
BUCKET_REASONS_TO_DEACTIVATE_CONTRACT=
TABLE_REASONS_TO_DEACTIVATE_CONTRACT=

# JIRA Configuration (if used)
URL_JIRA=https://dosystems.atlassian.net/rest/api/3
USER_JIRA=<EMAIL>
PASS_JIRA=your-jira-token