import { putNo<PERSON><PERSON> } from "../dynamo";
import { complianceItems } from "../../shared/complianceMonitoring/complianceItems";

export async function updateExistingComplianceItemDynamo(
  obj,
  complianceItem,
  items
) {
  let dynamoPutObj = {};
  if (items === false) {
    dynamoPutObj = {
      ...complianceItem,
      customerName: obj.customerName,
      paAccountId: obj.paAccountId,
      accountsStatus: {
        status: obj.accountsStatus,
        org: obj.organizations,
        scp: obj.scp,
        denyLeaveOrg: obj.denyLeaveOrg,
      },
    };
  } else {
    dynamoPutObj = {
      ...complianceItem,
      items: items,
      customerName: obj.customerName,
      paAccountId: obj.paAccountId,
      accountsStatus: {
        status: obj.accountsStatus,
        org: obj.organizations,
        scp: obj.scp,
        denyLeaveOrg: obj.denyLeaveOrg,
      },
    };
  }

  console.log(
    "Updating existing compliance-monitoring obj...",
    dynamoPutObj.id,
    JSON.stringify(dynamoPutObj)
  );

  let keys = Object.keys(dynamoPutObj);
  let values = Object.values(dynamoPutObj);

  let finalObjToDynamo = {};

  keys.forEach((k, i) => {
    if (k !== "id") {
      const newKey = {
        [k]: {
          Action: "PUT",
          Value: values[i],
        },
      };
      finalObjToDynamo = { ...finalObjToDynamo, ...newKey };
    }
  });

  await putNoKey(
    `${process.env.FINOPS_STAGE}-compliance-monitoring`,
    dynamoPutObj
  );
}
