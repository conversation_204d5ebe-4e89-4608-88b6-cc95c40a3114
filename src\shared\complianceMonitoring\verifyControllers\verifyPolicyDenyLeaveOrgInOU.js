export async function verifyPolicyDenyLeaveOrgInOU(org, targetId, targetName) {
  const allRootAccounts = await org
    .listAccountsForParent({
      ParentId: targetId,
    })
    .promise();

  console.log("allRootAccounts:\n", JSON.stringify(allRootAccounts));

  const policies = await org
    .listPoliciesForTarget({
      Filter: "SERVICE_CONTROL_POLICY",
      TargetId: targetId,
    })
    .promise()
    .catch((err) => {
      console.log({ err });
      return false;
    });

  if (policies.Policies.length === 0) {
    console.log(
      `There is no DenyLeaveOrganization policy attached to this target: Id - ${targetId} / Name - ${targetName}`
    );
    return false;
  }

  const policy = policies?.Policies?.find(
    (p) => p.Name.toLowerCase() === "denyleaveorganization"
  );

  console.log({ policy });

  if (!policy) {
    console.log(
      `There is no DenyLeaveOrganization policy attached to this target: Id - ${targetId} / Name - ${targetName}`
    );
    return false;
  }

  const targets = await org
    .listTargetsForPolicy({
      PolicyId: policy?.Id,
    })
    .promise();

  console.log("targets: \n", JSON.stringify(targets));

  console.log("Accounts: ", allRootAccounts?.Accounts?.length);
  console.log("Targets: ", targets?.Targets?.length);

  if (allRootAccounts?.Accounts?.length === targets?.Targets?.length) {
    return true;
  } else {
    return false;
  }
}
