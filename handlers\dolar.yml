create-dolar:
  handler: src/functions/dolar/create.handler
  name: ${self:custom.dotenv.STAGE}-create-dolar${self:custom.dotenv.VERSION}
  description: Função para registrar o valor do dolar no DynamoDB
  memorySize: 128
  events:
    - http:
        path: /create/dolar
        method: post
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}

read-dolar:
  handler: src/functions/dolar/read.handler
  name: ${self:custom.dotenv.STAGE}-read-dolar${self:custom.dotenv.VERSION}
  description: Função para leitura do dolar da tabela do DynamoDB
  memorySize: 128
  events:
    - http:
        path: /read/dolar/{month}/{year}
        method: get
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}


