import { parseEvent } from "../../shared/parsers";
import { SNS } from "aws-sdk";
import { json, message } from "../../shared/response";

const sns = new SNS({
  region: process.env.AWS_REGION_LOCATION,
});

async function sendMessageToSNS(text, requester, requestTime, title) {
  const messageBody = `**${requester}** 
*${requestTime}*
${text}`;

  try {
    const message = {
      version: "1.0",
      source: "custom",
      content: {
        textType: "client-markdown",
        description: messageBody,
        title: title,
      },
    };
    const params = {
      Message: JSON.stringify(message),
      Subject: title,
      TopicArn: process.env.SWITCH_ROLE_NOTIFICATION_SNS_ARN,
    };

    const publishResponse = await sns.publish(params).promise();
    return publishResponse;
  } catch (error) {
    console.log(error);
    return error;
  }
}

async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

export const handler = async (event) => {
  let message = parseEvent(event).body;
  message = JSON.parse(message).body;
  console.log("event: ", message);
  const { title, sections } = message;
  const { text, activityTitle, activitySubtitle } = sections[0];
  console.log("event: ", title, text, activityTitle, activitySubtitle);

  try {
    const response = await sendMessageToSNS(
      text,
      activityTitle,
      activitySubtitle,
      title
    );
    return await sendDataToUser(200, "success", response);
  } catch (error) {
    console.log(error);
    const statusCode = error?.response?.status;
    return await sendDataToUser(statusCode, "error", error);
  }
};