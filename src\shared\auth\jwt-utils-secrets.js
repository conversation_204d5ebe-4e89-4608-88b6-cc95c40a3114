/**
 * Utilitários para JWT usando AWS Secrets Manager
 * Versão refatorada com gerenciamento seguro de credenciais
 */

const jwt = require('jsonwebtoken');
const { getJWTCredentials } = require('../secrets/secrets-manager');

let jwtCredentialsCache = null;
let cacheExpiry = 0;

/**
 * Obtém credenciais JWT do Secrets Manager com cache
 * @returns {Promise<Object>} Credenciais JWT
 */
async function getJWTConfig() {
  const now = Date.now();
  
  // Verifica se o cache ainda é válido (5 minutos)
  if (jwtCredentialsCache && now < cacheExpiry) {
    return jwtCredentialsCache;
  }

  try {
    // console.log('Carregando credenciais JWT do Secrets Manager...');
    jwtCredentialsCache = await getJWTCredentials();
    cacheExpiry = now + (5 * 60 * 1000); // Cache por 5 minutos
    // console.log('Credenciais JWT carregadas com sucesso');
    return jwtCredentialsCache;
  } catch (error) {
    console.error('Erro ao carregar credenciais JWT do Secrets Manager:', error.message);

    if (process.env.JWT_SECRET) {
      console.warn('🔄 FALLBACK: Usando JWT_SECRET das variáveis de ambiente (Secrets Manager indisponível)');
      const fallbackConfig = {
        secret: process.env.JWT_SECRET,
        algorithm: 'HS256',
        issuer: 'dsm-api',
        audience: 'dsm-clients',
        expiresIn: process.env.JWT_EXPIRES_IN || '8h',
        refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
        _fallback: true,
        _fallbackReason: error.code || 'SECRETS_MANAGER_ERROR'
      };

      jwtCredentialsCache = fallbackConfig;
      cacheExpiry = now + (1 * 60 * 1000);

      return fallbackConfig;
    }

    throw new Error('Não foi possível obter credenciais JWT do Secrets Manager nem das variáveis de ambiente');
  }
}

/**
 * Gera um token JWT usando credenciais do Secrets Manager
 * @param {Object} payload - Dados do usuário para incluir no token
 * @param {string} expiresIn - Tempo de expiração (opcional)
 * @returns {Promise<string>} Token JWT
 */
async function generateToken(payload, expiresIn = null) {
  try {
    const config = await getJWTConfig();
    
    const tokenPayload = {
      ...payload,
      iat: Math.floor(Date.now() / 1000),
      jti: generateJTI() 
    };

    const options = {
      expiresIn: expiresIn || config.expiresIn,
      issuer: config.issuer,
      audience: config.audience,
      algorithm: config.algorithm
    };

    const token = jwt.sign(tokenPayload, config.secret, options);

    const source = config._fallback ? 'ENV_FALLBACK' : 'SECRETS_MANAGER';
    // console.log(`✅ Token JWT gerado para usuário: ${payload.email || payload.sub || 'N/A'} (${source})`);
    return token;
    
  } catch (error) {
    console.error('Erro ao gerar token:', error);
    throw new Error('Falha ao gerar token de autenticação');
  }
}

/**
 * Gera um refresh token usando credenciais do Secrets Manager
 * @param {Object} payload - Dados do usuário para incluir no token
 * @returns {Promise<string>} Refresh token JWT
 */
async function generateRefreshToken(payload) {
  try {
    const config = await getJWTConfig();
    
    const refreshPayload = {
      sub: payload.sub,
      userId: payload.userId,
      email: payload.email,
      role: payload.role,
      type: 'refresh',
      iat: Math.floor(Date.now() / 1000),
      jti: generateJTI()
    };

    const options = {
      expiresIn: config.refreshExpiresIn,
      issuer: config.issuer,
      audience: config.audience,
      algorithm: config.algorithm
    };

    const token = jwt.sign(refreshPayload, config.secret, options);
    
    // console.log(`Refresh token gerado para usuário: ${payload.email || payload.sub}`);
    return token;
    
  } catch (error) {
    console.error('Erro ao gerar refresh token:', error);
    throw new Error('Falha ao gerar refresh token');
  }
}

/**
 * Verifica e decodifica um token JWT usando credenciais do Secrets Manager
 * @param {string} token - Token JWT para verificar
 * @returns {Promise<Object>} Payload decodificado do token
 */
async function verifyToken(token) {
  try {
    const config = await getJWTConfig();
    
    const options = {
      issuer: config.issuer,
      audience: config.audience,
      algorithms: [config.algorithm]
    };

    const decoded = jwt.verify(token, config.secret, options);

    const source = config._fallback ? 'ENV_FALLBACK' : 'SECRETS_MANAGER';
    // console.log(`✅ Token verificado com sucesso para usuário: ${decoded.email || decoded.sub || 'N/A'} (${source})`);
    return decoded;
    
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new Error('Token expirado');
    } else if (error.name === 'JsonWebTokenError') {
      throw new Error('Token inválido');
    } else if (error.name === 'NotBeforeError') {
      throw new Error('Token ainda não é válido');
    } else {
      console.error('Erro ao verificar token:', error);
      throw new Error('Falha na verificação do token');
    }
  }
}

/**
 * Decodifica um token sem verificar a assinatura
 * @param {string} token - Token JWT para decodificar
 * @returns {Object} Payload decodificado
 */
function decodeToken(token) {
  try {
    return jwt.decode(token, { complete: true });
  } catch (error) {
    console.error('Erro ao decodificar token:', error);
    throw new Error('Token malformado');
  }
}

/**
 * Verifica se um token está próximo do vencimento
 * @param {string} token - Token JWT para verificar
 * @param {number} thresholdMinutes - Limite em minutos antes do vencimento
 * @returns {boolean} True se o token está próximo do vencimento
 */
function isTokenNearExpiry(token, thresholdMinutes = 30) {
  try {
    const decoded = jwt.decode(token);
    if (!decoded || !decoded.exp) {
      return true; // Se não conseguir decodificar, considera como próximo do vencimento
    }

    const now = Math.floor(Date.now() / 1000);
    const threshold = thresholdMinutes * 60;
    
    return (decoded.exp - now) <= threshold;
  } catch (error) {
    console.error('Erro ao verificar expiração do token:', error);
    return true;
  }
}

/**
 * Obtém informações sobre um token sem verificar a assinatura
 * @param {string} token - Token JWT
 * @returns {Object} Informações do token
 */
function getTokenInfo(token) {
  try {
    const decoded = jwt.decode(token, { complete: true });
    if (!decoded) {
      throw new Error('Token não pôde ser decodificado');
    }

    const payload = decoded.payload;
    const now = Math.floor(Date.now() / 1000);

    return {
      header: decoded.header,
      payload: payload,
      isExpired: payload.exp ? payload.exp < now : false,
      expiresAt: payload.exp ? new Date(payload.exp * 1000).toISOString() : null,
      issuedAt: payload.iat ? new Date(payload.iat * 1000).toISOString() : null,
      issuer: payload.iss,
      audience: payload.aud,
      subject: payload.sub,
      jwtId: payload.jti,
      tokenType: payload.type || 'access'
    };
  } catch (error) {
    console.error('Erro ao obter informações do token:', error);
    throw new Error('Token inválido');
  }
}

/**
 * Gera um JWT ID único
 * @returns {string} JWT ID
 */
function generateJTI() {
  return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * Valida a estrutura de um payload JWT
 * @param {Object} payload - Payload para validar
 * @returns {boolean} True se válido
 */
function validatePayload(payload) {
  const requiredFields = ['sub', 'email'];
  return requiredFields.every(field => payload[field]);
}

/**
 * Limpa o cache de credenciais JWT
 */
function clearJWTCache() {
  jwtCredentialsCache = null;
  cacheExpiry = 0;
  // console.log('Cache de credenciais JWT limpo');
}

/**
 * Obtém estatísticas do cache JWT
 * @returns {Object} Estatísticas do cache
 */
function getJWTCacheStats() {
  return {
    cached: !!jwtCredentialsCache,
    expiresAt: cacheExpiry ? new Date(cacheExpiry).toISOString() : null,
    isValid: jwtCredentialsCache && Date.now() < cacheExpiry
  };
}

module.exports = {
  generateToken,
  generateRefreshToken,
  verifyToken,
  decodeToken,
  isTokenNearExpiry,
  getTokenInfo,
  validatePayload,
  clearJWTCache,
  getJWTCacheStats,
  getJWTConfig
};
