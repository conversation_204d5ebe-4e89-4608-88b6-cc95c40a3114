export const mockedData = {
  Items: [
    {
      line_item_usage_account_id: "************",
      unblended_cost: 4599.15,
      month: "3",
      bill_invoice_id: "**********",
      bill_billing_entity: "AWS",
      bill_payer_account_id: "************",
      line_item_currency_code: "USD",
      line_item_legal_entity: "Amazon AWS Serviços Brasil Ltda.",
      line_item_line_item_type: "Usage",
      discount_spp_discount: -505.91,
      credit: 0,
      savings: 0,
      tax: 0,
      other_discounts: 0,
    },
    {
      line_item_usage_account_id: "************",
      unblended_cost: 4573.8,
      month: "3",
      bill_invoice_id: "**********",
      bill_billing_entity: "AWS",
      bill_payer_account_id: "************",
      line_item_currency_code: "USD",
      line_item_legal_entity: "Amazon AWS Serviços Brasil Ltda.",
      line_item_line_item_type: "Usage",
      discount_spp_discount: -503.12,
      credit: 0,
      savings: 0,
      tax: 0,
      other_discounts: 0,
    },
    {
      line_item_usage_account_id: "************",
      unblended_cost: 4464,
      month: "3",
      bill_invoice_id: "**********",
      bill_billing_entity: "AWS",
      bill_payer_account_id: "************",
      line_item_currency_code: "USD",
      line_item_legal_entity: "Amazon AWS Serviços Brasil Ltda.",
      line_item_line_item_type: "SavingsPlanRecurringFee",
      discount_spp_discount: -491.04,
      credit: 0,
      savings: 0,
      tax: 0,
      other_discounts: 0,
    },
    {
      line_item_usage_account_id: "************",
      unblended_cost: 4453.52,
      month: "3",
      bill_invoice_id: "**********",
      bill_billing_entity: "AWS",
      bill_payer_account_id: "************",
      line_item_currency_code: "USD",
      line_item_legal_entity: "Amazon AWS Serviços Brasil Ltda.",
      line_item_line_item_type: "Tax",
      discount_spp_discount: 0,
      credit: 0,
      savings: 0,
      tax: 4453.52,
      other_discounts: 0,
    },
  ],
};
