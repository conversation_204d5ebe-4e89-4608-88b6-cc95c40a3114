import { message, json } from '../../shared/response.js';
import { CognitoIdentityServiceProvider } from 'aws-sdk';
import { withAuth } from '../../shared/auth/auth-middleware.js';

const cognito = new CognitoIdentityServiceProvider({
  region: process.env.AWS_REGION_LOCATION
});

async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

/**
 * Handler principal para leitura de usuários do Cognito
 * Agora protegido com autenticação baseada em cookies
 */
const cognitoReadHandler = async (event, context) => {
  try {
    console.log(`Usuário autenticado: ${event.user.email} (${event.user.role})`);
    
    // Verifica se o usuário tem permissão para listar usuários
    const requiredPermissions = ['cognito:read', 'users:list'];
    const userPermissions = event.user.permissions || [];
    
    const hasPermission = requiredPermissions.some(permission => 
      userPermissions.includes(permission)
    ) || event.user.role === 'admin';
    
    if (!hasPermission && event.user.role !== 'admin') {
      return await sendDataToUser(403, 'error', 'Permissões insuficientes para listar usuários');
    }
    
    const read = await cognito.listUsers({
      UserPoolId: process.env.USER_POOL_ID
    }).promise();

    // Paginação para buscar todos os usuários
    while (read.PaginationToken) {
      const { Users, PaginationToken } = await cognito.listUsers({
        UserPoolId: process.env.USER_POOL_ID,
        PaginationToken: read.PaginationToken
      }).promise();

      read.Users.push(...Users);
      read.PaginationToken = PaginationToken;
    }

    // Mapeia e formata os dados dos usuários
    const response = read.Users.map(user => {
      const obj = {};

      user.Attributes.map(attribute => {
        const refactorName = {
          email: () => obj.email = attribute.Value,
          role: () => obj.permission = attribute.Value,
          hml_role: () => obj.hml_permission = attribute.Value,
          dev_role: () => obj.dev_permission = attribute.Value
        };

        try {
          refactorName[attribute?.Name?.replace('custom:', '')]();
        } catch (err) {
          // Ignora atributos não mapeados
        }

        if (user.Username.includes('\\')) {
          obj.user = user.Username.split('\\')[1];
        } else {
          obj.user = user.Username;
        }
        obj.status = user.Enabled;

        return attribute;
      });

      return obj;
    });

    // Log de auditoria
    console.log(`Usuários listados por: ${event.user.email}, Total: ${response.length}`);

    return await sendDataToUser(
      200, 
      'success', 
      response.filter(e => e?.user !== undefined)
    );
    
  } catch (error) {
    console.error('Erro ao listar usuários do Cognito:', error);
    return await sendDataToUser(500, 'error', 'Erro interno ao listar usuários');
  }
};

/**
 * Handler exportado com middleware de autenticação
 * Configurações:
 * - requireAuth: true (requer autenticação)
 * - autoRefresh: true (renova token automaticamente se próximo do vencimento)
 * - requiredRole: 'admin' (apenas admins podem acessar)
 */
export const handler = withAuth(cognitoReadHandler, {
  requireAuth: true,
  autoRefresh: true,
  requiredRole: 'admin', // Apenas admins podem listar usuários
  requiredPermissions: [] // Permissões específicas (opcional, role já controla???)
});
