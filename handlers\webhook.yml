webhook-send:
  handler: src/functions/webhook/webhookSend.handler
  name: ${self:custom.dotenv.STAGE}-webhook-send-notification${self:custom.dotenv.VERSION}
  description: Função para enviar notificação via Teams sobre solicitação de switch role
  memorySize: 128
  events:
    - http:
        path: /send/notification
        method: post
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}


