import { json, message } from '../shared/response';
import { getCorsOrigins } from '../config/environments';

/**
 * CORS Middleware
 * Centralized CORS configuration for all endpoints
 */

/**
 * Get allowed origins based on environment
 */
function getAllowedOrigins() {
  return getCorsOrigins();
}

/**
 * Check if origin is allowed
 */
function isOriginAllowed(origin, allowedOrigins) {
  if (!origin) return false;
  return allowedOrigins.includes(origin);
}

/**
 * Get CORS headers for a specific origin
 */
function getCorsHeaders(origin, options = {}) {
  const {
    allowCredentials = true,
    allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders = [
      'Content-Type',
      'Authorization',
      'X-Amz-Date',
      'X-Api-Key',
      'X-Amz-Security-Token',
      'X-CSRF-Token',
      'Cookie',
      'DynamoDB',
      'x-waf-header'
    ],
    exposedHeaders = [
      'X-CSP-Nonce',
      'X-Request-ID'
    ],
    maxAge = 86400 // 24 hours
  } = options;

  const allowedOrigins = getAllowedOrigins();
  const corsOrigin = isOriginAllowed(origin, allowedOrigins) ? origin : allowedOrigins[0];

  return {
    'Access-Control-Allow-Origin': corsOrigin,
    'Access-Control-Allow-Credentials': allowCredentials.toString(),
    'Access-Control-Allow-Methods': allowedMethods.join(', '),
    'Access-Control-Allow-Headers': allowedHeaders.join(', '),
    'Access-Control-Expose-Headers': exposedHeaders.join(', '),
    'Access-Control-Max-Age': maxAge.toString(),
    'Vary': 'Origin'
  };
}

/**
 * CORS Middleware for Serverless Framework
 */
export const corsMiddleware = (options = {}) => {
  return (handler) => {
    return async (event, context) => {
      try {
        const origin = event.headers?.Origin || event.headers?.origin;
        const method = event.httpMethod || event.requestContext?.http?.method;

        // Handle preflight requests
        if (method === 'OPTIONS') {
          const corsHeaders = getCorsHeaders(origin, options);

          return {
            statusCode: 200,
            headers: {
              ...corsHeaders,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ message: 'CORS preflight successful' })
          };
        }

        // Process the actual request
        const response = await handler(event, context);

        // Add CORS headers to response
        const corsHeaders = getCorsHeaders(origin, options);

        return {
          ...response,
          headers: {
            ...response.headers,
            ...corsHeaders
          }
        };

      } catch (error) {
        console.error('CORS Middleware error:', error);

        // Return error with CORS headers
        const origin = event.headers?.Origin || event.headers?.origin;
        const corsHeaders = getCorsHeaders(origin, options);

        return {
          statusCode: 500,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            status: 'error',
            message: 'Internal server error'
          })
        };
      }
    };
  };
};

/**
 * Strict CORS Middleware for sensitive endpoints
 */
export const strictCorsMiddleware = (options = {}) => {
  const strictOptions = {
    ...options,
    allowCredentials: true,
    allowedMethods: options.allowedMethods || ['POST', 'GET'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-CSRF-Token',
      'Cookie'
    ],
    maxAge: 3600 // 1 hour for sensitive endpoints
  };

  return corsMiddleware(strictOptions);
};

/**
 * Public CORS Middleware for public endpoints
 */
export const publicCorsMiddleware = (options = {}) => {
  const publicOptions = {
    ...options,
    allowCredentials: false,
    allowedMethods: options.allowedMethods || ['GET', 'POST'],
    allowedHeaders: [
      'Content-Type',
      'X-Amz-Date',
      'X-Api-Key',
      'X-Amz-Security-Token'
    ]
  };

  return corsMiddleware(publicOptions);
};

/**
 * CORS validation middleware
 * Validates that the request origin is allowed
 */
export const corsValidationMiddleware = (handler) => {
  return async (event, context) => {
    try {
      const origin = event.headers?.Origin || event.headers?.origin;
      const allowedOrigins = getAllowedOrigins();

      // Skip validation for same-origin requests (no Origin header)
      if (!origin) {
        return await handler(event, context);
      }

      // Check if origin is allowed
      if (!isOriginAllowed(origin, allowedOrigins)) {
        console.warn(`Blocked request from unauthorized origin: ${origin}`);

        return await json(await message('error', 'Origin not allowed'), 403);
      }

      return await handler(event, context);

    } catch (error) {
      console.error('CORS Validation Middleware error:', error);
      return await json(await message('error', 'CORS validation failed'), 500);
    }
  };
};

/**
 * CORS configuration for different endpoint types
 */
export const corsConfigs = {
  // Authentication endpoints
  auth: {
    allowCredentials: true,
    allowedMethods: ['POST', 'GET', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-CSRF-Token',
      'Cookie'
    ],
    maxAge: 3600
  },

  // API endpoints with authentication
  api: {
    allowCredentials: true,
    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-Amz-Date',
      'X-Api-Key',
      'X-Amz-Security-Token',
      'DynamoDB',
      'x-waf-header'
    ],
    maxAge: 86400
  },

  // Public endpoints
  public: {
    allowCredentials: false,
    allowedMethods: ['GET', 'POST', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'X-Amz-Date',
      'X-Api-Key',
      'X-Amz-Security-Token'
    ],
    maxAge: 86400
  },

  // File upload endpoints
  upload: {
    allowCredentials: true,
    allowedMethods: ['POST', 'PUT', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-Amz-Date',
      'X-Api-Key',
      'X-Amz-Security-Token'
    ],
    maxAge: 3600
  }
};

/**
 * Get CORS middleware for specific endpoint type
 */
export const getCorsMiddleware = (type = 'api') => {
  const config = corsConfigs[type] || corsConfigs.api;
  return corsMiddleware(config);
};

/**
 * Domain validation helper
 */
export const validateDomain = (origin) => {
  if (!origin) return false;

  try {
    const url = new URL(origin);
    const hostname = url.hostname;

    // Check for allowed domains
    const allowedDomains = [
      'dsm.com.br',
      'localhost',
      '127.0.0.1'
    ];

    return allowedDomains.some(domain =>
      hostname === domain || hostname.endsWith(`.${domain}`)
    );
  } catch (error) {
    return false;
  }
};

export default corsMiddleware;
