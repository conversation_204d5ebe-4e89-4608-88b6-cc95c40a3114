import { parseBody } from '../../shared/parsers'
import { message, json } from '../../shared/response'
import { S3 } from 'aws-sdk'

const clientS3 = new S3({
  region: process.env.AWS_REGION_LOCATION
})

async function sendDataToUser (status, statusString, data) {
  const msg = await message(statusString, data)
  return await json(msg, status)
};

export const handler = async (event, context) => {
  try {
    const { bucket, key, operation, content } = parseBody(event)

    const params = { Bucket: bucket, Key: key, Expires: 60, ContentType: content }

    const promise = clientS3.getSignedUrlPromise(`${operation}Object`, params)

    return promise.then(function (url) {
      return sendDataToUser(200, 'success', url)
    })
  } catch (error) {
    return await sendDataToUser(500, 'error', error)
  }
}
