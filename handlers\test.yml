cors-test:
  handler: src/functions/test/cors-test.handler
  name: ${env:STAGE}-cors-test${env:VERSION}
  description: Endpoint de teste para verificar configuração de CORS
  memorySize: 128
  timeout: 30
  events:
    - http:
        path: /test/cors
        method: get
        cors: true

cors-test-options:
  handler: src/functions/test/cors-test.handler
  name: ${env:STAGE}-cors-test-options${env:VERSION}
  description: Endpoint de teste para verificar preflight CORS
  memorySize: 128
  timeout: 30
  events:
    - http:
        path: /test/cors
        method: options
        cors: true

# cookie-test:
#   handler: src/functions/test/cookie-test.handler
#   name: ${env:STAGE}-cookie-test${env:VERSION}
#   description: Endpoint de teste para verificar configuração de cookies (DESABILITADO - HttpOnly)
#   memorySize: 128
#   timeout: 30
#   events:
#     - http:
#         path: /test/cookies
#         method: get
#         cors: true

