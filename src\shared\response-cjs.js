"use strict";

const STATUS_CODE = {
  SUCCESS: 200,
  BAD_REQUEST: 400,
  NOT_FOUND: 404,
  CONFLICT: 409,
  ERROR: 500,
};

async function message(status, data) {
  return {
    status,
    data,
  };
}

async function json(body = {}, status = 200, additionalHeaders = {}) {
  const allowedOrigins = process.env.ALLOWED_ORIGINS ?
    process.env.ALLOWED_ORIGINS.split(',').map(o => o.trim()) : ['*'];

  const origin = allowedOrigins.includes('*') ? '*' :
    (allowedOrigins.length > 0 ? allowedOrigins[0] : '*');

  const headers = {
    "Access-Control-Allow-Origin": origin,
    "Access-Control-Allow-Credentials": "true",
    "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With",
    "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
    ...additionalHeaders
  };

  return {
    statusCode: status,
    headers,
    body: body != null ? JSON.stringify(body.stack ? body.stack : body) : "",
  };
}

const responseWithError = (message, additionalHeaders = {}) => {
  const allowedOrigins = process.env.ALLOWED_ORIGINS ? 
    process.env.ALLOWED_ORIGINS.split(',') : ['*'];
  
  const origin = allowedOrigins.includes('*') ? '*' : allowedOrigins[0];
  
  return {
    statusCode: STATUS_CODE.ERROR,
    headers: {
      "Access-Control-Allow-Origin": origin,
      "Access-Control-Allow-Credentials": "true",
      ...additionalHeaders
    },
    body: JSON.stringify({
      message,
    }),
  };
};

const responseWithBadRequest = (message, additionalHeaders = {}) => {
  const allowedOrigins = process.env.ALLOWED_ORIGINS ? 
    process.env.ALLOWED_ORIGINS.split(',') : ['*'];
  
  const origin = allowedOrigins.includes('*') ? '*' : allowedOrigins[0];
  
  return {
    statusCode: STATUS_CODE.BAD_REQUEST,
    headers: {
      "Access-Control-Allow-Origin": origin,
      "Access-Control-Allow-Credentials": "true",
      ...additionalHeaders
    },
    body: JSON.stringify({
      message,
    }),
  };
};

const responseWithNotFound = (message) => {
  return {
    statusCode: STATUS_CODE.NOT_FOUND,
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
    body: JSON.stringify({
      message,
    }),
  };
};

const responseWithConflict = (message) => {
  return {
    statusCode: STATUS_CODE.CONFLICT,
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
    body: JSON.stringify({
      message,
    }),
  };
};

const responseWithSuccess = (data, message = "", additionalHeaders = {}) => {
  return {
    statusCode: STATUS_CODE.SUCCESS,
    headers: {
      ...additionalHeaders
    },
    body: JSON.stringify(data || message),
  };
};

/**
 * Resposta de sucesso com cookies
 * @param {*} data - Dados da resposta
 * @param {Array} cookies - Array de strings de cookies
 * @param {string} message - Mensagem opcional
 * @returns {Object} Resposta Lambda com cookies
 */
const responseWithCookies = (data, cookies = [], message = "") => {
  const allowedOrigins = process.env.ALLOWED_ORIGINS ?
    process.env.ALLOWED_ORIGINS.split(',') : ['*'];

  const origin = allowedOrigins.includes('*') ? '*' : allowedOrigins[0];

  const headers = {
    "Access-Control-Allow-Origin": origin,
    "Access-Control-Allow-Credentials": "true",
  };

  const response = {
    statusCode: STATUS_CODE.SUCCESS,
    headers,
    body: JSON.stringify(data || message),
  };

  // API Gateway: usar multiValueHeaders para múltiplos cookies
  if (cookies.length > 0) {
    response.multiValueHeaders = {
      'Set-Cookie': cookies
    };
  }

  return response;
};

/**
 * Resposta para logout com cookies de limpeza
 * @param {Array} deleteCookies - Array de cookies para deletar
 * @param {string} message - Mensagem de logout
 * @returns {Object} Resposta Lambda
 */
const responseWithLogout = (deleteCookies = [], message = "Logout realizado com sucesso") => {
  const allowedOrigins = process.env.ALLOWED_ORIGINS ? 
    process.env.ALLOWED_ORIGINS.split(',') : ['*'];
  
  const origin = allowedOrigins.includes('*') ? '*' : allowedOrigins[0];
  
  const headers = {
    "Access-Control-Allow-Origin": origin,
    "Access-Control-Allow-Credentials": "true",
  };
  
  if (deleteCookies.length > 0) {
    headers['Set-Cookie'] = deleteCookies.length === 1 ? deleteCookies[0] : deleteCookies;
  }
  
  return {
    statusCode: STATUS_CODE.SUCCESS,
    headers,
    body: JSON.stringify({ message }),
  };
};

const responseWithUnauthorized = (message, additionalHeaders = {}) => {
  const allowedOrigins = process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : ['*'];
  const origin = allowedOrigins.includes('*') ? '*' : allowedOrigins[0];
  return {
    statusCode: 401,
    headers: {
      "Access-Control-Allow-Origin": origin,
      "Access-Control-Allow-Credentials": "true",
      ...additionalHeaders
    },
    body: JSON.stringify({
      message
    })
  };
};

module.exports = {
  STATUS_CODE,
  message,
  json,
  responseWithError,
  responseWithBadRequest,
  responseWithNotFound,
  responseWithConflict,
  responseWithSuccess,
  responseWithCookies,
  responseWithLogout,
  responseWithUnauthorized
};
