import { makeS3 } from "../../shared/services/s3-service";
import { errorValidator } from "../../shared/validators";
import { parseBody } from "../../shared/parsers";

import { parse } from "aws-multipart-parser";

import {
  STATUS_CODE,
  responseWithError,
  responseWithSuccess,
  responseWithBadRequest,
} from "../../shared/response";

const s3 = makeS3();

export const handler = async (event) => {
  try {
    const body = parseBody(event);

    const bucketName = body.bucketName;

    console.log({ bucketName });

    let params = {
      Bucket: bucketName,
    };

    if (body.prefix) {
      params = { ...params, Prefix: body.prefix };
    }

    console.log({ params });

    const responseS3 = await s3.listObjects(params).promise();
    console.log({ responseS3 });

    return responseWithSuccess(
      responseS3,
      "Listagem de objetos realizada com sucesso"
    );
  } catch (error) {
    const { statusCode, message } = errorValidator(error);

    if (STATUS_CODE.BAD_REQUEST === statusCode)
      return responseWithBadRequest(message);

    return responseWithError(message);
  }
};
