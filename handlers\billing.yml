billing-data:
  handler: src/functions/billing/dataLoad.handler
  name: ${self:custom.dotenv.STAGE}-billing-data${self:custom.dotenv.VERSION}
  description: Função para carregar os dados de billing a cada 6h
  memorySize: 128
  timeout: 900
  events:
    - http:
        path: /billing/event/data
        method: post
        cors: true
    - schedule:
        rate: rate(1 hour)
        enabled: true

one-customer-check:
  handler: src/functions/billing/oneCustomer.handler
  name: ${self:custom.dotenv.STAGE}-one-customer-check${self:custom.dotenv.VERSION}
  description: Função para ler os dados de billing de 1 cliente
  memorySize: 128
  events:
    - http:
        path: /billing/one/customer
        method: post
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}

get-billing-document:
  handler: src/functions/billing/getBillingDocument.handler
  name: ${self:custom.dotenv.STAGE}-get-billing-document${self:custom.dotenv.VERSION}
  description: Função para retornar documento PDF ou Excel de 1 cliente
  memorySize: 128
  events:
    - http:
        path: /billing/get/{payer}/{fileFormat}
        method: get
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}

get-account-cost-by-service:
  handler: src/functions/billing/accountCostByService.handler
  name: ${self:custom.dotenv.STAGE}-get-account-cost-by-service${self:custom.dotenv.VERSION}
  description: Função para retornar os custos de uma conta por servicos
  memorySize: 128
  events:
    - http:
        path: /billing/service/cost
        method: get
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}

get-account-costs-by-payer:
  handler: src/functions/billing/getAccountCostsByPayer.handler
  name: ${self:custom.dotenv.STAGE}-get-account-costs-by-payer${self:custom.dotenv.VERSION}
  description: Função para retornar lista de contas pertencentes a uma payer com seus respectivos custos
  memorySize: 128
  events:
    - http:
        path: /billing/accounts/payer/{payer}
        method: get
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}

generate-billing-excel:
  handler: src/functions/billing/generateBillingExcel.handler
  name: ${self:custom.dotenv.STAGE}-generate-billing-excel${self:custom.dotenv.VERSION}
  description: Função para retornar lista de contas pertencentes a uma payer com seus respectivos custos
  memorySize: 128
  timeout: 30
  layers:
    - DocumentGenerationLambdaLayer
  events:
    - http:
        path: /billing/excel/{payer}
        method: get
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}

generate-billing-pdf:
  handler: src/functions/billing/generateBillingPDF.handler
  name: ${self:custom.dotenv.STAGE}-generate-billing-pdf${self:custom.dotenv.VERSION}
  description: Função para retornar lista de contas pertencentes a uma payer com seus respectivos custos
  memorySize: 128
  timeout: 30
  layers:
    - DocumentGenerationLambdaLayer
  events:
    - http:
        path: /billing/pdf/{payer}
        method: get
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}


