#!/usr/bin/env node

/**
 * Script para testar as correções JWT implementadas
 */

const { generateToken, generateRefreshToken, verifyToken } = require('./src/shared/auth/jwt-utils-cjs');

async function testJWTFixes() {
  console.log('🧪 TESTANDO CORREÇÕES JWT');
  console.log('========================');

  try {
    // 1. Testar geração de token
    console.log('1️⃣ Testando geração de token...');
    const testPayload = {
      sub: 'test-user-123',
      email: '<EMAIL>',
      role: 'admin',
      name: 'Test User',
      permissions: ['users:read', 'users:write']
    };

    const accessToken = generateToken(testPayload);
    console.log('✅ Token de acesso gerado com sucesso');
    console.log(`   Tamanho: ${accessToken.length} caracteres`);

    // 2. Testar geração de refresh token
    console.log('\n2️⃣ Testando geração de refresh token...');
    const refreshToken = generateRefreshToken({
      sub: testPayload.sub,
      email: testPayload.email,
      role: testPayload.role
    });
    console.log('✅ Refresh token gerado com sucesso');
    console.log(`   Tamanho: ${refreshToken.length} caracteres`);

    // 3. Testar verificação de token
    console.log('\n3️⃣ Testando verificação de token...');
    const decoded = verifyToken(accessToken);
    console.log('✅ Token verificado com sucesso');
    console.log(`   Email: ${decoded.email}`);
    console.log(`   Role: ${decoded.role}`);
    console.log(`   Issuer: ${decoded.iss}`);
    console.log(`   Audience: ${decoded.aud}`);

    // 4. Testar verificação de refresh token
    console.log('\n4️⃣ Testando verificação de refresh token...');
    const decodedRefresh = verifyToken(refreshToken);
    console.log('✅ Refresh token verificado com sucesso');
    console.log(`   Type: ${decodedRefresh.type}`);
    console.log(`   Email: ${decodedRefresh.email}`);

    // 5. Testar token inválido
    console.log('\n5️⃣ Testando token inválido...');
    try {
      verifyToken('token-invalido');
      console.log('❌ Deveria ter falhado');
    } catch (error) {
      console.log('✅ Token inválido rejeitado corretamente');
      console.log(`   Erro: ${error.message}`);
    }

    console.log('\n🎉 TODOS OS TESTES PASSARAM!');
    console.log('✅ Correções JWT implementadas com sucesso');

    // 6. Gerar tokens para teste manual
    console.log('\n📋 TOKENS PARA TESTE MANUAL:');
    console.log('============================');
    console.log('\n🔑 Access Token:');
    console.log(accessToken);
    console.log('\n🔄 Refresh Token:');
    console.log(refreshToken);

    console.log('\n📝 COMANDOS CURL PARA TESTE:');
    console.log('============================');
    
    console.log('\n1. Testar set-token:');
    console.log(`curl -X POST http://localhost:8000/dev/auth/set-token \\
  -H "Content-Type: application/json" \\
  -d "{\\"token\\": \\"${accessToken}\\"}" \\
  -c cookies.txt -v`);

    console.log('\n2. Testar verify:');
    console.log(`curl -X GET http://localhost:8000/dev/auth/verify \\
  -b cookies.txt -v`);

    console.log('\n3. Testar refresh:');
    console.log(`curl -X POST http://localhost:8000/dev/auth/refresh \\
  -b cookies.txt -v`);

    console.log('\n4. Testar logout:');
    console.log(`curl -X POST http://localhost:8000/dev/auth/logout \\
  -b cookies.txt -v`);

  } catch (error) {
    console.error('❌ ERRO NO TESTE:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  }
}

// Executar testes
testJWTFixes().catch(error => {
  console.error('❌ Erro fatal:', error);
  process.exit(1);
});
