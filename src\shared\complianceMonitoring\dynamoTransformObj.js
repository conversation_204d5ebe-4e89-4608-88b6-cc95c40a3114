export async function transformObjToDynamo(obj) {
  // const k = Object.keys(obj);
  // const v = Object.values(obj);

  // let res = {};
  // for (let i = 0; i < k.length; i++) {
  //   if (k[0] !== "id" && k[1]) {
  //     res = { ...res, [k[i]]: { Action: "PUT", Value: v[i] } };
  //   }
  // }

  let data = obj;
  const objectsData = Object.keys(data);

  for (let i = 0; i < objectsData.length; i++) {
    data[objectsData] = {
      Action: "PUT",
      Value: data[objectsData],
    };
  }

  return data;
}
