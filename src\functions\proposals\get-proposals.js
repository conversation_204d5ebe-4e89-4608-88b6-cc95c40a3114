import { ProposalEntity } from "../../entities/proposal-entity";
import { parseQueryString } from "../../shared/parsers";
import * as Yup from "yup";

import {
  STATUS_CODE,
  responseWithError,
  responseWithSuccess,
  responseWithBadRequest,
  responseWithNotFound,
} from "../../shared/response";

import { errorValidator } from "../../shared/validators";

import { makeDynamoDB } from "../../shared/services/dynamo-service";

let bodyValidade = Yup.object().shape({
  status: Yup.string().default(""),
  id: Yup.string().default(""),
});

const clientDynamo = makeDynamoDB();
export const handler = async (event, context) => {
  try {
    const { status, id } = parseQueryString(event);

    const params = await bodyValidade.validate(
      { status, id },
      { abortEarly: false }
    );

    const entity = new ProposalEntity(clientDynamo);
    const inputGetProposal = entity.generateInputGetProposal(params);
    console.log(inputGetProposal);
    const proposals = await entity.getPropostals(inputGetProposal);

    if (proposals.length === 0)
      return responseWithNotFound("Proposals not found");

    return responseWithSuccess(proposals, "Query successfully");
  } catch (error) {
    const { statusCode, message } = errorValidator(error);

    if (STATUS_CODE.BAD_REQUEST === statusCode)
      return responseWithBadRequest(message);

    return responseWithError(message);
  }
};
