create-data:
  handler: src/functions/dynamodb/create.handler
  name: ${env:STAGE}-create-data${env:VERSION}
  description: Função para inserção de um dado em uma tabela do DynamoDB
  memorySize: 128
  events:
    - http:
        path: /create
        method: post
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

read-data:
  handler: src/functions/dynamodb/read.handler
  name: ${env:STAGE}-read-data${env:VERSION}
  description: Função para leitura de dados da tabela do DynamoDB
  memorySize: 128
  events:
    - http:
        path: /read/{form}/{id}
        method: get
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

read-audits:
  handler: src/functions/dynamodb/readAudits.handler
  name: ${env:STAGE}-read-audits${env:VERSION}
  description: Função para leitura de dados de auditoria da tabela do DynamoDB
  memorySize: 128
  events:
    - http:
        path: /read/audits/{month}/{year}
        method: get
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

read-audits-switch-role:
  handler: src/functions/dynamodb/readAuditsSwitchRole.handler
  name: ${env:STAGE}-read-audits-switch-role${env:VERSION}
  description: Função para leitura de dados de switch role de auditoria da tabela do DynamoDB
  memorySize: 128
  events:
    - http:
        path: /read/audits
        method: get
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

update-data:
  handler: src/functions/dynamodb/update.handler
  name: ${env:STAGE}-update-data${env:VERSION}
  description: Função para atualização de um dado na tabela do DynamoDB
  memorySize: 128
  events:
    - http:
        path: /update/{id}
        method: put
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

delete-data:
  handler: src/functions/dynamodb/delete.handler
  name: ${env:STAGE}-delete-data${env:VERSION}
  description: Função para deleção de um dado na tabela do DynamoDB
  memorySize: 128
  events:
    - http:
        path: /delete/{id}
        method: delete
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

get-paginate:
  handler: src/functions/dynamodb/read-paginate.handler
  name: ${env:STAGE}-read-paginate${env:VERSION}
  description: Função para buscar as registros paginados no Dynamodb
  memorySize: 128
  timeout: 30
  events:
    - http:
        path: /read/paginate
        method: get
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}


