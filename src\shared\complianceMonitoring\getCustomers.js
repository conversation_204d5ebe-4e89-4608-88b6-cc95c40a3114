const axios = require("axios");

export async function getCustomers() {
  const dsmUrl = "https://ulas9sl10f.execute-api.us-east-1.amazonaws.com/prod/";

  const jwtToken = await axios.post(dsmUrl + "cognito/token", {
    arnSecret:
      "arn:aws:secretsmanager:us-east-1:800844742271:secret:dsm-integration-crm-user-nn1ZiE",
  });

  let customers = await axios.get(dsmUrl + "read/all/0", {
    headers: {
      dynamodb: `${process.env.FINOPS_STAGE}-customers`,
      Authorization: jwtToken.data.data,
    },
  });

  customers = customers.data.data.Items;

  return customers;
}
