import { checkConfiguredBillingStatus } from "../../shared/complianceMonitoring/checkCustomerStatus";
import * as dynamo from "../../model/dynamo";

jest.mock("../../model/dynamo");

describe("checkConfiguredBillingStatus", () => {
  beforeEach(() => {
    dynamo.readByIndex.mockReset();
  });

  it("should return true if billing agreement is configured", async () => {
    dynamo.readByIndex.mockResolvedValueOnce({
      Items: [
        { name: "Billing Contract", billingConfigured: true, active: 1 },
        { name: "Contract", billingConfigured: false, active: 1 },
      ],
    });

    const customerId = "customer123";
    const result = await checkConfiguredBillingStatus(customerId);
    expect(result).toBe(true);
  });

  it("should return false if billing agreement is not configured", async () => {
    dynamo.readByIndex.mockResolvedValueOnce({
      Items: [
        { name: "Billing Contract", billingConfigured: false, active: 1 },
        { name: "Contract", billingConfigured: false, active: 1 },
      ],
    });

    const customerId = "customer123";
    const result = await checkConfiguredBillingStatus(customerId);
    expect(result).toBe(false);
  });
});
