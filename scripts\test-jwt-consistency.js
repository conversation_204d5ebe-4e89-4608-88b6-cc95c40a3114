/**
 * Script para testar consistência entre geração e verificação de JWT
 */

// Carrega variáveis de ambiente do .env.dev
require('dotenv').config({ path: '.env.dev' });

const { generateToken, verifyToken } = require('../src/shared/auth/jwt-utils-secrets');

/**
 * Testa se um token gerado pode ser verificado
 */
async function testJWTConsistency() {
  try {
    console.log('🧪 Testando consistência JWT (geração vs verificação)\n');

    // Payload de teste
    const testPayload = {
      sub: 'test-user-123',
      email: '<EMAIL>',
      role: 'user',
      name: '<PERSON><PERSON><PERSON><PERSON>e'
    };

    console.log('1️⃣ Gerando token...');
    const token = await generateToken(testPayload);
    console.log(`✅ Token gerado: ${token.substring(0, 50)}...`);

    console.log('\n2️⃣ Verificando token gerado...');
    const decoded = await verifyToken(token);
    console.log('✅ Token verificado com sucesso!');
    console.log(`   Email: ${decoded.email}`);
    console.log(`   Role: ${decoded.role}`);
    console.log(`   Sub: ${decoded.sub}`);

    console.log('\n3️⃣ Comparando payloads...');
    const matches = {
      email: testPayload.email === decoded.email,
      role: testPayload.role === decoded.role,
      sub: testPayload.sub === decoded.sub
    };

    console.log(`   Email: ${matches.email ? '✅' : '❌'} (${testPayload.email} vs ${decoded.email})`);
    console.log(`   Role: ${matches.role ? '✅' : '❌'} (${testPayload.role} vs ${decoded.role})`);
    console.log(`   Sub: ${matches.sub ? '✅' : '❌'} (${testPayload.sub} vs ${decoded.sub})`);

    const allMatch = Object.values(matches).every(Boolean);
    console.log(`\n🎯 Consistência: ${allMatch ? '✅ PERFEITA' : '❌ PROBLEMAS'}`);

    return allMatch;

  } catch (error) {
    console.error('\n❌ Erro no teste de consistência:');
    console.error(`   Tipo: ${error.name || 'Error'}`);
    console.error(`   Mensagem: ${error.message}`);
    console.error(`   Stack: ${error.stack}`);
    return false;
  }
}

/**
 * Testa token inválido
 */
async function testInvalidToken() {
  try {
    console.log('\n🧪 Testando token inválido...');
    
    await verifyToken('token.invalido.aqui');
    console.log('❌ Token inválido foi aceito (problema!)');
    return false;

  } catch (error) {
    console.log('✅ Token inválido rejeitado corretamente');
    console.log(`   Erro: ${error.message}`);
    return true;
  }
}

/**
 * Testa token expirado
 */
async function testExpiredToken() {
  try {
    console.log('\n🧪 Testando token expirado...');

    // Gera token que expira em 1 segundo
    const expiredPayload = {
      sub: 'test-expired',
      email: '<EMAIL>',
      exp: Math.floor(Date.now() / 1000) - 10 // 10 segundos atrás
    };

    const token = await generateToken(expiredPayload);
    
    // Tenta verificar
    await verifyToken(token);
    console.log('❌ Token expirado foi aceito (problema!)');
    return false;

  } catch (error) {
    if (error.message.includes('expirado')) {
      console.log('✅ Token expirado rejeitado corretamente');
      return true;
    } else {
      console.log(`⚠️ Token rejeitado por outro motivo: ${error.message}`);
      return true; // Ainda é um comportamento correto
    }
  }
}

/**
 * Executa todos os testes
 */
async function runAllTests() {
  console.log('🚀 TESTE DE CONSISTÊNCIA JWT\n');
  console.log('=' .repeat(60));

  const results = {
    consistency: await testJWTConsistency(),
    invalidToken: await testInvalidToken(),
    expiredToken: await testExpiredToken()
  };

  console.log('\n' + '=' .repeat(60));
  console.log('📊 RESUMO DOS TESTES:');
  console.log(`   ✅ Consistência: ${results.consistency ? 'PASSOU' : 'FALHOU'}`);
  console.log(`   ✅ Token inválido: ${results.invalidToken ? 'PASSOU' : 'FALHOU'}`);
  console.log(`   ✅ Token expirado: ${results.expiredToken ? 'PASSOU' : 'FALHOU'}`);

  const allPassed = Object.values(results).every(Boolean);
  console.log(`\n🎯 RESULTADO GERAL: ${allPassed ? '✅ TODOS PASSARAM' : '❌ ALGUNS FALHARAM'}`);

  if (allPassed) {
    console.log('\n🎉 JWT está funcionando corretamente!');
    console.log('💡 O problema pode estar na API ou na validação do Cognito.');
  } else {
    console.log('\n❌ Há problemas na implementação JWT.');
    console.log('🔧 Verifique as configurações de geração e verificação.');
  }

  return allPassed;
}

// Executa se chamado diretamente
if (require.main === module) {
  runAllTests()
    .then(success => process.exit(success ? 0 : 1))
    .catch(error => {
      console.error('💥 Erro fatal:', error);
      process.exit(1);
    });
}

module.exports = { testJWTConsistency, testInvalidToken, testExpiredToken, runAllTests };
