cognito-read-users:
  handler: src/functions/cognito/read.handler
  name: ${env:API_NAME}-cognito-read-users
  description: Função para leitura de usuários do Cognito
  memorySize: 128
  events:
    - http:
        path: /cognito/read
        method: get
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

update-status:
  handler: src/functions/cognito/updateStatus.handler
  name: ${env:API_NAME}-update-status-v2
  description: Função para atualização do status do usuário
  memorySize: 128
  events:
    - http:
        path: /cognito/update/status
        method: put
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

update-role:
  handler: src/functions/cognito/updateRole.handler
  name: ${env:API_NAME}-update-role-v2
  description: Função para atualização da role do usuário
  memorySize: 128
  events:
    - http:
        path: /cognito/update/role
        method: put
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

token:
  handler: src/functions/cognito/token.handler
  name: ${env:API_NAME}-token
  description: Função para obter um token do cognito
  memorySize: 128
  events:
    - http:
        path: /cognito/token
        method: post
        cors: true

access-token:
  handler: src/functions/cognito/accessToken.handler
  name: ${env:API_NAME}-access-token-v2
  description: Função para obter um token do OTRS do cognito
  memorySize: 128
  events:
    - http:
        path: /cognito/access-token
        method: get
        cors:
          origin: "*"
          headers: "*"
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

