import axios from "axios";
import { SecretsManager } from "aws-sdk";

async function getSecretString() {
  const secretsManager = new SecretsManager({
    region: process.env.AWS_REGION_LOCATION,
  });
  const { SecretString } = await secretsManager
    .getSecretValue({
      SecretId: process.env.AWS_SECRET_CREDENTIALS_OTRS_API,
    })
    .promise();

  const parsedSecretString = JSON.parse(SecretString);
  return parsedSecretString;
}

export const generateToken = async () => {
  try {
    const secretString = await getSecretString();
    const { data } = await axios.post(
      secretString.accessTokenUrl,
      new URLSearchParams({
        grant_type: "client_credentials",
        client_id: secretString.clientId,
        client_secret: secretString.clientSecret,
        scope: "otrs/read",
      }).toString(),
      {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      }
    );
    return data.access_token;
  } catch (error) {
    console.log(error);
    throw new Error("Error generating token");
  }
};

export const otrsProvider = async () => {
  const token = await generateToken();

  return axios.create({
    baseURL: process.env.API_OTRS_BASE_URL,
    headers: {
      Authorization: token,
    },
  });
};
