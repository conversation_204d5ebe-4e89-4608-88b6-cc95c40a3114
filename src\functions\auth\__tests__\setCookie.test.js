import { handler } from '../setCookie';
import { enhancedSecretsManager } from '../../../shared/services/enhanced-secrets-manager';
import jwt from 'jsonwebtoken';

// Mock dependencies
jest.mock('../../../shared/services/enhanced-secrets-manager');
jest.mock('jsonwebtoken');
jest.mock('../../../shared/parsers', () => ({
  parseBody: jest.fn()
}));
jest.mock('../../../shared/response', () => ({
  json: jest.fn(),
  message: jest.fn()
}));

const { parseBody } = require('../../../shared/parsers');
const { json, message } = require('../../../shared/response');

describe('setCookie <PERSON>ler', () => {
  const mockEvent = {
    body: JSON.stringify({
      token: 'mock-cognito-token',
      userInfo: {
        name: '<PERSON>',
        email: '<EMAIL>',
        permission: 'admin'
      }
    })
  };

  const mockContext = {};

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock environment variables
    process.env.STAGE = 'dev';
    process.env.JWT_DECRIPTION_CREDENTIALS = 'mock-secret-arn';
    
    // Setup default mocks
    parseBody.mockReturnValue({
      token: 'mock-cognito-token',
      userInfo: {
        name: 'John Doe',
        email: '<EMAIL>',
        permission: 'admin'
      }
    });

    message.mockImplementation((status, data) => ({ status, data }));
    json.mockImplementation((msg, statusCode) => ({ 
      statusCode, 
      body: JSON.stringify(msg),
      headers: {},
      multiValueHeaders: {}
    }));
  });

  describe('successful token processing', () => {
    it('should successfully set authentication cookie', async () => {
      // Mock JWT operations
      jwt.decode.mockReturnValue({
        sub: 'user-123',
        email: '<EMAIL>',
        'cognito:username': 'john.doe',
        exp: Math.floor(Date.now() / 1000) + 3600 // 1 hour from now
      });

      jwt.sign.mockReturnValueOnce('session-token').mockReturnValueOnce('refresh-token');

      // Mock secrets manager
      enhancedSecretsManager.getJWTSecrets.mockResolvedValue({
        JWT_SECRET: 'mock-secret',
        JWT_EXPIRES_IN: '8h',
        JWT_REFRESH_EXPIRES_IN: '7d'
      });

      const result = await handler(mockEvent, mockContext);

      expect(result.statusCode).toBe(200);
      expect(enhancedSecretsManager.getJWTSecrets).toHaveBeenCalled();
      expect(jwt.decode).toHaveBeenCalledWith('mock-cognito-token', { complete: true });
      expect(jwt.sign).toHaveBeenCalledTimes(2); // session and refresh tokens
      expect(result.multiValueHeaders['Set-Cookie']).toHaveLength(2);
    });

    it('should set correct cookie attributes for production', async () => {
      process.env.STAGE = 'prod';

      jwt.decode.mockReturnValue({
        sub: 'user-123',
        email: '<EMAIL>',
        exp: Math.floor(Date.now() / 1000) + 3600
      });

      jwt.sign.mockReturnValueOnce('session-token').mockReturnValueOnce('refresh-token');

      enhancedSecretsManager.getJWTSecrets.mockResolvedValue({
        JWT_SECRET: 'mock-secret',
        JWT_EXPIRES_IN: '8h',
        JWT_REFRESH_EXPIRES_IN: '7d'
      });

      const result = await handler(mockEvent, mockContext);

      expect(result.multiValueHeaders['Set-Cookie'][0]).toContain('Secure=true');
      expect(result.multiValueHeaders['Set-Cookie'][0]).toContain('Domain=.darede.com.br');
    });

    it('should set correct CORS headers', async () => {
      jwt.decode.mockReturnValue({
        sub: 'user-123',
        email: '<EMAIL>',
        exp: Math.floor(Date.now() / 1000) + 3600
      });

      jwt.sign.mockReturnValueOnce('session-token').mockReturnValueOnce('refresh-token');

      enhancedSecretsManager.getJWTSecrets.mockResolvedValue({
        JWT_SECRET: 'mock-secret'
      });

      const result = await handler(mockEvent, mockContext);

      expect(result.headers['Access-Control-Allow-Origin']).toBe('http://localhost:3000');
      expect(result.headers['Access-Control-Allow-Credentials']).toBe('true');
    });
  });

  describe('error handling', () => {
    it('should return 400 when token is missing', async () => {
      parseBody.mockReturnValue({ userInfo: {} });

      const result = await handler(mockEvent, mockContext);

      expect(result.statusCode).toBe(400);
      expect(message).toHaveBeenCalledWith('error', 'Token is required');
    });

    it('should return 500 when token verification fails', async () => {
      jwt.decode.mockReturnValue(null);

      const result = await handler(mockEvent, mockContext);

      expect(result.statusCode).toBe(500);
      expect(message).toHaveBeenCalledWith('error', expect.stringContaining('Invalid token format'));
    });

    it('should return 500 when token is expired', async () => {
      jwt.decode.mockReturnValue({
        sub: 'user-123',
        email: '<EMAIL>',
        exp: Math.floor(Date.now() / 1000) - 3600 // 1 hour ago (expired)
      });

      const result = await handler(mockEvent, mockContext);

      expect(result.statusCode).toBe(500);
      expect(message).toHaveBeenCalledWith('error', expect.stringContaining('Token expired'));
    });

    it('should return 500 when secrets manager fails', async () => {
      jwt.decode.mockReturnValue({
        sub: 'user-123',
        email: '<EMAIL>',
        exp: Math.floor(Date.now() / 1000) + 3600
      });

      enhancedSecretsManager.getJWTSecrets.mockRejectedValue(new Error('Secrets manager error'));

      const result = await handler(mockEvent, mockContext);

      expect(result.statusCode).toBe(500);
      expect(message).toHaveBeenCalledWith('error', expect.stringContaining('Secrets manager error'));
    });

    it('should return 500 when JWT signing fails', async () => {
      jwt.decode.mockReturnValue({
        sub: 'user-123',
        email: '<EMAIL>',
        exp: Math.floor(Date.now() / 1000) + 3600
      });

      enhancedSecretsManager.getJWTSecrets.mockResolvedValue({
        JWT_SECRET: 'mock-secret'
      });

      jwt.sign.mockImplementation(() => {
        throw new Error('JWT signing failed');
      });

      const result = await handler(mockEvent, mockContext);

      expect(result.statusCode).toBe(500);
      expect(message).toHaveBeenCalledWith('error', expect.stringContaining('JWT signing failed'));
    });
  });

  describe('token payload validation', () => {
    it('should create correct session token payload', async () => {
      const mockDecodedToken = {
        sub: 'user-123',
        email: '<EMAIL>',
        'cognito:username': 'john.doe',
        exp: Math.floor(Date.now() / 1000) + 3600
      };

      jwt.decode.mockReturnValue(mockDecodedToken);
      jwt.sign.mockReturnValueOnce('session-token').mockReturnValueOnce('refresh-token');

      enhancedSecretsManager.getJWTSecrets.mockResolvedValue({
        JWT_SECRET: 'mock-secret'
      });

      await handler(mockEvent, mockContext);

      expect(jwt.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          sub: 'user-123',
          email: '<EMAIL>',
          cognito_username: 'john.doe',
          iss: 'dsm-backend',
          aud: 'dsm-frontend',
          userInfo: expect.any(Object)
        }),
        'mock-secret'
      );
    });

    it('should create correct refresh token payload', async () => {
      jwt.decode.mockReturnValue({
        sub: 'user-123',
        email: '<EMAIL>',
        exp: Math.floor(Date.now() / 1000) + 3600
      });

      jwt.sign.mockReturnValueOnce('session-token').mockReturnValueOnce('refresh-token');

      enhancedSecretsManager.getJWTSecrets.mockResolvedValue({
        JWT_SECRET: 'mock-secret'
      });

      await handler(mockEvent, mockContext);

      expect(jwt.sign).toHaveBeenNthCalledWith(2,
        expect.objectContaining({
          sub: 'user-123',
          type: 'refresh'
        }),
        'mock-secret'
      );
    });
  });
});
