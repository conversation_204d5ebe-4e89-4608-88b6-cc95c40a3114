import { makeSES } from '../../shared/services/ses-service';
import { errorValidator } from '../../shared/validators'
import * as Yup from 'yup'

import {
    STATUS_CODE,
    responseWithError,
    responseWithSuccess,
    responseWithBadRequest
} from '../../shared/response'

const ses = makeSES({
    region: process.env.AWS_REGION_LOCATION,
    credentials: {
        accessKeyId: process.env.ACCESS_KEY_ID_USER_ROOT,
        secretAccessKey: process.env.SECRET_KEY_ID_USER_ROOT
    }
})

exports.handler = async () => {
    try {
        const emails = await ses.listIdentities().promise()

        const filterEmails = emails.Identities.filter(email => !email.includes('teste') && email.includes('@'))
        filterEmails.sort()
        return responseWithSuccess(filterEmails, 'Emails encontrado com sucesso')
    } catch (error) {
        const { statusCode, message } = errorValidator(error)

        if (STATUS_CODE.BAD_REQUEST === statusCode)
            return responseWithBadRequest(message)

        return responseWithError(message)
    }
};
