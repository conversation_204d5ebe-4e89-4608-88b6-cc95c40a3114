update-proposals-persist-data:
  handler: src/functions/proposals/update-persist-data.handler
  name: ${env:STAGE}-update-proposals-persist-data${env:VERSION}
  description: Função responsavel por persistir os dados de propostas não concluidas
  memorySize: 128
  events:
    - http:
        path: /proposals/persit
        method: put
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

get-proposals:
  handler: src/functions/proposals/get-proposals.handler
  name: ${env:STAGE}-get-proposals${env:VERSION}
  description: Função para buscar as propostas do DSM
  memorySize: 128
  timeout: 30
  events:
    - http:
        path: /proposals
        method: get
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}


