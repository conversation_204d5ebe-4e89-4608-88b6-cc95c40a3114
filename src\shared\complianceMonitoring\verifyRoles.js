import { verifyAccountType } from "./verifyControllers/verifyAccountType";
import { verifyCostExplorer } from "./verifyControllers/verifyCostExplorer";
import { verifyDarede } from "./verifyControllers/verifyDarede";
import { verifyDenyCloseAccount } from "./verifyControllers/verifyDenyCloseAccount";
import { verifyDaredeFull } from "./verifyControllers/verifyDaredeFull";
import { verifyMFA } from "./verifyControllers/verifyMFA";
import { logStates } from "./logStates";
import { accessKeysCustomerAccount } from "../accessKeysCustomerAccount";

const AWS = require("aws-sdk");
const region = "us-east-1";

let roleDarede = false;
let roleDaredeFull = false;
let policyDenyCloseAccountDaredeFull = false;
let mfa = false;
let costExplorer = false;
let tagCritical = false;

export async function verifyRoles(account) {
  console.log("\nVerifying Roles...");

  const newSession = await accessKeysCustomerAccount(account);

  let obj = {};

  if (newSession) {
    const iam = new AWS.IAM({
      region,
      credentials: {
        accessKeyId: newSession.access_key_id,
        secretAccessKey: newSession.secret_key_id,
        sessionToken: newSession.session_token,
      },
    });

    obj = await iam
      .listRoles({ MaxItems: 1000 })
      .promise()
      .then(async (value) => {
        const roles = value;
        console.log("roles.Roles.length: ", roles.Roles.length);

        roleDarede = await verifyDarede(iam, roles);
        roleDaredeFull = await verifyDaredeFull(iam, roles);
        mfa = await verifyMFA(account);
        costExplorer = await verifyCostExplorer(account);
        policyDenyCloseAccountDaredeFull = await verifyDenyCloseAccount(
          iam,
          "darede-full"
        );
        tagCritical = await verifyAccountType(account);

        await logStates(mfa, costExplorer, tagCritical, account);

        const obj = {
          account: account,
          rolesLength: roles.Roles.length,
          roleDarede: roleDarede,
          roleDaredeFull: roleDaredeFull,
          policyDenyCloseAccountDaredeFull: policyDenyCloseAccountDaredeFull,
          mfaRootAccount: mfa,
          costExplorer: costExplorer,
          tagCritical: tagCritical,
        };

        return obj;
      });

    return obj;
  } else {
    console.log("it was not possible to assume role on this account: ", {
      account,
    });

    obj = {
      account: account,
      rolesLength: 0,
      roleDarede: false,
      roleDaredeFull: false,
      policyDenyCloseAccountDaredeFull: false,
      mfaRootAccount: false,
      costExplorer: false,
      tagCritical: false,
    };

    return obj;
  }
}
