compliance-status-monitoring:
  handler: src/functions/compliance/complianceStatusMonitoring.handler
  name: ${env:STAGE}-compliance-invoke${env:VERSION}
  description: Função que invoka o lambda que verifica os status das contas dos clientes
  memorySize: 128
  timeout: 900

compliance-check-accounts-status:
  handler: src/functions/compliance/checkAccountsStatus.handler
  name: ${env:STAGE}-compliance-check-accounts-status${env:VERSION}
  description: Função que verifica cada um dos itens de compliance na conta do cliente e atualiza os status na tabela STAGE-compliance-monitoring
  memorySize: 128
  timeout: 900

disparity-contracts-invoke:
  handler: src/functions/compliance/disparityContractsInvoke.handler
  name: ${env:STAGE}-disparity-contracts-invoke${env:VERSION}
  description: Função que realiza o invoke do script de compliance de disparidades de contratos
  memorySize: 128

