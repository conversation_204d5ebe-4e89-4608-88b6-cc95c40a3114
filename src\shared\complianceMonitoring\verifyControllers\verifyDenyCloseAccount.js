export async function verifyDenyCloseAccount(iam, roleName) {
  console.log("\nVerifying DenyLeaveOrganization policy...");

  let policies = await iam.listRolePolicies({ RoleName: roleName }).promise();

  const existsDenyLeaveAccount = policies?.AttachedPolicies?.find(
    (p) => p.toLowerCase() === "denycloseaccount"
  );

  if (existsDenyLeaveAccount === undefined) {
    console.log("Policy DenyCloseAccount is not defined in this role!");
    return false;
  } else {
    console.log("Policy DenyCloseAccount is defined in this role!");
    return true;
  }
}
