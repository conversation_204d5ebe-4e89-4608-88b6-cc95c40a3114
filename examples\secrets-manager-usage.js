/**
 * Exemplos de uso do serviço de Secrets Manager
 * Demonstra como usar o serviço em diferentes cenários
 */

const { 
  getSecret, 
  getJWTCredentials, 
  getAPICredentials, 
  getMultipleSecrets,
  cache 
} = require('../src/shared/secrets/secrets-manager');

const { 
  generateToken, 
  verifyToken, 
  generateRefreshToken,
  getJWTConfig 
} = require('../src/shared/auth/jwt-utils-secrets');

const { 
  createAPIClient, 
  apiGet, 
  testAPIConnection,
  getAPIConfig 
} = require('../src/shared/api/api-client-secrets');

const { 
  withSecureAuth, 
  withRoleAuth, 
  withPermissionAuth 
} = require('../src/shared/auth/auth-middleware-secrets');

/**
 * Exemplo 1: Uso básico do Secrets Manager
 */
async function exemploBasicoSecretsManager() {
  console.log('\n=== Exemplo 1: Uso Básico do Secrets Manager ===');
  
  try {
    // Recupera credenciais JWT
    const jwtCredentials = await getJWTCredentials();
    console.log('✅ Credenciais JWT recuperadas:', {
      algorithm: jwtCredentials.algorithm,
      issuer: jwtCredentials.issuer,
      expiresIn: jwtCredentials.expiresIn
    });
    
    // Recupera credenciais de API
    const apiCredentials = await getAPICredentials();
    console.log('✅ Credenciais de API recuperadas:', {
      baseUrl: apiCredentials.baseUrl,
      hasApiKey: !!apiCredentials.apiKey,
      timeout: apiCredentials.timeout
    });
    
    // Estatísticas do cache
    console.log('📊 Estatísticas do cache:', cache.stats());
    
  } catch (error) {
    console.error('❌ Erro no exemplo básico:', error.message);
  }
}

/**
 * Exemplo 2: Geração e verificação de tokens JWT
 */
async function exemploJWTComSecretsManager() {
  console.log('\n=== Exemplo 2: JWT com Secrets Manager ===');
  
  try {
    // Payload do usuário
    const userPayload = {
      sub: 'user-123',
      userId: 'user-123',
      email: '<EMAIL>',
      role: 'admin',
      permissions: ['read', 'write', 'delete'],
      name: 'Usuário Exemplo'
    };
    
    // Gera token de acesso
    const accessToken = await generateToken(userPayload);
    console.log('✅ Token de acesso gerado (primeiros 50 chars):', accessToken.substring(0, 50) + '...');
    
    // Gera refresh token
    const refreshToken = await generateRefreshToken(userPayload);
    console.log('✅ Refresh token gerado (primeiros 50 chars):', refreshToken.substring(0, 50) + '...');
    
    // Verifica token
    const decoded = await verifyToken(accessToken);
    console.log('✅ Token verificado com sucesso:', {
      email: decoded.email,
      role: decoded.role,
      expiresAt: new Date(decoded.exp * 1000).toISOString()
    });
    
  } catch (error) {
    console.error('❌ Erro no exemplo JWT:', error.message);
  }
}

/**
 * Exemplo 3: Cliente de API com autenticação
 */
async function exemploAPIClienteComSecretsManager() {
  console.log('\n=== Exemplo 3: Cliente de API com Secrets Manager ===');
  
  try {
    // Testa conectividade
    const connectionTest = await testAPIConnection();
    console.log('🔗 Teste de conectividade:', connectionTest);
    
    if (connectionTest.success) {
      // Cria cliente personalizado
      const client = await createAPIClient({
        timeout: 15000,
        headers: {
          'X-Custom-Header': 'exemplo'
        }
      });
      
      console.log('✅ Cliente API criado com sucesso');
      
      // Exemplo de requisição GET (ajuste o endpoint conforme necessário)
      try {
        const data = await apiGet('/api/status');
        console.log('✅ Requisição GET bem-sucedida:', data);
      } catch (apiError) {
        console.log('ℹ️ Endpoint /api/status não disponível (esperado em exemplo)');
      }
    }
    
  } catch (error) {
    console.error('❌ Erro no exemplo de API:', error.message);
  }
}

/**
 * Exemplo 4: Middleware de autenticação em Lambda
 */
function exemploMiddlewareAutenticacao() {
  console.log('\n=== Exemplo 4: Middleware de Autenticação ===');
  
  // Handler protegido básico
  const handlerProtegido = withSecureAuth(async (event, context) => {
    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Acesso autorizado!',
        user: event.user.email,
        timestamp: new Date().toISOString()
      })
    };
  });
  
  // Handler que requer role específica
  const handlerAdmin = withRoleAuth(async (event, context) => {
    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Área administrativa',
        user: event.user.email,
        role: event.user.role
      })
    };
  }, 'admin');
  
  // Handler que requer permissões específicas
  const handlerComPermissoes = withPermissionAuth(async (event, context) => {
    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Operação autorizada',
        user: event.user.email,
        permissions: event.user.permissions
      })
    };
  }, ['read', 'write']);
  
  console.log('✅ Middlewares de autenticação configurados:');
  console.log('  - handlerProtegido: Requer autenticação básica');
  console.log('  - handlerAdmin: Requer role "admin"');
  console.log('  - handlerComPermissoes: Requer permissões ["read", "write"]');
  
  return {
    handlerProtegido,
    handlerAdmin,
    handlerComPermissoes
  };
}

/**
 * Exemplo 5: Recuperação de múltiplos segredos
 */
async function exemploMultiplosSegredos() {
  console.log('\n=== Exemplo 5: Múltiplos Segredos ===');
  
  try {
    const secretIds = [
      process.env.JWT_DECRIPTION_CREDENTIALS,
      process.env.DSM_API_SECREAT_MANAGER
    ].filter(Boolean);
    
    if (secretIds.length === 0) {
      console.log('ℹ️ Nenhum segredo configurado para teste');
      return;
    }
    
    const result = await getMultipleSecrets(secretIds);
    
    console.log(`✅ ${Object.keys(result.secrets).length} segredos recuperados com sucesso`);
    
    if (result.errors.length > 0) {
      console.log(`⚠️ ${result.errors.length} segredos falharam:`, result.errors);
    }
    
    // Lista segredos recuperados (sem mostrar valores sensíveis)
    Object.keys(result.secrets).forEach(secretId => {
      const secret = result.secrets[secretId];
      console.log(`  - ${secretId}: ${Object.keys(secret).filter(k => k !== '_metadata').length} campos`);
    });
    
  } catch (error) {
    console.error('❌ Erro ao recuperar múltiplos segredos:', error.message);
  }
}

/**
 * Exemplo 6: Gerenciamento de cache
 */
async function exemploGerenciamentoCache() {
  console.log('\n=== Exemplo 6: Gerenciamento de Cache ===');
  
  try {
    // Mostra estatísticas iniciais
    console.log('📊 Estatísticas iniciais do cache:', cache.stats());
    
    // Força carregamento de credenciais
    await getJWTCredentials();
    await getAPICredentials();
    
    // Mostra estatísticas após carregamento
    console.log('📊 Estatísticas após carregamento:', cache.stats());
    
    // Limpa cache
    cache.clear();
    console.log('🧹 Cache limpo');
    
    // Mostra estatísticas após limpeza
    console.log('📊 Estatísticas após limpeza:', cache.stats());
    
  } catch (error) {
    console.error('❌ Erro no gerenciamento de cache:', error.message);
  }
}

/**
 * Exemplo 7: Tratamento de erros e fallbacks
 */
async function exemploTratamentoErros() {
  console.log('\n=== Exemplo 7: Tratamento de Erros ===');
  
  try {
    // Tenta recuperar um segredo inexistente
    try {
      await getSecret('segredo-inexistente');
    } catch (error) {
      console.log('✅ Erro capturado corretamente para segredo inexistente:', error.message);
    }
    
    // Testa fallback para JWT (se JWT_SECRET estiver definido)
    if (process.env.JWT_SECRET) {
      // Temporariamente remove a variável de ambiente do Secrets Manager
      const originalSecretId = process.env.JWT_DECRIPTION_CREDENTIALS;
      delete process.env.JWT_DECRIPTION_CREDENTIALS;
      
      try {
        const config = await getJWTConfig();
        console.log('✅ Fallback para JWT_SECRET funcionou:', {
          algorithm: config.algorithm,
          issuer: config.issuer
        });
      } catch (error) {
        console.log('❌ Fallback para JWT falhou:', error.message);
      } finally {
        // Restaura variável de ambiente
        if (originalSecretId) {
          process.env.JWT_DECRIPTION_CREDENTIALS = originalSecretId;
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Erro no exemplo de tratamento de erros:', error.message);
  }
}

/**
 * Executa todos os exemplos
 */
async function executarTodosExemplos() {
  console.log('🚀 Iniciando exemplos de uso do Secrets Manager\n');
  
  await exemploBasicoSecretsManager();
  await exemploJWTComSecretsManager();
  await exemploAPIClienteComSecretsManager();
  exemploMiddlewareAutenticacao();
  await exemploMultiplosSegredos();
  await exemploGerenciamentoCache();
  await exemploTratamentoErros();
  
  console.log('\n✅ Todos os exemplos executados!');
}

// Executa exemplos se o arquivo for executado diretamente
if (require.main === module) {
  executarTodosExemplos().catch(console.error);
}

module.exports = {
  exemploBasicoSecretsManager,
  exemploJWTComSecretsManager,
  exemploAPIClienteComSecretsManager,
  exemploMiddlewareAutenticacao,
  exemploMultiplosSegredos,
  exemploGerenciamentoCache,
  exemploTratamentoErros,
  executarTodosExemplos
};
