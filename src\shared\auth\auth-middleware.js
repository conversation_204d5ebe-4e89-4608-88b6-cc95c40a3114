import { verifyToken, isTokenNearExpiry, generateToken } from './jwt-utils.js';
import { getAccessTokenFromCookies, getCookieConfig, createAuthCookieHeaders } from './cookie-utils.js';
import { responseWithError, responseWithBadRequest } from '../response.js';

/**
 * Extrai token do header Authorization ou cookies
 * @param {Object} event - Evento Lambda
 * @returns {string|null} Token extraído ou null
 */
const extractToken = (event) => {
  // Primeiro tenta extrair dos cookies (prioridade)
  const cookieToken = getAccessTokenFromCookies(event);
  if (cookieToken) {
    return cookieToken;
  }
  
  // Fallback para Authorization header
  const authHeader = event.headers?.Authorization || event.headers?.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  return null;
};

/**
 * Middleware de autenticação para Lambda functions
 * @param {Object} event - <PERSON><PERSON>
 * @param {Object} context - Contexto <PERSON>da
 * @param {Function} next - Próxima função (opcional para compatibilidade)
 * @returns {Object} Resultado da autenticação
 */
export const authMiddleware = async (event, context, next = null) => {
  try {
    const token = extractToken(event);
    
    if (!token) {
      return {
        isAuthorized: false,
        error: 'Token de autenticação não fornecido',
        response: responseWithBadRequest('Token de autenticação requerido')
      };
    }
    
    // Verifica e decodifica o token
    let decoded;
    try {
      decoded = await verifyToken(token);
    } catch (error) {
      return {
        isAuthorized: false,
        error: error.message,
        response: responseWithBadRequest(`Token inválido: ${error.message}`)
      };
    }
    
    // Verifica se o token está próximo do vencimento
    const needsRefresh = isTokenNearExpiry(token, 30); // 30 minutos antes do vencimento
    
    // Adiciona informações do usuário ao evento para uso posterior
    event.user = {
      id: decoded.sub || decoded.userId,
      email: decoded.email,
      role: decoded.role,
      permissions: decoded.permissions || [],
      ...decoded
    };
    
    // Adiciona flag indicando se o token precisa ser renovado
    event.tokenNeedsRefresh = needsRefresh;
    
    return {
      isAuthorized: true,
      user: event.user,
      needsRefresh,
      token: token
    };
    
  } catch (error) {
    console.error('Erro no middleware de autenticação:', error);
    return {
      isAuthorized: false,
      error: 'Erro interno de autenticação',
      response: responseWithError('Erro interno de autenticação')
    };
  }
};

/**
 * Wrapper para funções Lambda que requer autenticação
 * @param {Function} handler - Handler da função Lambda
 * @param {Object} options - Opções de configuração
 * @returns {Function} Handler wrapeado com autenticação
 */
export const withAuth = (handler, options = {}) => {
  const {
    requireAuth = true,
    autoRefresh = true,
    requiredPermissions = [],
    requiredRole = null
  } = options;
  
  return async (event, context) => {
    try {
      // Se autenticação não é requerida, executa diretamente
      if (!requireAuth) {
        return await handler(event, context);
      }
      
      // Executa middleware de autenticação
      const authResult = await authMiddleware(event, context);
      
      if (!authResult.isAuthorized) {
        return authResult.response;
      }
      
      // Verifica permissões se especificadas
      if (requiredPermissions.length > 0) {
        const userPermissions = event.user.permissions || [];
        const hasPermission = requiredPermissions.every(permission => 
          userPermissions.includes(permission)
        );
        
        if (!hasPermission) {
          return responseWithBadRequest('Permissões insuficientes');
        }
      }
      
      // Verifica role se especificada
      if (requiredRole && event.user.role !== requiredRole) {
        return responseWithBadRequest('Role insuficiente para esta operação');
      }
      
      // Executa o handler original
      const result = await handler(event, context);
      
      // Se auto-refresh está habilitado e token precisa ser renovado
      if (autoRefresh && authResult.needsRefresh && result.statusCode === 200) {
        try {
          // Gera novo token
          const newToken = generateToken({
            sub: event.user.id,
            email: event.user.email,
            role: event.user.role,
            permissions: event.user.permissions
          });
          
          // Adiciona cookie com novo token na resposta
          const newCookieHeaders = createAuthCookieHeaders(newToken, null);
          
          // Adiciona ou atualiza headers de resposta
          if (!result.headers) {
            result.headers = {};
          }
          
          // Adiciona novos cookies à resposta
          if (result.headers['Set-Cookie']) {
            if (Array.isArray(result.headers['Set-Cookie'])) {
              result.headers['Set-Cookie'].push(...newCookieHeaders);
            } else {
              result.headers['Set-Cookie'] = [result.headers['Set-Cookie'], ...newCookieHeaders];
            }
          } else {
            result.headers['Set-Cookie'] = newCookieHeaders;
          }
          
          console.log('Token renovado automaticamente para usuário:', event.user.id);
          
        } catch (refreshError) {
          console.error('Erro ao renovar token automaticamente:', refreshError);
          // Não falha a requisição, apenas loga o erro
        }
      }
      
      return result;
      
    } catch (error) {
      console.error('Erro no wrapper de autenticação:', error);
      return responseWithError('Erro interno de autenticação');
    }
  };
};

/**
 * Middleware simplificado apenas para verificação de token
 * @param {Object} event - Evento Lambda
 * @returns {Object} Resultado da verificação
 */
export const verifyAuthToken = async (event) => {
  const token = extractToken(event);
  
  if (!token) {
    return { valid: false, error: 'Token não fornecido' };
  }
  
  try {
    const decoded = await verifyToken(token);
    return {
      valid: true,
      user: decoded,
      needsRefresh: isTokenNearExpiry(token, 30)
    };
  } catch (error) {
    return { valid: false, error: error.message };
  }
};
