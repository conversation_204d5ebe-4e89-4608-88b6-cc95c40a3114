import * as Yup from "yup";
import { readPaginate } from "../../model/dynamo";
import { parseQueryString, parseHeaders } from "../../shared/parsers";

import {
  STATUS_CODE,
  responseWithError,
  responseWithSuccess,
  responseWithBadRequest,
} from "../../shared/response";
import { errorValidator } from "../../shared/validators";

let bodyValidade = Yup.object().shape({
  nextPage: Yup.string().default(""),
  dynamodb: Yup.string().required("Invalid dynamodb"),
});

export const handler = async (event, context) => {
  console.log({ event });
  const { nextPage } = parseQueryString(event);
  const { dynamodb } = parseHeaders(event);

  try {
    const params = await bodyValidade.validate(
      { nextPage, dynamodb },
      { abortEarly: false }
    );

    const { data, lastEvaluatedKey } = await readPaginate(params);

    return responseWithSuccess(
      {
        data,
        nextPage: lastEvaluatedKey ? lastEvaluatedKey.id : "",
      },
      "Query successfully"
    );
  } catch (error) {
    const { statusCode, message } = errorValidator(error);

    if (STATUS_CODE.BAD_REQUEST === statusCode)
      return responseWithBadRequest(message);

    return responseWithError(message);
  }
};