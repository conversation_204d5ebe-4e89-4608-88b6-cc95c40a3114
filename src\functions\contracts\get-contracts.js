import { ContractEntity } from "../../entities/contract-entity";
import { parseQueryString } from "../../shared/parsers";
import * as Yup from "yup";

import {
  STATUS_CODE,
  responseWithError,
  responseWithSuccess,
  responseWithBadRequest,
  responseWithNotFound,
} from "../../shared/response";

import { errorValidator } from "../../shared/validators";

import { makeDynamoDB } from "../../shared/services/dynamo-service";

let bodyValidade = Yup.object().shape({
  active: Yup.number().default(1),
  type_hours: Yup.string().default(""),
  customer_id: Yup.string().default(""),
  customer_itsm: Yup.string().default(""),
});

const clientDynamo = makeDynamoDB();
export const handler = async (event, context) => {
  try {
    const { active, type_hours, customer_id, customer_itsm } =
      parseQueryString(event);

    const params = await bodyValidade.validate(
      { active, type_hours, customer_id, customer_itsm },
      { abortEarly: false }
    );

    const entity = new ContractEntity(clientDynamo);
    const inputGetContracts = entity.generateInputGetContracts(params);
    console.log(inputGetContracts);
    const customers = await entity.getContracts(inputGetContracts);

    if (customers.length === 0)
      return responseWithNotFound("Contracts not found");

    return responseWithSuccess(customers, "Query successfully");
  } catch (error) {
    const { statusCode, message } = errorValidator(error);

    if (STATUS_CODE.BAD_REQUEST === statusCode)
      return responseWithBadRequest(message);

    return responseWithError(message);
  }
};
