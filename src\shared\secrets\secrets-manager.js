/**
 * Serviço para gerenciar credenciais e segredos usando AWS Secrets Manager
 * Implementa cache, retentativas e tratamento de erros
 */

const AWS = require('aws-sdk');

// Configuração do cliente Secrets Manager
const secretsManager = new AWS.SecretsManager({
  region: process.env.AWS_REGION_LOCATION || 'us-east-1'
});

// Cache em memória para segredos
const secretsCache = new Map();

// Configurações de cache e retry
const CACHE_CONFIG = {
  defaultTTL: 15 * 60 * 1000, // 15 minutos
  jwtTTL: 30 * 60 * 1000,     // 30 minutos para JWT
  apiTTL: 60 * 60 * 1000,     // 1 hora para APIs
  maxSize: 50                  // Máximo de segredos em cache
};

const RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000,    // 1 segundo
  maxDelay: 10000,    // 10 segundos
  backoffMultiplier: 2
};

/**
 * Classe para gerenciar cache de segredos
 */
class SecretsCache {
  constructor() {
    this.cache = new Map();
    this.accessTimes = new Map();
  }

  /**
   * Obtém um segredo do cache
   * @param {string} key - Chave do segredo
   * @returns {Object|null} Segredo ou null se não encontrado/expirado
   */
  get(key) {
    const entry = this.cache.get(key);
    if (!entry) return null;

    const now = Date.now();
    if (now > entry.expiresAt) {
      this.cache.delete(key);
      this.accessTimes.delete(key);
      // console.log(`Cache expirado para segredo: ${key}`);
      return null;
    }

    this.accessTimes.set(key, now);
    // console.log(`Cache hit para segredo: ${key}`);
    return entry.value;
  }

  /**
   * Armazena um segredo no cache
   * @param {string} key - Chave do segredo
   * @param {Object} value - Valor do segredo
   * @param {number} ttl - Tempo de vida em milissegundos
   */
  set(key, value, ttl = CACHE_CONFIG.defaultTTL) {
    const now = Date.now();
    
    // Remove entradas antigas se o cache estiver cheio
    if (this.cache.size >= CACHE_CONFIG.maxSize) {
      this._evictOldest();
    }

    this.cache.set(key, {
      value,
      expiresAt: now + ttl,
      createdAt: now
    });
    
    this.accessTimes.set(key, now);
    // console.log(`Segredo armazenado em cache: ${key} (TTL: ${ttl}ms)`);
  }

  /**
   * Remove a entrada mais antiga do cache
   */
  _evictOldest() {
    let oldestKey = null;
    let oldestTime = Date.now();

    for (const [key, time] of this.accessTimes) {
      if (time < oldestTime) {
        oldestTime = time;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.accessTimes.delete(oldestKey);
      // console.log(`Cache eviction: removido ${oldestKey}`);
    }
  }

  /**
   * Limpa o cache
   */
  clear() {
    this.cache.clear();
    this.accessTimes.clear();
    // console.log('Cache de segredos limpo');
  }

  /**
   * Obtém estatísticas do cache
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: CACHE_CONFIG.maxSize,
      entries: Array.from(this.cache.keys())
    };
  }
}

// Instância global do cache
const cache = new SecretsCache();

/**
 * Implementa retry com backoff exponencial
 * @param {Function} fn - Função a ser executada
 * @param {number} retries - Número de tentativas restantes
 * @returns {Promise} Resultado da função
 */
async function withRetry(fn, retries = RETRY_CONFIG.maxRetries) {
  try {
    return await fn();
  } catch (error) {
    // Não faz retry para erros de permissão
    if (error.code === 'AccessDeniedException' || error.code === 'UnauthorizedOperation') {
      console.error(`❌ Erro de permissão no Secrets Manager: ${error.message}`);
      throw error;
    }

    if (retries <= 0) {
      throw error;
    }

    const delay = Math.min(
      RETRY_CONFIG.baseDelay * Math.pow(RETRY_CONFIG.backoffMultiplier, RETRY_CONFIG.maxRetries - retries),
      RETRY_CONFIG.maxDelay
    );

    // console.log(`⚠️ Erro ao acessar Secrets Manager, tentando novamente em ${delay}ms. Tentativas restantes: ${retries - 1}`);
    // console.error('Erro:', error.message);

    await new Promise(resolve => setTimeout(resolve, delay));
    return withRetry(fn, retries - 1);
  }
}

/**
 * Recupera um segredo do AWS Secrets Manager
 * @param {string} secretId - ID ou ARN do segredo
 * @param {number} ttl - Tempo de vida do cache em milissegundos
 * @returns {Promise<Object>} Segredo decodificado
 */
async function getSecret(secretId, ttl = CACHE_CONFIG.defaultTTL) {
  try {
    // Verifica cache primeiro
    const cached = cache.get(secretId);
    if (cached) {
      return cached;
    }

    // console.log(`Recuperando segredo do AWS Secrets Manager: ${secretId}`);

    const result = await withRetry(async () => {
      return await secretsManager.getSecretValue({ SecretId: secretId }).promise();
    });

    let secret;
    if (result.SecretString) {
      try {
        secret = JSON.parse(result.SecretString);
      } catch (parseError) {
        secret = { value: result.SecretString };
      }
    } else if (result.SecretBinary) {
      secret = { value: Buffer.from(result.SecretBinary, 'base64').toString() };
    } else {
      throw new Error('Segredo não contém SecretString nem SecretBinary');
    }

    secret._metadata = {
      secretId,
      retrievedAt: new Date().toISOString(),
      versionId: result.VersionId,
      versionStage: result.VersionStage
    };

    cache.set(secretId, secret, ttl);

    // console.log(`Segredo recuperado com sucesso: ${secretId}`);
    return secret;

  } catch (error) {
    console.error(`Erro ao recuperar segredo ${secretId}:`, error);
    throw new Error(`Falha ao recuperar segredo ${secretId}: ${error.message}`);
  }
}

/**
 * Mapeia campos alternativos do segredo JWT
 * @param {Object} secret - Objeto do segredo
 * @returns {Object} Credenciais mapeadas
 */
function mapJWTFields(secret) {
  let jwtSecret = secret.secret;
  let algorithm = secret.algorithm;

  if (!jwtSecret) {
    jwtSecret = secret.jwt_secret || secret.jwtSecret || secret.key || secret.JWT_SECRET;
  }

  if (!algorithm) {
    algorithm = secret.alg || secret.JWT_ALGORITHM || 'HS256';
  }

  // console.log(`🔑 JWT Secret encontrado: ${!!jwtSecret ? 'SIM' : 'NÃO'}`);
  // console.log(`🔐 Algorithm encontrado: ${algorithm || 'PADRÃO (HS256)'}`);

  return {
    secret: jwtSecret,
    algorithm: algorithm || 'HS256',
    issuer: secret.issuer || secret.iss || 'dsm-api',
    audience: secret.audience || secret.aud || 'dsm-clients',
    expiresIn: secret.expiresIn || secret.exp || process.env.JWT_EXPIRES_IN || '8h',
    refreshExpiresIn: secret.refreshExpiresIn || secret.refresh_exp || process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    _metadata: secret._metadata,
    _mappedFields: {
      secretFrom: Object.keys(secret).find(k => [
        'secret', 'jwt_secret', 'jwtSecret', 'key', 'JWT_SECRET'
      ].includes(k)),
      algorithmFrom: Object.keys(secret).find(k => [
        'algorithm', 'alg', 'JWT_ALGORITHM'
      ].includes(k))
    }
  };
}

/**
 * Recupera credenciais JWT do segredo específico
 * @returns {Promise<Object>} Credenciais JWT
 */
async function getJWTCredentials() {
  const secretId = process.env.JWT_DECRIPTION_CREDENTIALS;
  
  if (!secretId) {
    throw new Error('JWT_DECRIPTION_CREDENTIALS não está definido nas variáveis de ambiente');
  }

  try {
    const secret = await getSecret(secretId, CACHE_CONFIG.jwtTTL);
    
    const mappedCredentials = mapJWTFields(secret);

    const requiredFields = ['secret'];
    const missingFields = requiredFields.filter(field => !mappedCredentials[field]);

    if (missingFields.length > 0) {
      // console.error('🔍 Estrutura original do segredo JWT:', JSON.stringify(secret, null, 2));
      // console.error('📋 Campos disponíveis:', Object.keys(secret).filter(k => k !== '_metadata'));
      // console.error('🔄 Mapeamento tentado:', mappedCredentials._mappedFields);
      throw new Error(`Campos obrigatórios ausentes no segredo JWT: ${missingFields.join(', ')}. Campos disponíveis: ${Object.keys(secret).filter(k => k !== '_metadata').join(', ')}`);
    }

    return mappedCredentials;

  } catch (error) {
    console.error('Erro ao recuperar credenciais JWT:', error);
    throw error;
  }
}

/**
 * Recupera credenciais de API do segredo específico
 * @returns {Promise<Object>} Credenciais de API
 */
async function getAPICredentials() {
  const secretId = process.env.DSM_API_SECREAT_MANAGER;
  
  if (!secretId) {
    throw new Error('DSM_API_SECREAT_MANAGER não está definido nas variáveis de ambiente');
  }

  try {
    const secret = await getSecret(secretId, CACHE_CONFIG.apiTTL);
    
    const requiredFields = ['username', 'password'];
    const missingFields = requiredFields.filter(field => !secret[field]);
    
    if (missingFields.length > 0) {
      throw new Error(`Campos obrigatórios ausentes no segredo API: ${missingFields.join(', ')}`);
    }

    return {
      username: secret.username,
      password: secret.password,
      apiKey: secret.apiKey,
      baseUrl: secret.baseUrl || process.env.DSM_API_URL,
      timeout: secret.timeout || 30000,
      _metadata: secret._metadata
    };

  } catch (error) {
    console.error('Erro ao recuperar credenciais de API:', error);
    throw error;
  }
}

/**
 * Recupera múltiplos segredos em paralelo
 * @param {Array<string>} secretIds - Lista de IDs de segredos
 * @returns {Promise<Object>} Objeto com segredos indexados por ID
 */
async function getMultipleSecrets(secretIds) {
  try {
    const promises = secretIds.map(secretId => 
      getSecret(secretId).then(secret => ({ secretId, secret }))
    );

    const results = await Promise.allSettled(promises);
    const secrets = {};
    const errors = [];

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        const { secretId, secret } = result.value;
        secrets[secretId] = secret;
      } else {
        errors.push({
          secretId: secretIds[index],
          error: result.reason.message
        });
      }
    });

    if (errors.length > 0) {
      console.warn('Alguns segredos falharam ao carregar:', errors);
    }

    return { secrets, errors };

  } catch (error) {
    console.error('Erro ao recuperar múltiplos segredos:', error);
    throw error;
  }
}

module.exports = {
  getSecret,
  getJWTCredentials,
  getAPICredentials,
  getMultipleSecrets,
  cache: {
    clear: () => cache.clear(),
    stats: () => cache.getStats()
  },
  config: {
    CACHE_CONFIG,
    RETRY_CONFIG
  }
};
