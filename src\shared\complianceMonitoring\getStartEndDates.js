export async function getStartEndDates() {
  let today = new Date();

  const startMonth = today.getMonth();
  const endMonth = startMonth + 1;

  let nextDate = today;
  nextDate.setMonth(endMonth);

  const start = new Date();

  const endFormated = `${nextDate.getFullYear()}-${
    nextDate.getMonth() + 1 < 9
      ? `0${nextDate.getMonth() + 1}`
      : `${nextDate.getMonth() + 1}`
  }-01`;
  const startFormated = `${start.getFullYear()}-${
    start.getMonth() + 1 < 9
      ? `0${start.getMonth() + 1}`
      : `${start.getMonth() + 1}`
  }-${start.getDate()}`;

  console.log({ endFormated }, { startFormated });

  return { End: endFormated, Start: startFormated };
}
