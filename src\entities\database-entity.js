export class DatabaseEntity {
  clientDynamo = null;
  constructor(clientDynamo) {
    this.clientDynamo = clientDynamo;
  }

  generateReadOneInput({ id, table }) {
    let params = {
      TableName: table,
      Key: {
        id: id,
      },
    };

    return params;
  }

  generateUpdateInput({ id, data, table }) {
    if (!id) throw new Error("id is required");
    if (!data) throw new Error("data is required");
    if (!table) throw new Error("table is required");
    let obj = {};
    Object.entries(data).forEach((o) => {
      if (o[0] !== "id" && o[1] !== undefined && o[1] !== null && o[1] !== "") {
        obj[o[0]] = { Action: "PUT", Value: o[1] };
      }
    });
    let params = {
      TableName: table,
      Key: {
        id: id,
      },
      AttributeUpdates: obj,
    };
    return params;
  }

  async readOne(input) {
    const response = await this.clientDynamo.get(input).promise();
    return response.Item;
  }

  async readCustomersByStatus(input) {
    const scanResults = { Items: [] };
    let items;

    do {
      items = await this.clientDynamo.query(input).promise();
      items.Items.forEach((item) => scanResults.Items.push(item));
      input.ExclusiveStartKey = items.LastEvaluatedKey;
    } while (typeof items.LastEvaluatedKey !== "undefined");

    return scanResults;
  }

  generateReadSwitchRoleByUserInput({ user, account }) {
    let userFormat = user.replace("-", ".");
    let shouldDisplay = 1;
    let params;
    if (account === "all") {
      params = {
        IndexName: "switch_role_display-index",
        TableName: `${process.env.FINOPS_STAGE}-switch-role`,
        ExpressionAttributeNames: {
          "#switch_role_display": "switch_role_display",
        },
        KeyConditionExpression: "#switch_role_display = :switch_role_display",

        FilterExpression: `username = :user`,
        ExpressionAttributeValues: {
          ":switch_role_display": shouldDisplay,
          ":user": userFormat,
        },
      };
    } else {
      params = {
        IndexName: "switch_role_display-index",
        TableName: `${process.env.FINOPS_STAGE}-switch-role`,
        ExpressionAttributeNames: {
          "#switch_role_display": "switch_role_display",
        },
        KeyConditionExpression: "#switch_role_display = :switch_role_display",

        FilterExpression: `username = :user AND account_id = :account`,
        ExpressionAttributeValues: {
          ":switch_role_display": shouldDisplay,
          ":user": userFormat,
          ":account": account,
        },
      };
    }
    return params;
  }

  async getCustomersByScan(table) {
    const params = {
      TableName: table,
    };
    const scanResults = { Items: [] };
    let items;

    do {
      items = await this.clientDynamo.scan(params).promise();
      items.Items.forEach((item) => scanResults.Items.push(item));
      params.ExclusiveStartKey = items.LastEvaluatedKey;
    } while (typeof items.LastEvaluatedKey !== "undefined");

    return scanResults;
  }

  async updateItem(input) {
    const response = await this.clientDynamo.update(input).promise();
    return response;
  }
}
