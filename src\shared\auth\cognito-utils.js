/**
 * Utilitários para integração com Amazon Cognito
 */

const jwt = require('jsonwebtoken');
const AWS = require('aws-sdk');

const cognito = new AWS.CognitoIdentityServiceProvider({
  region: process.env.AWS_REGION_LOCATION || 'us-east-1'
});

/**
 * Valida token JWT do Cognito verificando estrutura e assinatura
 * @param {string} token - Token JWT do Cognito
 * @returns {Object} Payload decodificado e validado
 */
async function validateCognitoToken(token) {
  try {
    // Decodifica o token sem verificar assinatura primeiro
    const decoded = jwt.decode(token, { complete: true });
    
    if (!decoded || !decoded.payload) {
      throw new Error('Token não pôde ser decodificado');
    }
    
    const payload = decoded.payload;
    
    // Verifica estrutura básica do token Cognito
    if (!payload.token_use || !payload.sub || !payload.iss) {
      throw new Error('Token não tem estrutura válida de Cognito');
    }
    
    // Verifica se é do User Pool correto
    const expectedIssuer = `https://cognito-idp.${process.env.AWS_REGION_LOCATION}.amazonaws.com/${process.env.USER_POOL_ID}`;
    if (payload.iss !== expectedIssuer) {
      throw new Error('Token não é do User Pool esperado');
    }
    
    // Verifica se não está expirado
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      throw new Error('Token Cognito expirado');
    }
    
    // Verifica tipo de token (deve ser 'id' ou 'access')
    if (!['id', 'access'].includes(payload.token_use)) {
      throw new Error('Tipo de token Cognito inválido');
    }
    
    console.log('Token Cognito validado:', {
      sub: payload.sub,
      email: payload.email,
      token_use: payload.token_use,
      exp: payload.exp,
      iss: payload.iss
    });
    
    return payload;
    
  } catch (error) {
    console.error('Erro ao validar token Cognito:', error);
    throw new Error(`Token Cognito inválido: ${error.message}`);
  }
}

/**
 * Extrai dados do usuário do token Cognito
 * @param {Object} cognitoPayload - Payload do token Cognito
 * @returns {Object} Dados estruturados do usuário
 */
function extractUserDataFromCognito(cognitoPayload) {
  return {
    // Dados básicos
    sub: cognitoPayload.sub,
    userId: cognitoPayload.sub,
    email: cognitoPayload.email || cognitoPayload['cognito:username'],
    emailVerified: cognitoPayload.email_verified || false,
    
    // Nome
    name: cognitoPayload.name || 
          `${cognitoPayload.given_name || ''} ${cognitoPayload.family_name || ''}`.trim() ||
          cognitoPayload.email,
    givenName: cognitoPayload.given_name,
    familyName: cognitoPayload.family_name,
    
    // Cognito específico
    cognitoUsername: cognitoPayload['cognito:username'],
    
    // Roles e permissões (customizáveis)
    role: cognitoPayload['custom:role'] || 
          cognitoPayload.role || 
          'user',
    permissions: cognitoPayload['custom:permissions'] ? 
      (Array.isArray(cognitoPayload['custom:permissions']) ? 
        cognitoPayload['custom:permissions'] : 
        JSON.parse(cognitoPayload['custom:permissions'])) : 
      [],
    
    // Metadados
    tokenUse: cognitoPayload.token_use,
    issuer: cognitoPayload.iss,
    audience: cognitoPayload.aud,
    
    // Timestamps
    issuedAt: cognitoPayload.iat,
    expiresAt: cognitoPayload.exp
  };
}

/**
 * Verifica se o usuário existe no Cognito User Pool
 * @param {string} username - Username ou email do usuário
 * @returns {Object|null} Dados do usuário ou null se não encontrado
 */
async function getCognitoUser(username) {
  try {
    const params = {
      UserPoolId: process.env.USER_POOL_ID,
      Username: username
    };
    
    const result = await cognito.adminGetUser(params).promise();
    
    // Converte atributos para objeto
    const attributes = {};
    result.UserAttributes.forEach(attr => {
      attributes[attr.Name] = attr.Value;
    });
    
    return {
      username: result.Username,
      userStatus: result.UserStatus,
      enabled: result.Enabled,
      created: result.UserCreateDate,
      modified: result.UserLastModifiedDate,
      attributes
    };
    
  } catch (error) {
    if (error.code === 'UserNotFoundException') {
      return null;
    }
    console.error('Erro ao buscar usuário no Cognito:', error);
    throw error;
  }
}

/**
 * Lista usuários do Cognito User Pool com filtros
 * @param {Object} options - Opções de filtro
 * @returns {Array} Lista de usuários
 */
async function listCognitoUsers(options = {}) {
  try {
    const params = {
      UserPoolId: process.env.USER_POOL_ID,
      Limit: options.limit || 60,
      PaginationToken: options.paginationToken
    };
    
    if (options.filter) {
      params.Filter = options.filter;
    }
    
    const result = await cognito.listUsers(params).promise();
    
    // Processa todos os usuários com paginação
    let allUsers = result.Users;
    let nextToken = result.PaginationToken;
    
    while (nextToken && (!options.limit || allUsers.length < options.limit)) {
      const nextParams = {
        ...params,
        PaginationToken: nextToken
      };
      
      const nextResult = await cognito.listUsers(nextParams).promise();
      allUsers = allUsers.concat(nextResult.Users);
      nextToken = nextResult.PaginationToken;
    }
    
    // Converte para formato mais amigável
    return allUsers.map(user => {
      const attributes = {};
      user.Attributes.forEach(attr => {
        attributes[attr.Name] = attr.Value;
      });
      
      return {
        username: user.Username,
        userStatus: user.UserStatus,
        enabled: user.Enabled,
        created: user.UserCreateDate,
        modified: user.UserLastModifiedDate,
        attributes
      };
    });
    
  } catch (error) {
    console.error('Erro ao listar usuários do Cognito:', error);
    throw error;
  }
}

module.exports = {
  validateCognitoToken,
  extractUserDataFromCognito,
  getCognitoUser,
  listCognitoUsers
};
