create-dolar:
  handler: src/functions/dolar/create.handler
  name: ${env:STAGE}-create-dolar${env:VERSION}
  description: Função para registrar o valor do dolar no DynamoDB
  memorySize: 128
  events:
    - http:
        path: /create/dolar
        method: post
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

read-dolar:
  handler: src/functions/dolar/read.handler
  name: ${env:STAGE}-read-dolar${env:VERSION}
  description: Função para leitura do dolar da tabela do DynamoDB
  memorySize: 128
  events:
    - http:
        path: /read/dolar/{month}/{year}
        method: get
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}


