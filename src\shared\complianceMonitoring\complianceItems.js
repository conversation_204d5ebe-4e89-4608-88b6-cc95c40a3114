export const complianceItems = [
  {
    name: "Trust Relationship da conta root configurado",
    status: false,
  },
  {
    name: "IAM Role de acesso <PERSON> dared<PERSON>-full (AdministratorAccess)",
    status: false,
  },
  {
    name: "IAM Role de acesso <PERSON> dared<PERSON> (ReadOnlyAccess)",
    status: false,
  },
  {
    name: "Habilitar conta como Organization",
    status: false,
  },
  {
    name: "Policy SCP habilitadas para todas as contas",
    status: false,
  },
  {
    name: "Policy DenyLeaveOrganization criada e atrelada à conta root",
    status: false,
  },
  {
    name: "Cost Explorer aberto pelo menos 1 vez",
    status: false,
  },
  {
    name: "MFA na conta root habilitado",
    status: false,
  },
  {
    name: "Método de pagamento configurado para invoice",
    status: false,
  },
  {
    name: "CNPJ configurado como 18.342.439/0001-68",
    status: false,
  },
  {
    name: "Herança habilitada nas TaxSettings",
    status: false,
  },
  {
    name: "Tag 'AccountType' com valor 'Critical'",
    status: false,
  },
  {
    name: "Billing configurado",
    status: false,
  },
];
