import { readByIndex } from "../../model/dynamo";
import { complianceItems } from "./complianceItems";
import {
  getBucketPolicy,
  getCredentials,
  listPayerAccounts,
  updateManuallyConfiguredBilling,
} from "./updateManuallyConfiguredBilling";

let items = complianceItems;

async function checkAccountsStatus(accounts, prop, index) {
  for (let i = 0; i < accounts.length; i++) {
    if (!accounts[i][prop]) {
      items[index].status = false;
      return;
    } else {
      items[index].status = true;
    }
  }
  return;
}

export async function checkCustomerStatus(customer) {
  if (!customer.trust) items[0].status = false;
  else items[0].status = true;

  if (!customer.organizations) items[3].status = false;
  else items[3].status = true;

  if (!customer.scp) items[4].status = false;
  else items[4].status = true;

  if (!customer.denyLeaveOrg) items[5].status = false;
  else items[5].status = true;

  const customerHasBiilingConfig = 12;
  items[customerHasBiilingConfig].status =
    (await checkConfiguredBillingStatus(customer.customerId)) || false;

  const accounts = customer?.accountsStatus;

  if (accounts.length <= 0) return false;

  await checkAccountsStatus(accounts, "roleDaredeFull", 1);
  await checkAccountsStatus(accounts, "roleDarede", 2);
  await checkAccountsStatus(accounts, "mfaRootAccount", 7);
  await checkAccountsStatus(accounts, "costExplorer", 6);
  await checkAccountsStatus(accounts, "tagCritical", 11);

  return items;
}

export async function checkConfiguredBillingStatus(customerId) {
  const contractsByCustomer = await readByIndex(
    `${process.env.FINOPS_STAGE}-contracts`,
    customerId,
    "customer_id"
  );

  const billingContract = contractsByCustomer?.Items?.find(
    (contract) =>
      contract?.name.toLowerCase().includes("billing") && contract?.active === 1
  );

  if (billingContract?.billingConfigured) {
    return true;
  } else if (billingContract) {
    const assumedRole = await getCredentials();
    console.log("assumed role", assumedRole);
    const bucketPolicy = await getBucketPolicy(assumedRole);
    console.log("bucket policy", bucketPolicy);
    const payerAccountList = listPayerAccounts(bucketPolicy);
    const billingContractUpdated = await updateManuallyConfiguredBilling(
      customerId,
      billingContract,
      payerAccountList
    );
    return billingContractUpdated;
  }

  return false;
}
