const { handler: setTokenHandler } = require('../../functions/auth/set-token.js');
const { generateToken, verifyToken } = require('../../shared/auth/jwt-utils-cjs.js');
const { parseCookies } = require('../../shared/auth/cookie-utils-cjs.js');
const { setupAuthTests, createTestToken, createInvalidToken } = require('./utils/test-helpers.js');

setupAuthTests();

describe('Sistema de Autenticação com Cookies', () => {
  const mockUser = {
    sub: 'user123',
    email: '<EMAIL>',
    role: 'admin',
    name: 'Test User',
    permissions: ['users:read', 'users:write']
  };

  let testToken;
  let testEvent;

  beforeEach(() => {
    // Gera token de teste usando helper
    testToken = createTestToken(mockUser);

    // Mock do evento Lambda
    testEvent = {
      headers: {},
      body: JSON.stringify({ token: testToken })
    };
  });

  describe('JWT Utils', () => {
    test('deve gerar token válido', () => {
      const token = generateToken(mockUser);
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
    });

    test('deve verificar token válido', () => {
      const token = generateToken(mockUser);
      const decoded = verifyToken(token);
      
      expect(decoded.sub).toBe(mockUser.sub);
      expect(decoded.email).toBe(mockUser.email);
      expect(decoded.role).toBe(mockUser.role);
    });

    test('deve rejeitar token inválido', () => {
      expect(() => {
        verifyToken('invalid-token');
      }).toThrow();
    });
  });

  describe('Endpoint /auth/set-token', () => {
    test('deve armazenar token em cookies com sucesso', async () => {
      const response = await setTokenHandler(testEvent, {});

      expect(response.statusCode).toBe(200);
      expect(response.headers['Set-Cookie']).toBeDefined();

      const body = JSON.parse(response.body);
      expect(body.message).toContain('Token armazenado com sucesso');
      expect(body.user.email).toBe(mockUser.email);
      expect(body.tokenSet).toBe(true);
    });

    test('deve rejeitar requisição sem token', async () => {
      const eventWithoutToken = {
        headers: {},
        body: JSON.stringify({})
      };

      const response = await setTokenHandler(eventWithoutToken, {});

      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.message).toContain('Token é obrigatório');
    });

    test('deve rejeitar token inválido', async () => {
      const invalidToken = createInvalidToken();
      const eventWithInvalidToken = {
        headers: {},
        body: JSON.stringify({ token: invalidToken })
      };

      const response = await setTokenHandler(eventWithInvalidToken, {});

      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.message).toContain('Token inválido');
    });
  });

  // Testes para outros endpoints serão adicionados conforme implementação

  describe('Cookie Utils', () => {
    test('deve parsear cookies corretamente', () => {
      const event = {
        headers: {
          Cookie: 'dsm_access_token=test-token; dsm_refresh_token=refresh-token'
        }
      };
      
      const cookies = parseCookies(event);
      
      expect(cookies.dsm_access_token).toBe('test-token');
      expect(cookies.dsm_refresh_token).toBe('refresh-token');
    });

    test('deve lidar com headers de cookie vazios', () => {
      const event = {
        headers: {}
      };
      
      const cookies = parseCookies(event);
      
      expect(Object.keys(cookies)).toHaveLength(0);
    });
  });
});
