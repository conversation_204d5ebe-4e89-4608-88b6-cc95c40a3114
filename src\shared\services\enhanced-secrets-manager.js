import { SecretsManager } from 'aws-sdk';

/**
 * Enhanced Secrets Manager Service
 * Provides caching, error handling, and retry logic for AWS Secrets Manager
 */
class EnhancedSecretsManager {
  constructor() {
    this.client = new SecretsManager({
      region: process.env.AWS_REGION_LOCATION || 'us-east-1'
    });
    
    // Cache for secrets to avoid repeated API calls
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Get secret value with caching and retry logic
   */
  async getSecret(secretId, options = {}) {
    const { 
      useCache = true, 
      maxRetries = 3, 
      retryDelay = 1000,
      parseJson = true 
    } = options;

    // Check cache first
    if (useCache && this.cache.has(secretId)) {
      const cached = this.cache.get(secretId);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.value;
      }
      // Remove expired cache entry
      this.cache.delete(secretId);
    }

    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Attempting to retrieve secret ${secretId} (attempt ${attempt}/${maxRetries})`);
        
        const result = await this.client.getSecretValue({
          SecretId: secretId
        }).promise();

        let secretValue = result.SecretString;
        
        // Parse JSON if requested and if it's valid JSON
        if (parseJson && secretValue) {
          try {
            secretValue = JSON.parse(secretValue);
          } catch (parseError) {
            console.warn(`Secret ${secretId} is not valid JSON, returning as string`);
          }
        }

        // Cache the result
        if (useCache) {
          this.cache.set(secretId, {
            value: secretValue,
            timestamp: Date.now()
          });
        }

        console.log(`Successfully retrieved secret ${secretId}`);
        return secretValue;

      } catch (error) {
        lastError = error;
        console.error(`Attempt ${attempt} failed for secret ${secretId}:`, error.message);

        // Don't retry on certain errors
        if (error.code === 'ResourceNotFoundException' || 
            error.code === 'AccessDeniedException' ||
            error.code === 'InvalidParameterException') {
          throw error;
        }

        // Wait before retrying (exponential backoff)
        if (attempt < maxRetries) {
          const delay = retryDelay * Math.pow(2, attempt - 1);
          console.log(`Waiting ${delay}ms before retry...`);
          await this.sleep(delay);
        }
      }
    }

    throw new Error(`Failed to retrieve secret ${secretId} after ${maxRetries} attempts: ${lastError.message}`);
  }

  /**
   * Get JWT secrets specifically
   */
  async getJWTSecrets() {
    const secretId = process.env.JWT_DECRIPTION_CREDENTIALS;
    
    if (!secretId) {
      throw new Error('JWT_DECRIPTION_CREDENTIALS environment variable is not set');
    }

    try {
      const secrets = await this.getSecret(secretId);
      
      // Validate required JWT fields
      const requiredFields = ['JWT_SECRET', 'JWT_EXPIRES_IN', 'JWT_REFRESH_EXPIRES_IN'];
      const missingFields = requiredFields.filter(field => !secrets[field]);
      
      if (missingFields.length > 0) {
        throw new Error(`Missing required JWT fields: ${missingFields.join(', ')}`);
      }

      return secrets;
    } catch (error) {
      console.error('Error retrieving JWT secrets:', error);
      throw new Error(`Failed to retrieve JWT secrets: ${error.message}`);
    }
  }

  /**
   * Get WAF token
   */
  async getWafToken() {
    const secretId = process.env.DSM_API_WAF_ARN;
    
    if (!secretId) {
      throw new Error('DSM_API_WAF_ARN environment variable is not set');
    }

    try {
      const secret = await this.getSecret(secretId);
      return secret.token || secret;
    } catch (error) {
      console.error('Error retrieving WAF token:', error);
      throw new Error(`Failed to retrieve WAF token: ${error.message}`);
    }
  }

  /**
   * Get OTRS API credentials
   */
  async getOTRSCredentials() {
    const secretId = process.env.AWS_SECRET_CREDENTIALS_OTRS_API;
    
    if (!secretId) {
      throw new Error('AWS_SECRET_CREDENTIALS_OTRS_API environment variable is not set');
    }

    try {
      const credentials = await this.getSecret(secretId);
      
      // Validate required OTRS fields
      const requiredFields = ['clientId', 'clientSecret', 'accessTokenUrl'];
      const missingFields = requiredFields.filter(field => !credentials[field]);
      
      if (missingFields.length > 0) {
        throw new Error(`Missing required OTRS fields: ${missingFields.join(', ')}`);
      }

      return credentials;
    } catch (error) {
      console.error('Error retrieving OTRS credentials:', error);
      throw new Error(`Failed to retrieve OTRS credentials: ${error.message}`);
    }
  }

  /**
   * Get CRM API credentials
   */
  async getCRMCredentials() {
    const secretId = process.env.DSM_API_SECREAT_MANAGER;
    
    if (!secretId) {
      throw new Error('DSM_API_SECREAT_MANAGER environment variable is not set');
    }

    try {
      const credentials = await this.getSecret(secretId);
      
      // Validate required CRM fields
      const requiredFields = ['user', 'password'];
      const missingFields = requiredFields.filter(field => !credentials[field]);
      
      if (missingFields.length > 0) {
        throw new Error(`Missing required CRM fields: ${missingFields.join(', ')}`);
      }

      return credentials;
    } catch (error) {
      console.error('Error retrieving CRM credentials:', error);
      throw new Error(`Failed to retrieve CRM credentials: ${error.message}`);
    }
  }

  /**
   * Clear cache for a specific secret or all secrets
   */
  clearCache(secretId = null) {
    if (secretId) {
      this.cache.delete(secretId);
      console.log(`Cache cleared for secret: ${secretId}`);
    } else {
      this.cache.clear();
      console.log('All secrets cache cleared');
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      entries: Array.from(this.cache.keys()),
      timeout: this.cacheTimeout
    };
  }

  /**
   * Sleep utility for retry delays
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Health check for Secrets Manager connectivity
   */
  async healthCheck() {
    try {
      // Try to list secrets to verify connectivity
      await this.client.listSecrets({ MaxResults: 1 }).promise();
      return { status: 'healthy', timestamp: new Date().toISOString() };
    } catch (error) {
      return { 
        status: 'unhealthy', 
        error: error.message, 
        timestamp: new Date().toISOString() 
      };
    }
  }
}

// Export singleton instance
export const enhancedSecretsManager = new EnhancedSecretsManager();
export default enhancedSecretsManager;
