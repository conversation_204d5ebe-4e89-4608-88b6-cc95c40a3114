/**
 * Health Check Endpoint
 * Endpoint simples para verificar se a API está funcionando
 */



const healthHandler = async (event, context) => {
  console.log('🏥 Health check endpoint called');
  console.log('Method:', event.httpMethod);
  console.log('Path:', event.path);

  const healthData = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'DSM Backend API',
    version: '1.0.0',
    environment: process.env.STAGE || 'dev',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    path: event.path,
    method: event.httpMethod
  };

  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(healthData)
  };
};

exports.handler = async (event, context) => {
  return healthHandler(event, context);
};
