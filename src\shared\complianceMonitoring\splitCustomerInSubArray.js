export async function splitCustomersArr(customers, splitLength) {
  if (customers.length > splitLength) {
    let resArr = [];

    for (let i = 0; i < customers.length; i += splitLength) {
      let customersSplitted = [];
      for (let j = i; j - i < splitLength && j < customers.length; j++) {
        if (customers[j]) {
          customersSplitted.push(customers[j]);
        }
      }
      resArr.push(customersSplitted);
    }

    console.log("returning resArr");
    return resArr;
  } else {
    console.log("returning customers");
    return [customers];
  }
}
