// import { parseBody, parseHeaders } from '../../shared/parsers'
import { message, json } from "../../shared/response";
import { main } from "./checkAccountsStatus";

const AWS = require("aws-sdk");
const region = "us-east-1";

async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

export const handler = async (event, context) => {
  try {
    // await main(); // -------------- uncomment this to run it locally
    // const res = ""; // -------------- uncomment this to run it locally

    // -------------- comment the code below to run it locally
    const lambda = new AWS.Lambda({
      region,
    });

    const res = await lambda
      .invoke({
        FunctionName:
          process.env.FINOPS_STAGE + "-compliance-check-accounts-status",
      })
      .promise()
      .catch(async (err) => {
        console.log(
          "Couldn't invoke lambda function: ",
          process.env.FINOPS_STAGE + "-compliance-check-accounts-status"
        );
        return await sendDataToUser(500, "error", err);
      });
    // -------------- comment the code above to run it locally

    return await sendDataToUser(200, "success", res);
  } catch (error) {
    console.log(error);
    return await sendDataToUser(500, "error", error);
  }
};
