const { parseBody } = require('../../shared/parsers-cjs');
const { responseWithCookies, responseWithBadRequest, responseWithError } = require('../../shared/response-cjs');
const { verifyToken, generateToken, generateRefreshToken } = require('../../shared/auth/jwt-utils-secrets');
const { createAuthCookieHeaders } = require('../../shared/auth/cookie-utils-cjs');
const { withAuthCors } = require('../../shared/cors/cors-middleware');
const jwt = require('jsonwebtoken');
const { CognitoJwtVerifier } = require('aws-jwt-verify');

const cognitoVerifier = CognitoJwtVerifier.create({
  userPoolId: "us-east-1_VCf8aHRIZ",
  tokenUse: "id",
  clientId: "a612mhbbrvrh45ec6n8amqf5i"
});

/**
 * Valida token Cognito com verificação de assinatura
 * @param {string} token - Token Cognito para validar
 * @returns {Object} Payload decodificado do token Cognito
 */
async function validateCognitoToken(token) {
  try {
    console.log('🔍 Validando token Cognito com verificação de assinatura...');
    console.log('Token recebido (primeiros 50 chars):', token.substring(0, 50) + '...');

    const payload = await cognitoVerifier.verify(token);
    console.log('✅ Token Cognito verificado com assinatura válida');

    console.log('Verificando estrutura do token:', {
      token_use: payload.token_use,
      sub: payload.sub,
      iss: payload.iss,
      email: payload.email
    });

    if (!payload.sub) {
      throw new Error('Token não tem subject (sub) obrigatório');
    }

    // Verificação de expiração já é feita pelo aws-jwt-verify
    console.log('Token Cognito validado com assinatura:', {
      sub: payload.sub,
      email: payload.email,
      token_use: payload.token_use,
      exp: payload.exp
    });

    return payload;

  } catch (error) {
    console.error('Erro ao validar token Cognito:', error);

    if (error.name === 'JwtExpiredError') {
      throw new Error('Token Cognito expirado');
    } else if (error.name === 'JwtInvalidSignatureError') {
      throw new Error('Assinatura do token Cognito inválida');
    } else if (error.name === 'JwtInvalidClaimError') {
      throw new Error('Claims do token Cognito inválidos');
    } else {
      throw new Error(`Token Cognito inválido: ${error.message}`);
    }
  }
}

/**
 * Endpoint POST /auth/set-token
 * Recebe um token (JWT interno ou Cognito) no corpo da requisição e armazena em cookies HttpOnly
 * Usa AWS Secrets Manager para credenciais JWT
 */
const setTokenHandler = async (event, context) => {
  try {
    console.log('Set-token endpoint called (usando Secrets Manager para JWT)');

    const body = parseBody(event);

    if (!body || !body.token) {
      return responseWithBadRequest('Token é obrigatório no corpo da requisição');
    }

    let decoded;
    let isCognitoToken = false;

    console.log('🔍 Detectando tipo de token...');
    const tokenPreview = jwt.decode(body.token);
    console.log('Preview do token:', {
      iss: tokenPreview?.iss,
      sub: tokenPreview?.sub,
      email: tokenPreview?.email,
      token_use: tokenPreview?.token_use
    });

    // Verifica se é token do Cognito pelo issuer
    if (tokenPreview && tokenPreview.iss && tokenPreview.iss.includes('cognito-idp')) {
      console.log('✅ Token identificado como Cognito pelo issuer');
      try {
        decoded = await validateCognitoToken(body.token);
        isCognitoToken = true;
        console.log('✅ Token Cognito validado com sucesso');
      } catch (cognitoError) {
        console.error('❌ Erro ao validar token Cognito:', cognitoError.message);
        return responseWithBadRequest(`Token Cognito inválido: ${cognitoError.message}`);
      }
    } else {
      console.log('🔍 Token não é do Cognito, tentando como JWT interno...');
      try {
        decoded = await verifyToken(body.token);
        console.log('✅ Token JWT interno validado com sucesso');
      } catch (jwtError) {
        console.error('❌ Erro ao validar token JWT interno:', jwtError.message);
        return responseWithBadRequest(`Token JWT interno inválido: ${jwtError.message}`);
      }
    }

    let userPayload;

    if (isCognitoToken) {
      try {
        userPayload = {
          sub: decoded.sub,
          userId: decoded.sub,
          email: decoded.email || decoded['cognito:username'] || '',
          name: decoded.name || decoded.given_name || decoded.family_name || '',
          role: decoded['custom:role'] || decoded.role || 'user',
          permissions: decoded['custom:permissions'] ?
            JSON.parse(decoded['custom:permissions']) : [],
          cognitoUsername: decoded['cognito:username'] || '',
          emailVerified: decoded.email_verified || false,
          tokenUse: decoded.token_use || 'id'
        };
      } catch (extractionError) {
        console.error('Erro na extração de dados do token Cognito:', extractionError);
        return responseWithError('Erro ao processar token Cognito');
      }

      console.log('Dados extraídos do token Cognito:', {
        sub: userPayload.sub,
        email: userPayload.email,
        role: userPayload.role
      });

    } else {
      // Para tokens JWT internos, remove campos de controle
      const { iat, exp, iss, aud, ...payload } = decoded;
      userPayload = {
        sub: payload.sub || payload.userId,
        userId: payload.sub || payload.userId,
        email: payload.email,
        role: payload.role,
        permissions: payload.permissions || [],
        name: payload.name,
        ...payload
      };
    }

    console.log('🔄 Gerando access token...');
    const newAccessToken = await generateToken({
      sub: userPayload.sub,
      userId: userPayload.userId,
      email: userPayload.email,
      role: userPayload.role,
      permissions: userPayload.permissions,
      name: userPayload.name,
      // Marca origem do token
      tokenSource: isCognitoToken ? 'cognito' : 'internal'
    });
    console.log('✅ Access token gerado');

    console.log('🔄 Gerando refresh token...');
    const refreshToken = await generateRefreshToken({
      sub: userPayload.sub,
      userId: userPayload.userId,
      email: userPayload.email,
      role: userPayload.role,
      tokenSource: isCognitoToken ? 'cognito' : 'internal'
    });
    console.log('✅ Refresh token gerado');

    console.log('🍪 Criando headers de cookies...');
    const cookieHeaders = createAuthCookieHeaders(newAccessToken, refreshToken);
    console.log('✅ Headers de cookies criados:', cookieHeaders.length, 'cookies');
    
    // Dados de resposta (sem incluir os tokens por segurança)
    const responseData = {
      message: isCognitoToken ?
        'Token Cognito convertido e armazenado em cookies seguros' :
        'Token JWT armazenado em cookies seguros',
      user: {
        id: userPayload.sub,
        email: userPayload.email,
        role: userPayload.role,
        name: userPayload.name
      },
      tokenInfo: {
        source: isCognitoToken ? 'cognito' : 'internal',
        expiresIn: '8h',
        refreshExpiresIn: '7d',
        cookiesSet: true
      },
      conversion: isCognitoToken ? {
        from: 'cognito-jwt',
        to: 'internal-jwt-cookies',
        cognitoData: {
          sub: decoded.sub,
          tokenUse: decoded.token_use,
          emailVerified: decoded.email_verified
        }
      } : null
    };

    console.log(`Token ${isCognitoToken ? 'Cognito convertido' : 'JWT armazenado'} com sucesso para usuário: ${userPayload.email} (Secrets Manager)`);

    console.log('📦 Criando resposta com cookies...');
    const response = responseWithCookies(responseData, cookieHeaders);
    console.log('✅ Resposta criada');

    // Headers CORS já são adicionados por responseWithCookies
    // Não duplicar para evitar conflitos

    // Debug da resposta para identificar problema 502
    console.log('🔍 Resposta final sendo retornada: NOVO', {
      statusCode: response.statusCode,
      headersCount: Object.keys(response.headers).length,
      bodyLength: response.body?.length || 0,
      cookiesCount: cookieHeaders.length,
      headers: Object.keys(response.headers),
      hasMultiValueHeaders: !!response.multiValueHeaders,
      multiValueHeadersCount: response.multiValueHeaders ? Object.keys(response.multiValueHeaders).length : 0
    });

    console.log('🚀 Retornando resposta...');
    return response;

  } catch (error) {
    console.error('Erro no endpoint set-token (Secrets Manager):', error);
    return responseWithError('Erro interno do servidor ao definir token');
  }
};

exports.handler = withAuthCors(setTokenHandler);
