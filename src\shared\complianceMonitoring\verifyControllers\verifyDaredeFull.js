export async function verifyDaredeFull(iam, roles) {
  for (let i = 0; i < roles.Roles.length; i++) {
    if (roles.Roles[i].RoleName === "darede-full") {
      let policies = await iam
        .listAttachedRolePolicies({ RoleName: "darede-full" })
        .promise();

      const existsAdministratorAccess = policies?.AttachedPolicies?.find(
        (p) => p.PolicyName === "AdministratorAccess"
      );

      if (existsAdministratorAccess) {
        console.log(
          "Role darede-full exists and has AdministratorAccess attached"
        );
        return true;
      } else {
        console.log(
          "Role darede-full exists but doesn't have AdministratorAccess attached"
        );
        return false;
      }
    }
  }
}
