import { message, json } from "../../shared/response";
import { readCustomers } from "../../model/dynamo";
import { parsePath, parseQueryString } from "../../shared/parsers";


async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

export const handler = async (event, context) => {
  console.log({ event });
  const { nextPage } = parseQueryString(event);

  try {
    const { customers, lastEvaluatedKey } = await readCustomers({ nextPage });

    return await sendDataToUser(200, "success", {
      customers,
      nextPage: lastEvaluatedKey ? lastEvaluatedKey.id : "",
    });
  } catch (error) {
    console.log(error);
    return await sendDataToUser(500, "error", error);
  }
};