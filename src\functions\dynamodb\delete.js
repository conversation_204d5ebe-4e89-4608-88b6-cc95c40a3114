import { parseHeaders, parsePath } from "../../shared/parsers";
import { message, json } from "../../shared/response";
import { delt } from "../../model/dynamo";

async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

export const handler = async (event, context) => {
  try {
    console.log({ event });
    const url = parsePath(event);
    const head = parseHeaders(event);

    const { id } = url;
    const { dynamodb } = head;

    const delObjDynamo = await delt(dynamodb, id);
    console.log({ delObjDynamo });
    return await sendDataToUser(200, "success", delObjDynamo);
  } catch (error) {
    console.log(error);
    return await sendDataToUser(500, "error", error);
  }
};