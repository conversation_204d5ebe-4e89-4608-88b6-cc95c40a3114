#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting DynamoDB Local...');

// Start DynamoDB Local
const dynamoProcess = spawn('java', [
  '-Djava.library.path=./DynamoDBLocal_lib',
  '-jar', 'DynamoDBLocal.jar',
  '-sharedDb',
  '-port', '8001'
], {
  cwd: path.join(__dirname, '../dynamodb-local'),
  stdio: 'inherit'
});

dynamoProcess.on('close', (code) => {
  console.log(`DynamoDB Local process exited with code ${code}`);
});

dynamoProcess.on('error', (err) => {
  console.error('Failed to start DynamoDB Local:', err);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down DynamoDB Local...');
  dynamoProcess.kill();
  process.exit(0);
});
