import {
  parseHeaders,
  parsePath,
  parseQueryString,
} from "../../shared/parsers";
import { message, json } from "../../shared/response";

import {
  readOne,
  readAll,
  readByCRM,
  readByITSM,
  readByDeal,
  readByIndex,
  readAllAccounts,
  readAccountsByCustomerDsmID,
} from "../../model/dynamo";

async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

export const handler = async (event) => {
  console.log("Event:", event);

  try {
    const url = parsePath(event);
    const head = parseHeaders(event);
    const queryString = parseQueryString(event);
    console.log("queryString:", queryString);
    let indexName = "";

    if (queryString.indexName) indexName = queryString.indexName;

    const { id, form } = url;
    const { dynamodb } = head;
    console.log("id:", id, form, dynamodb);

    // Validação de parâmetros obrigatórios
    if (!form) {
      console.error("Parâmetro 'form' é obrigatório");
      return await sendDataToUser(400, "error", "Parâmetro 'form' é obrigatório");
    }

    if (!dynamodb) {
      console.error("Header 'dynamodb' é obrigatório");
      return await sendDataToUser(400, "error", "Header 'dynamodb' é obrigatório");
    }

    const operationsRequiringId = ['id', 'crm', 'itsm', 'deal', 'indexKey', 'accountsByCustomer'];
    if (operationsRequiringId.includes(form) && (!id || id === 'null' || id === 'undefined')) {
      console.error(`Operação '${form}' requer um ID válido. ID recebido: ${id}`);
      return await sendDataToUser(400, "error", `ID válido é obrigatório para operação '${form}'`);
    }
    const params = {
      all: async () => await readAll(dynamodb),
      id: async () => await readOne(dynamodb, id),
      crm: async () => await readByCRM(dynamodb, id),
      itsm: async () => await readByITSM(dynamodb, id),
      deal: async () => await readByDeal(dynamodb, id),
      indexKey: async () => await readByIndex(dynamodb, id, indexName),
      allAccounts: async () => await readAllAccounts(dynamodb),
      accountsByCustomer: async () =>
        await readAccountsByCustomerDsmID(dynamodb, id),
    };

    const readObjDynamo = await params[form]();
    return await sendDataToUser(200, "success", readObjDynamo);
  } catch (error) {
    console.log(error);
    return await sendDataToUser(500, "error", error);
  }
};
