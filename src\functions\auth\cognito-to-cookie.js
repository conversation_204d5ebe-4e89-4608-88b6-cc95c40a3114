/**
 * Endpoint POST /auth/cognito-to-cookie
 * Converte token JWT do Cognito para cookies HttpOnly seguros
 */

const { parseBody } = require('../../shared/parsers-cjs');
const { responseWithCookies, responseWithBadRequest, responseWithError } = require('../../shared/response-cjs');
const { generateToken, generateRefreshToken } = require('../../shared/auth/jwt-utils-secrets');
const { createAuthCookieHeaders } = require('../../shared/auth/cookie-utils-cjs');
const { validateCognitoToken, extractUserDataFromCognito } = require('../../shared/auth/cognito-utils');
const { withAuthCors } = require('../../shared/cors/cors-middleware');

/**
 * Handler principal do endpoint
 */
const cognitoToCookieHandler = async (event, context) => {
  try {
    console.log('Cognito-to-cookie endpoint called');
    
    // Parse do body da requisição
    const body = parseBody(event);
    
    if (!body || !body.token) {
      return responseWithBadRequest('Token do Cognito é obrigatório no corpo da requisição');
    }
    
    // Valida o token do Cognito
    let cognitoPayload;
    try {
      cognitoPayload = await validateCognitoToken(body.token);
      console.log('Token Cognito validado com sucesso');
    } catch (error) {
      console.error('Erro na validação do token Cognito:', error);
      return responseWithBadRequest(`Token Cognito inválido: ${error.message}`);
    }
    
    // Extrai dados do usuário do token Cognito
    const userData = extractUserDataFromCognito(cognitoPayload);
    
    console.log('Dados do usuário extraídos:', {
      sub: userData.sub,
      email: userData.email,
      role: userData.role,
      tokenUse: userData.tokenUse
    });
    
    // Gera novos tokens JWT internos
    const accessTokenPayload = {
      sub: userData.sub,
      userId: userData.userId,
      email: userData.email,
      role: userData.role,
      permissions: userData.permissions,
      name: userData.name,
      // Metadados da conversão
      tokenSource: 'cognito',
      cognitoSub: userData.sub,
      originalTokenUse: userData.tokenUse
    };
    
    const refreshTokenPayload = {
      sub: userData.sub,
      userId: userData.userId,
      email: userData.email,
      role: userData.role,
      tokenSource: 'cognito',
      type: 'refresh'
    };
    
    // Gera os tokens
    const newAccessToken = generateToken(accessTokenPayload);
    const newRefreshToken = generateRefreshToken(refreshTokenPayload);
    
    // Cria headers de cookies HttpOnly
    const cookieHeaders = createAuthCookieHeaders(newAccessToken, newRefreshToken);
    
    // Dados de resposta (sem incluir os tokens por segurança)
    const responseData = {
      success: true,
      message: 'Token Cognito convertido e armazenado em cookies HttpOnly seguros',
      user: {
        id: userData.sub,
        email: userData.email,
        name: userData.name,
        role: userData.role,
        emailVerified: userData.emailVerified
      },
      conversion: {
        from: 'cognito-jwt',
        to: 'internal-jwt-cookies',
        tokenUse: userData.tokenUse,
        cognitoData: {
          sub: userData.sub,
          issuer: userData.issuer,
          audience: userData.audience,
          emailVerified: userData.emailVerified
        }
      },
      tokenInfo: {
        source: 'cognito',
        accessTokenExpiresIn: process.env.JWT_EXPIRES_IN || '8h',
        refreshTokenExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
        cookiesSet: true,
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production'
      },
      timestamp: new Date().toISOString()
    };
    
    console.log(`Token Cognito convertido com sucesso para usuário: ${userData.email}`);
    
    return responseWithCookies(responseData, cookieHeaders);
    
  } catch (error) {
    console.error('Erro no endpoint cognito-to-cookie:', error);
    return responseWithError('Erro interno do servidor ao converter token Cognito');
  }
};

exports.handler = withAuthCors(cognitoToCookieHandler);
