import jwt from 'jsonwebtoken';
import { SecretsManager } from 'aws-sdk';
import { json, message } from '../shared/response';

const secretsManager = new SecretsManager({
  region: process.env.AWS_REGION_LOCATION
});

/**
 * Get JWT secrets from AWS Secrets Manager
 */
async function getJWTSecrets() {
  try {
    const secretValue = await secretsManager.getSecretValue({
      SecretId: process.env.JWT_DECRIPTION_CREDENTIALS
    }).promise();
    
    return JSON.parse(secretValue.SecretString);
  } catch (error) {
    console.error('Error getting JWT secrets:', error);
    throw new Error('Failed to retrieve JWT secrets');
  }
}

/**
 * Extract token from cookies
 */
function extractTokenFromCookies(event) {
  const cookies = event.headers?.Cookie || event.headers?.cookie || '';
  const cookieArray = cookies.split(';');
  
  for (const cookie of cookieArray) {
    const [name, value] = cookie.trim().split('=');
    if (name === 'dsm_auth_token') {
      return value;
    }
  }
  
  return null;
}

/**
 * Verify JWT token
 */
async function verifyToken(token) {
  try {
    const secrets = await getJWTSecrets();
    const decoded = jwt.verify(token, secrets.JWT_SECRET);
    
    // Check if token is expired
    const currentTime = Math.floor(Date.now() / 1000);
    if (decoded.exp < currentTime) {
      throw new Error('Token expired');
    }
    
    return decoded;
  } catch (error) {
    console.error('Token verification failed:', error);
    throw new Error('Invalid token');
  }
}

/**
 * Authentication middleware
 */
export const authMiddleware = (handler) => {
  return async (event, context) => {
    try {
      // Extract token from cookies
      const token = extractTokenFromCookies(event);

      if (!token) {
        return await json(await message('Unauthorized', 'No authentication token provided'), 401);
      }

      // Verify token
      const decoded = await verifyToken(token);

      // Add user info to event context
      event.user = decoded;

      // Continue to handler
      return await handler(event, context);

    } catch (error) {
      console.error('Authentication middleware error:', error);
      return await json(await message('Unauthorized', error.message), 401);
    }
  };
};

/**
 * Generate JWT token
 */
export async function generateJWTToken(payload) {
  try {
    const secrets = await getJWTSecrets();
    
    const tokenPayload = {
      ...payload,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (8 * 60 * 60), // 8 hours
    };
    
    return jwt.sign(tokenPayload, secrets.JWT_SECRET);
  } catch (error) {
    console.error('Error generating JWT token:', error);
    throw new Error('Failed to generate token');
  }
}

/**
 * Generate refresh token
 */
export async function generateRefreshToken(payload) {
  try {
    const secrets = await getJWTSecrets();
    
    const tokenPayload = {
      ...payload,
      type: 'refresh',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60), // 7 days
    };
    
    return jwt.sign(tokenPayload, secrets.JWT_SECRET);
  } catch (error) {
    console.error('Error generating refresh token:', error);
    throw new Error('Failed to generate refresh token');
  }
}

/**
 * Set secure HTTP-only cookie
 */
export function setSecureCookie(name, value, maxAge = 8 * 60 * 60) { // 8 hours default
  const isProduction = process.env.STAGE === 'prod';
  
  return {
    'Set-Cookie': `${name}=${value}; HttpOnly; Secure=${isProduction}; SameSite=Strict; Max-Age=${maxAge}; Path=/`
  };
}

/**
 * Clear cookie
 */
export function clearCookie(name) {
  return {
    'Set-Cookie': `${name}=; HttpOnly; Secure=true; SameSite=Strict; Max-Age=0; Path=/`
  };
}

/**
 * CSRF token generation and validation
 */
export function generateCSRFToken() {
  return require('crypto').randomBytes(32).toString('hex');
}

export function validateCSRFToken(token, sessionToken) {
  return token === sessionToken;
}

/**
 * Rate limiting middleware
 */
const rateLimitStore = new Map();

export const rateLimitMiddleware = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  return async (event, context, next) => {
    const clientIP = event.requestContext?.identity?.sourceIp || 'unknown';
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Clean old entries
    for (const [ip, requests] of rateLimitStore.entries()) {
      const filteredRequests = requests.filter(time => time > windowStart);
      if (filteredRequests.length === 0) {
        rateLimitStore.delete(ip);
      } else {
        rateLimitStore.set(ip, filteredRequests);
      }
    }
    
    // Check current IP
    const requests = rateLimitStore.get(clientIP) || [];
    const recentRequests = requests.filter(time => time > windowStart);
    
    if (recentRequests.length >= maxRequests) {
      return await json(await message('Too Many Requests', 'Rate limit exceeded'), 429);
    }
    
    // Add current request
    recentRequests.push(now);
    rateLimitStore.set(clientIP, recentRequests);
    
    return await next(event, context);
  };
};
