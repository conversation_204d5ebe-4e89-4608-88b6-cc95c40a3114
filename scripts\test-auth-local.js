#!/usr/bin/env node

/**
 * Script para testar manualmente os endpoints de autenticação
 * Execute: yarn test:auth:manual ou node scripts/test-auth-local.js
 * 
 * Certifique-se de que o Serverless Offline está rodando:
 * yarn dev ou npm run dev
 */

// Configurar variáveis de ambiente para coincidir com o servidor
// Usando a mesma chave JWT que está no .env.dev
process.env.JWT_SECRET = '871c4c458f16a9807c5a53027a62bda3e4c01e06ab755aa243f5b0d4f447e5d9d35e24f9d8ac976c4bc1ce58291fd0796d39c93dce8343a46890542b8a29f3be';
process.env.JWT_EXPIRES_IN = '8h';
process.env.JWT_REFRESH_EXPIRES_IN = '7d';
process.env.ALLOWED_ORIGINS = 'http://localhost:3000,https://dev.dsm.darede.com.br';
process.env.NODE_ENV = 'development';

const http = require('http');
const path = require('path');

// Importar utilitários JWT
const jwtUtilsPath = path.join(__dirname, '..', 'src', 'shared', 'auth', 'jwt-utils-cjs.js');
const { generateToken } = require(jwtUtilsPath);

let cookies = '';

// Função para fazer requisições HTTP
function makeRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 8000,
      path: `/dev${path}`,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (cookies) {
      options.headers['Cookie'] = cookies;
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        if (res.headers['set-cookie']) {
          cookies = res.headers['set-cookie'].join('; ');
        }
        
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: responseData
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testAuthSystem() {
  console.log('🚀 Testando Sistema de Autenticação com Cookies HttpOnly\n');
  console.log('📋 Pré-requisitos:');
  console.log('   - Serverless Offline rodando em http://localhost:8000');
  console.log('   - Execute: yarn dev ou npm run dev\n');

  try {
    // Verificar se servidor está rodando
    console.log('🔍 Verificando se servidor está rodando...');
    try {
      await makeRequest('GET', '/auth/verify');
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log('❌ Servidor não está rodando!');
        console.log('   Execute: yarn dev ou npm run dev');
        console.log('   Aguarde a mensagem "Server ready: http://localhost:8000 🚀"');
        return;
      }
    }
    console.log('✅ Servidor está rodando\n');

    // 1. Gerar token de teste
    console.log('1️⃣ Gerando token de teste...');
    const testUser = {
      sub: 'user123',
      email: '<EMAIL>',
      role: 'admin',
      name: 'Test User',
      permissions: ['users:read', 'users:write']
    };
    
    const token = generateToken(testUser);
    console.log('✅ Token gerado');
    console.log(`   Token: ${token.substring(0, 50)}...\n`);

    // 2. Testar set-token
    console.log('2️⃣ Testando POST /auth/set-token...');
    const setResponse = await makeRequest('POST', '/auth/set-token', { token });
    console.log(`   Status: ${setResponse.statusCode}`);
    
    if (setResponse.statusCode === 200) {
      console.log('   ✅ set-token funcionou');
      const body = JSON.parse(setResponse.body);
      console.log(`   👤 Usuário: ${body.user.email} (${body.user.role})`);
      console.log(`   🍪 Cookies definidos: ${setResponse.headers['set-cookie'] ? 'Sim' : 'Não'}`);
      
      if (setResponse.headers['set-cookie']) {
        const cookieCount = Array.isArray(setResponse.headers['set-cookie']) 
          ? setResponse.headers['set-cookie'].length 
          : 1;
        console.log(`   📊 Quantidade de cookies: ${cookieCount}`);
      }
    } else {
      console.log('   ❌ set-token falhou:', setResponse.body);
      return;
    }
    console.log('');

    // 3. Testar verify
    console.log('3️⃣ Testando GET /auth/verify...');
    const verifyResponse = await makeRequest('GET', '/auth/verify');
    console.log(`   Status: ${verifyResponse.statusCode}`);
    
    if (verifyResponse.statusCode === 200) {
      console.log('   ✅ verify funcionou');
      const body = JSON.parse(verifyResponse.body);
      console.log(`   🔐 Autenticado: ${body.authenticated}`);
      console.log(`   👤 Usuário: ${body.user.email}`);
      console.log(`   ⏰ Token válido: ${body.tokenStatus.valid}`);
      console.log(`   🔄 Precisa refresh: ${body.tokenStatus.needsRefresh}`);
    } else {
      console.log('   ❌ verify falhou:', verifyResponse.body);
    }
    console.log('');

    // 4. Testar refresh
    console.log('4️⃣ Testando POST /auth/refresh...');
    const refreshResponse = await makeRequest('POST', '/auth/refresh');
    console.log(`   Status: ${refreshResponse.statusCode}`);
    
    if (refreshResponse.statusCode === 200) {
      console.log('   ✅ refresh funcionou');
      const body = JSON.parse(refreshResponse.body);
      console.log(`   👤 Usuário: ${body.user.email}`);
      console.log(`   🔄 Token renovado em: ${body.refreshedAt}`);
    } else {
      console.log('   ❌ refresh falhou:', refreshResponse.body);
    }
    console.log('');

    // 5. Testar logout
    console.log('5️⃣ Testando POST /auth/logout...');
    const logoutResponse = await makeRequest('POST', '/auth/logout');
    console.log(`   Status: ${logoutResponse.statusCode}`);
    
    if (logoutResponse.statusCode === 200) {
      console.log('   ✅ logout funcionou');
      const body = JSON.parse(logoutResponse.body);
      console.log(`   📝 Mensagem: ${body.message}`);
      console.log(`   🍪 Cookies limpos: ${logoutResponse.headers['set-cookie'] ? 'Sim' : 'Não'}`);
    } else {
      console.log('   ❌ logout falhou:', logoutResponse.body);
    }
    console.log('');

    // 6. Verificar se logout limpou os cookies
    console.log('6️⃣ Verificando se logout limpou os cookies...');
    const verifyAfterLogoutResponse = await makeRequest('GET', '/auth/verify');
    console.log(`   Status: ${verifyAfterLogoutResponse.statusCode}`);
    
    if (verifyAfterLogoutResponse.statusCode === 400) {
      console.log('   ✅ Logout limpou os cookies corretamente');
    } else {
      console.log('   ❌ Logout não limpou os cookies');
      console.log('   Response:', verifyAfterLogoutResponse.body);
    }
    console.log('');

    console.log('🎉 Teste completo finalizado!');
    console.log('');
    console.log('📊 Resumo:');
    console.log('   ✅ Sistema de autenticação com cookies HttpOnly funcionando');
    console.log('   ✅ Tokens sendo armazenados em cookies seguros');
    console.log('   ✅ Verificação de autenticação funcionando');
    console.log('   ✅ Refresh de tokens funcionando');
    console.log('   ✅ Logout limpando cookies corretamente');
    console.log('');
    console.log('🔗 Endpoints testados:');
    console.log('   • POST http://localhost:8000/dev/auth/set-token');
    console.log('   • GET  http://localhost:8000/dev/auth/verify');
    console.log('   • POST http://localhost:8000/dev/auth/refresh');
    console.log('   • POST http://localhost:8000/dev/auth/logout');

  } catch (error) {
    console.error('❌ Erro durante o teste:', error.message);
    console.log('');
    console.log('🔧 Possíveis soluções:');
    console.log('   1. Verifique se o Serverless Offline está rodando');
    console.log('   2. Execute: yarn dev ou npm run dev');
    console.log('   3. Aguarde o servidor inicializar completamente');
    console.log('   4. Verifique se a porta 8000 está disponível');
  }
}

// Executar teste se chamado diretamente
if (require.main === module) {
  testAuthSystem();
}

module.exports = { testAuthSystem };
