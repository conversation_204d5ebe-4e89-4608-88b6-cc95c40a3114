import { putNo<PERSON><PERSON> } from "../dynamo";
import { complianceItems } from "../../shared/complianceMonitoring/complianceItems";
import { v4 } from "uuid";

export async function createNewComplianceItemDynamo(obj, customer) {
  const items = complianceItems;
  const dynamoPutObj = {
    customerId: customer.id,
    customerName: customer.names.fantasy_name,
    paAccountId: obj.paAccountId,
    items: items,
    accountsStatus: {
      status: obj.accountsStatus,
      org: obj.organizations,
      scp: obj.scp,
      denyLeaveOrg: obj.denyLeaveOrg,
    },
  };
  console.log("\nCreating new compliance-monitoring obj... ");
  console.log(JSON.stringify(dynamoPutObj));

  await putNoKey(`${process.env.FINOPS_STAGE}-compliance-monitoring`, {
    ...dynamoPutObj,
    id: v4(),
  });
}
