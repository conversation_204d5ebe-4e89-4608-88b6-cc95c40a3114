# Environment Configuration - Homologation
NODE_ENV=production
STAGE=hml

# API Configuration
API_NAME=dsm-back-end
VERSION=v1

# AWS Configuration
AWS_REGION_LOCATION=us-east-1
ACCOUNT_ID=************

# AWS Secrets Manager ARNs
JWT_DECRIPTION_CREDENTIALS=arn:aws:secretsmanager:us-east-1:************:secret:dsm-token-encription-credentials-Fk8enl
DSM_API_SECREAT_MANAGER=arn:aws:secretsmanager:us-east-1:************:secret:dsm-api-credentials-XXXXX
DSM_API_WAF_ARN=arn:aws:secretsmanager:us-east-1:************:secret:dsm-waf-token-XXXXX

# Cognito Configuration
USER_POOL_ID=us-east-1
AWS_API_GATEWAY_COGNITO_NAME=dsm-cognito-authorizer

# Development Features
ENABLE_DEBUG_LOGS=false
ENABLE_DETAILED_LOGGING=true

# External APIs
DSM_API_URL=https://api.hml.dsm.com.br
