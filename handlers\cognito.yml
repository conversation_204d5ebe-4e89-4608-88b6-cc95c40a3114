cognito-read-users:
  handler: src/functions/cognito/read.handler
  name: ${self:custom.dotenv.API_NAME}-cognito-read-users
  description: Função para leitura de usuários do Cognito
  memorySize: 128
  events:
    - http:
        path: /cognito/read
        method: get
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}

update-status:
  handler: src/functions/cognito/updateStatus.handler
  name: ${self:custom.dotenv.API_NAME}-update-status-v2
  description: Função para atualização do status do usuário
  memorySize: 128
  events:
    - http:
        path: /cognito/update/status
        method: put
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}

update-role:
  handler: src/functions/cognito/updateRole.handler
  name: ${self:custom.dotenv.API_NAME}-update-role-v2
  description: Função para atualização da role do usuário
  memorySize: 128
  events:
    - http:
        path: /cognito/update/role
        method: put
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}

token:
  handler: src/functions/cognito/token.handler
  name: ${self:custom.dotenv.API_NAME}-token
  description: Função para obter um token do cognito
  memorySize: 128
  events:
    - http:
        path: /cognito/token
        method: post
        cors: true

access-token:
  handler: src/functions/cognito/accessToken.handler
  name: ${self:custom.dotenv.API_NAME}-access-token-v2
  description: Função para obter um token do OTRS do cognito
  memorySize: 128
  events:
    - http:
        path: /cognito/access-token
        method: get
        cors:
          origin: "*"
          headers: "*"
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}

