import axios from "axios";

const URL_JIRA =
  process.env.URL_JIRA || "https://dosystems.atlassian.net/rest/api/3";
const USER_JIRA = process.env.USER_JIRA || "<EMAIL>";
const PASS_JIRA = process.env.PASS_JIRA || "Aqmc2JkwElVglmRNWqFAF510";

export function createProvider() {
  return axios.create({
    baseURL: URL_JIRA,
    auth: {
      username: USER_JIRA,
      password: PASS_JIRA,
    },
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
    },
  });
}
