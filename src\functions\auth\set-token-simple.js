/**
 * Versão simplificada da função set-token para diagnóstico
 * Não usa Secrets Manager - apenas para testar se o problema é específico do SM
 */

const jwt = require('jsonwebtoken');
const { parseBody } = require('../../shared/parsers');
const { responseWithSuccess, responseWithBadRequest, responseWithError } = require('../../shared/response-cjs');
const { createSecureCookie } = require('../../shared/auth/cookie-utils-cjs');
const { withPublicCors } = require('../../shared/cors/cors-middleware');

/**
 * Valida token do Cognito (versão simplificada)
 */
function validateCognitoTokenSimple(token) {
  try {
    // Apenas decodifica sem verificar assinatura (para teste)
    const decoded = jwt.decode(token);
    
    if (!decoded) {
      throw new Error('Token inválido - não foi possível decodificar');
    }
    
    if (!decoded.iss || !decoded.iss.includes('cognito-idp')) {
      throw new Error('Token não é do Cognito');
    }
    
    if (!decoded.token_use || decoded.token_use !== 'id') {
      throw new Error('Token não é um ID token');
    }
    
    if (!decoded.exp || decoded.exp < Math.floor(Date.now() / 1000)) {
      throw new Error('Token expirado');
    }
    
    if (!decoded.sub || !decoded.email) {
      throw new Error('Token não contém informações necessárias');
    }
    
    return decoded;
    
  } catch (error) {
    throw new Error(`Erro na validação do token Cognito: ${error.message}`);
  }
}

/**
 * Gera token JWT interno simples (sem Secrets Manager)
 */
function generateSimpleToken(payload) {
  try {
    // Usa secret fixo apenas para teste
    const secret = process.env.JWT_SECRET || 'test-secret-for-debugging';
    
    const tokenPayload = {
      sub: payload.sub,
      userId: payload.sub,
      email: payload.email,
      role: 'user',
      permissions: ['read'],
      name: payload.email.split('@')[0],
      provider: 'cognito',
      groups: payload['cognito:groups'] || [],
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (8 * 60 * 60) // 8 horas
    };
    
    return jwt.sign(tokenPayload, secret, { algorithm: 'HS256' });
    
  } catch (error) {
    throw new Error(`Erro ao gerar token: ${error.message}`);
  }
}

/**
 * Handler principal simplificado
 */
const setTokenSimpleHandler = async (event, context) => {
  try {
    console.log('🚀 Iniciando set-token-simple');
    console.log('Event:', JSON.stringify(event, null, 2));
    
    // Parse do body
    const body = parseBody(event);
    console.log('Body parseado:', body);
    
    if (!body || !body.token) {
      console.log('❌ Token não encontrado no body');
      return responseWithBadRequest('Token é obrigatório');
    }
    
    console.log('🔍 Token recebido:', body.token.substring(0, 50) + '...');
    
    // Decodifica token para verificar tipo
    const tokenDecoded = jwt.decode(body.token);
    console.log('Token decodificado:', {
      iss: tokenDecoded?.iss,
      sub: tokenDecoded?.sub,
      email: tokenDecoded?.email,
      token_use: tokenDecoded?.token_use
    });
    
    let decoded;
    
    if (tokenDecoded && tokenDecoded.iss && tokenDecoded.iss.includes('cognito-idp')) {
      console.log('✅ Token do Cognito detectado');
      
      try {
        decoded = validateCognitoTokenSimple(body.token);
        console.log('✅ Token Cognito validado:', {
          sub: decoded.sub,
          email: decoded.email,
          exp: decoded.exp
        });
      } catch (cognitoError) {
        console.error('❌ Erro ao validar token Cognito:', cognitoError.message);
        return responseWithBadRequest(`Token Cognito inválido: ${cognitoError.message}`);
      }
    } else {
      console.log('❌ Token não é do Cognito');
      return responseWithBadRequest('Token deve ser do AWS Cognito');
    }
    
    // Gera token interno
    console.log('🔧 Gerando token interno...');
    let internalToken;
    
    try {
      internalToken = generateSimpleToken(decoded);
      console.log('✅ Token interno gerado:', internalToken.substring(0, 50) + '...');
    } catch (tokenError) {
      console.error('❌ Erro ao gerar token interno:', tokenError.message);
      return responseWithError(`Erro ao gerar token interno: ${tokenError.message}`);
    }
    
    // Cria cookie
    console.log('🍪 Criando cookie...');
    let cookieHeader;
    
    try {
      cookieHeader = createSecureCookie('auth_token', internalToken, {
        httpOnly: true,
        secure: true,
        sameSite: 'None',
        maxAge: 8 * 60 * 60, // 8 horas
        domain: '.dsm.darede.com.br',
        path: '/'
      });
      console.log('✅ Cookie criado:', cookieHeader.substring(0, 100) + '...');
    } catch (cookieError) {
      console.error('❌ Erro ao criar cookie:', cookieError.message);
      return responseWithError(`Erro ao criar cookie: ${cookieError.message}`);
    }
    
    // Resposta de sucesso
    console.log('✅ Processo concluído com sucesso');
    
    return responseWithSuccess('Token definido com sucesso', {
      message: 'Token armazenado em cookie HttpOnly seguro',
      user: {
        id: decoded.sub,
        email: decoded.email,
        name: decoded.email.split('@')[0]
      },
      tokenInfo: {
        provider: 'cognito',
        expiresIn: '8 hours'
      }
    }, {
      'Set-Cookie': cookieHeader
    });
    
  } catch (error) {
    console.error('❌ Erro geral na função:', error);
    console.error('Stack trace:', error.stack);
    
    return responseWithError('Erro interno do servidor', {
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
};

// Exporta handler com middleware CORS
exports.handler = withPublicCors(setTokenSimpleHandler);
