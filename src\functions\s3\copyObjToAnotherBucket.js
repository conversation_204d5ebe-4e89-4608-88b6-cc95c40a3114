import { makeS3 } from "../../shared/services/s3-service";
import { errorValidator } from "../../shared/validators";
import { parseBody } from "../../shared/parsers";

import { parse } from "aws-multipart-parser";

import {
  STATUS_CODE,
  responseWithError,
  responseWithSuccess,
  responseWithBadRequest,
} from "../../shared/response";

const s3 = makeS3();

export const handler = async (event) => {
  try {
    const body = parseBody(event);

    const sourceBucketName = body.sourceBucketName;
    const destinationBucketName = body.destinationBucketName;
    const fileKey = body.fileKey;
    const fileName = body.fileName;
    // const contentType = body.contentType;

    console.log({ sourceBucketName });
    console.log({ destinationBucketName });
    console.log({ fileKey });
    console.log({ fileName });
    // console.log({ contentType });

    let params = {
      Bucket: destinationBucketName,
      CopySource: encodeURI(`/${sourceBucketName}${fileKey}`),
      Key: encodeURI(fileName),
      ContentEncoding: "base64",
      // ContentType: contentType,
    };

    console.log({ params });

    const responseS3 = await s3.copyObject(params).promise();
    console.log({ responseS3 });

    return responseWithSuccess(responseS3, "Upload realizado com sucesso");
  } catch (error) {
    const { statusCode, message } = errorValidator(error);

    if (STATUS_CODE.BAD_REQUEST === statusCode)
      return responseWithBadRequest(message);

    return responseWithError(message);
  }
};
