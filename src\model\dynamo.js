// Importando módulos
import { DynamoDB } from "aws-sdk";
import { crmApiProvider } from "../providers/crm-api-provider";
import { v4 } from "uuid";
import { DatabaseEntity } from "../entities/database-entity";

const cli = new DynamoDB.DocumentClient({
  region: process.env.AWS_REGION_LOCATION,
});

const dynamoEntity = new DatabaseEntity(cli);

// Exportando função de inserir dados no DynamoDB
export async function create(table, data) {
  if (table.endsWith("-customers") || table.endsWith("-contracts")) {
    data.id = v4();
    const dsmID = await newCreateRegister(table, data);

    let endpointsNames = {
      customer: {
        pipedrive: "customer",
        sales: "customer",
      },
      contract: {
        pipedrive: "deal",
        sales: "contract",
      },
    };

    let endpoints = null;
    if (table.endsWith("-customers")) {
      endpoints = endpointsNames["customer"];
    } else {
      endpoints = endpointsNames["contract"];
    }

    // try {
    //   console.log(`Update Pipedrive with DSM ID ${dsmID}`);
    //   const payload = { dsmID };
    //   await updateCRMItem(
    //     `pipedrive/dsmid/${endpoints.pipedrive}/${data.identifications.crm_id}`,
    //     payload
    //   );
    //   console.log(`DSM ID updated successfully on Pipedrive`);
    // } catch (error) {
    //   console.log("Unable to update DSM ID on Pipedrive");
    //   console.log(error);
    // }

    if (data.identifications?.crm_id && isNaN(data.identifications?.crm_id)) {
      try {
        console.log(`Update Sales Force with DSM ID ${dsmID}`);
        const payload = {
          dsm_id: dsmID,
          crm_id: data.identifications.crm_id,
        };
        await updateCRMItem(`salesforce/${endpoints.sales}`, payload);
        console.log(`DSM ID updated successfully on Sales Force`);
      } catch (error) {
        console.log("Unable to update DSM ID on Sales Force");
        console.log(error);
      }
    }
  } else {
    data.id = v4();
    return await cli
      .put({
        TableName: table,
        Item: data,
      })
      .promise();
  }
}

async function newCreateRegister(table, data) {
  const id = await generateSequentialId(table);

  if (!id) {
    console.log({ data });
    throw new Error("Não foi possivel gerar o DSM ID criar o registro");
  }

  let type = "";
  if (table.toUpperCase().includes("CUSTOMERS")) type = "CL";

  if (table.toUpperCase().includes("CONTRACTS")) type = "CT";

  //data.id = `${type}${id.toString().padStart(6, "0")}`;
  data.dsm_id = `${type}${id.toString().padStart(6, "0")}`;

  if (table.toUpperCase().includes("CUSTOMERS")) {
    data.contacts = Array.isArray(data.contacts) ? data.contacts : [];
    if (data.contacts) {
      let lastID = 0;
      data.contacts.forEach((c) => {
        if (c.dsm_id) {
          const [customerID, contactID] = c.dsm_id.split("-");
          if (contactID && Number(contactID) > lastID)
            lastID = Number(contactID);
        }
      });

      let customerID = data.dsm_id.replace("CL", "PE");
      data.contacts.forEach((c) => {
        if (!c.dsm_id) {
          lastID++;
          c.dsm_id = `${customerID}-${lastID}`;
        }
      });
    }
  }
  await cli
    .put({
      TableName: table,
      Item: data,
    })
    .promise();

  return data.dsm_id;
}

async function generateSequentialId(table) {
  const tabelaNome = `${process.env.FINOPS_STAGE}-dsm-ids`;
  let key = "";

  if (table.toUpperCase().includes("CUSTOMERS")) key = "customers";

  if (table.toUpperCase().includes("CONTRACTS")) key = "contracts";

  try {
    const response = await cli
      .update({
        TableName: tabelaNome,
        Key: { id: key },
        UpdateExpression: "SET #value = #value + :incr",
        ExpressionAttributeNames: {
          "#value": "value",
        },
        ExpressionAttributeValues: {
          ":incr": 1,
        },
        ReturnValues: "UPDATED_NEW",
        ConditionExpression: "attribute_exists(id)", // Certifica-se de que a chave exista
      })
      .promise();

    const newID = response.Attributes.value;

    return newID;
  } catch (error) {
    console.error("Error to generate sequential ID:", error);
    return null;
  }
}

// Exportando função de ler os dados do DynamoDB
export async function readAll(table) {
  const params = {
    TableName: table,
  };

  const scanResults = { Items: [] };
  let items;

  do {
    items = await cli.scan(params).promise();
    items.Items.forEach((item) => scanResults.Items.push(item));
    params.ExclusiveStartKey = items.LastEvaluatedKey;
  } while (typeof items.LastEvaluatedKey !== "undefined");

  return scanResults;
}

export async function readAuditsByDateRange({ startDate, endDate, type }) {
  console.log({ startDate, endDate, type });
  let verifiedParam = parseInt(type);
  console.log({ verifiedParam });
  const params = {
    IndexName: "verified-index",
    TableName: `${process.env.FINOPS_STAGE}-switch-role`,
    ExpressionAttributeNames: {
      "#verified": "verified",
      "#created_at": "created_at",
    },
    KeyConditionExpression: "#verified = :type",
    FilterExpression: `#created_at BETWEEN :start AND :end`,
    ExpressionAttributeValues: {
      ":start": new Date(startDate).toISOString(),
      ":end": new Date(endDate).toISOString(),
      ":type": verifiedParam,
    },
  };

  const scanResults = { Items: [] };
  let items;

  do {
    items = await cli.query(params).promise();
    items.Items.forEach((item) => scanResults.Items.push(item));
    params.ExclusiveStartKey = items.LastEvaluatedKey;
  } while (typeof items.LastEvaluatedKey !== "undefined");

  return scanResults;
}
export async function checkSwitchRoleSolicitation(params) {
  const scanResults = { Items: [] };
  let items;

  do {
    items = await cli.query(params).promise();
    items.Items.forEach((item) => scanResults.Items.push(item));
    params.ExclusiveStartKey = items.LastEvaluatedKey;
  } while (typeof items.LastEvaluatedKey !== "undefined");

  return items;
}

export async function readSwitchRoleSolicitations() {
  let shouldDisplay = 1;

  const params = {
    IndexName: "switch_role_display-index",
    TableName: `${process.env.FINOPS_STAGE}-switch-role`,
    ExpressionAttributeNames: {
      "#switch_role_display": "switch_role_display",
    },
    KeyConditionExpression: "#switch_role_display = :switch_role_display",

    FilterExpression: `active = :ask OR  active = :approved`,
    ExpressionAttributeValues: {
      ":switch_role_display": shouldDisplay,
      ":approved": "Aprovado",
      ":ask": "Solicitado",
    },
  };

  const scanResults = { Items: [] };
  let items;

  do {
    items = await cli.query(params).promise();
    items.Items.forEach((item) => scanResults.Items.push(item));
    params.ExclusiveStartKey = items.LastEvaluatedKey;
  } while (typeof items.LastEvaluatedKey !== "undefined");

  return items;
}

export async function readAudits({ month, year, nextPage }) {
  let params = {
    TableName: `${process.env.FINOPS_STAGE}-audits`,
    FilterExpression: "contains(created_at, :date)",
    ExpressionAttributeValues: {
      ":date": `${year}-${parseInt(month) < 10 ? `0${month}` : month}`,
    },
  };

  if (nextPage) params.ExclusiveStartKey = { id: nextPage };

  const { Items, LastEvaluatedKey } = await cli.scan(params).promise();

  return {
    audits: Items,
    lastEvaluatedKey: LastEvaluatedKey,
  };
}

export async function readCustomers({ nextPage }) {
  let params = {
    TableName: `${process.env.FINOPS_STAGE}-customers`,
  };

  if (nextPage) params.ExclusiveStartKey = { id: nextPage };

  const { Items, LastEvaluatedKey } = await cli.scan(params).promise();

  return {
    customers: Items,
    lastEvaluatedKey: LastEvaluatedKey,
  };
}

export async function readPaginate({ nextPage, dynamodb }) {
  let params = {
    TableName: dynamodb,
  };

  if (nextPage) params.ExclusiveStartKey = { id: nextPage };

  const { Items, LastEvaluatedKey } = await cli.scan(params).promise();

  return {
    data: Items,
    lastEvaluatedKey: LastEvaluatedKey,
  };
}

// Exportando função de ler apenas 1 registro do DynamoDB
export async function readOne(table, id) {
  return await cli
    .get({
      TableName: table,
      Key: {
        id: id,
      },
    })
    .promise();
}

// Exportando função de ler apenas 1 registro do DynamoDB
export async function readByCRM(table, crm) {
  let params = {
    TableName: table,
  };

  const customers = { Items: [] };
  let items;

  do {
    items = await cli.scan(params).promise();
    items.Items.forEach((item) => customers.Items.push(item));
    params.ExclusiveStartKey = items.LastEvaluatedKey;
  } while (typeof items.LastEvaluatedKey !== "undefined");

  return customers.Items.find(
    (obj) => obj.identifications?.crm_id?.toString() === crm?.toString()
  );
}

// Exportando função de ler apenas 1 registro do DynamoDB
export async function readByITSM(table, itsm) {
  let params = {
    TableName: table,
  };

  const customers = { Items: [] };
  let items;

  do {
    items = await cli.scan(params).promise();
    items.Items.forEach((item) => customers.Items.push(item));
    params.ExclusiveStartKey = items.LastEvaluatedKey;
  } while (typeof items.LastEvaluatedKey !== "undefined");

  return customers.Items.find(
    (obj) => obj?.identifications?.itsm_id?.toString() === itsm?.toString()
  );
}

export async function readAccountsByCustomerDsmID(table, dsm_id) {
  const customerAccounts = [];

  if (dsm_id) {
    const customers = await dynamoEntity.getCustomersByScan(table);

    customers.Items.forEach((c) => {
      customerAccounts.push({
        owner_id: c.dsm_id,
        owner_name: c?.names?.fantasy_name
          ? c?.names?.fantasy_name
          : c?.names?.name,
        accounts: c.accounts,
      });
    });

    const currentCustomer = customerAccounts.find(
      (obj) => obj?.owner_id?.toString() === dsm_id?.toString()
    );

    return currentCustomer;
  } else {
    throw new Error(
      `Houve um erro inesperado!\nNão foi possível obter as contas do cliente "${dsm_id}"`
    );
  }
}

export async function readAllAccounts(table) {
  const allAccounts = [];
  let customers;

  if (table) {
    customers = await dynamoEntity.getCustomersByScan(table);
    customers.Items.forEach((item) => {
      allAccounts.push({
        owner_id: item.dsm_id,
        owner_name: item?.names?.fantasy_name || item?.names?.name,
        accounts: item.accounts,
      });
    });
    return allAccounts;
  } else {
    throw new Error(
      `A tabela "${table}" é inválida ou não existe!\nNão foi possível buscar as contas AWS dos clientes`
    );
  }
}

export async function readByDeal(table, deal) {
  const proposals = await cli
    .scan({
      TableName: table,
    })
    .promise();

  return (
    proposals.Items.filter(
      (currentProposal) => currentProposal.mainOportunity === deal
    ) ||
    proposals.Items.filter((currentProposal) =>
      currentProposal.opportunity.find((currentDeal) => currentDeal === deal)
    )
  );
}

export async function readCustomerByStatus(table, status, statusContract) {
  let params = {
    TableName: table,
    IndexName: "active-index",
    ExpressionAttributeNames: {
      "#active": "active",
    },
    KeyConditionExpression: "#active = :active",
    FilterExpression: `active = :active`,
    ExpressionAttributeValues: {
      ":active": parseInt(status),
    },
  };

  const scanResults = { Items: [] };
  let items;

  do {
    items = await cli.query(params).promise();
    items.Items.forEach((item) => scanResults.Items.push(item));
    params.ExclusiveStartKey = items.LastEvaluatedKey;
  } while (typeof items.LastEvaluatedKey !== "undefined");

  return scanResults;
}

// Exportando função de atualização de registros do DynamoDB
export async function put(table, id, data) {
  let dataCopy = JSON.stringify(data);
  dataCopy = JSON.parse(dataCopy);
  if (table.toUpperCase().includes("CUSTOMERS") && data.contacts) {
    data.contacts.Value = Array.isArray(data.contacts.Value)
      ? data.contacts.Value
      : [];
    if (data.contacts.Value) {
      let lastID = 0;
      data.contacts.Value.forEach((c, index) => {
        if (c.dsm_id) {
          const [customerID, contactID] = c.dsm_id.split("-");
          if (contactID && Number(contactID) > lastID)
            lastID = Number(contactID);

          dataCopy.contacts.Value[index].existsDSM_ID = true;
        }
      });

      let customerID = data?.dsm_id?.Value.replace("CL", "PE");
      data.contacts.Value.forEach((c) => {
        if (!c.dsm_id) {
          lastID++;
          c.dsm_id = `${customerID}-${lastID}`;
        }
      });
    }
  }

  const response = await cli
    .update({
      TableName: table,
      Key: {
        id: id,
      },
      AttributeUpdates: data,
      ReturnValues: "UPDATED_NEW",
    })
    .promise();

  if (table.toUpperCase().includes("CUSTOMERS")) {
    if (data.contacts?.Value) {
      console.log("Updating contacts of customer");
      data.contacts.Value = Array.isArray(data.contacts.Value)
        ? data.contacts.Value
        : [];

      for (let index = 0; index <= data.contacts.Value.length; index++) {
        const contact = data.contacts.Value[index];
        console.log(contact, dataCopy?.contacts?.Value[index]?.existsDSM_ID);
        console.log(
          "DSM ID already exists, skip update pipedrive and salesforce"
        );
        if (dataCopy?.contacts?.Value[index]?.existsDSM_ID) continue;

        // try {
        //   console.log(`Update Pipedrive with DSM ID ${contact.dsm_id}`);
        //   const payload = { dsmID: contact.dsm_id };
        //   await updateCRMItem(
        //     `pipedrive/dsmid/contact/${contact.identifications.crm_id}`,
        //     payload
        //   );
        //   console.log(`DSM ID updated successfully on Pipedrive`);
        // } catch (error) {
        //   console.log("Unable to update DSM ID on Pipedrive");
        //   console.log(error);
        // }

        if (
          contact?.identifications?.crm_id &&
          isNaN(contact?.identifications?.crm_id)
        ) {
          try {
            console.log(`Update Sales Force with DSM ID ${contact.dsm_id}`);
            const payload = {
              dsm_id: contact.dsm_id,
              crm_id: contact.identifications.crm_id,
            };
            await updateCRMItem(`salesforce/contact`, payload);
            console.log(`DSM ID updated successfully on Sales Force`);
          } catch (error) {
            console.log("Unable to update DSM ID on Sales Force");
            console.log(error);
          }
        }
      }
    } else {
      console.log("This customer hasn't contacts");
    }
  }

  return response.Attributes;
}

// Exportando função de atualização de registros do DynamoDB
export async function putNoKey(table, data) {
  return await cli
    .put({
      TableName: table,
      Item: data,
    })
    .promise();
}

// Exportando função que realiza a deleção de um dado no DynamoDB
export async function delt(table, id) {
  return await cli
    .delete({
      TableName: table,
      Key: {
        id: id,
      },
    })
    .promise();
}

async function updateCRMItem(path, payload) {
  try {
    const provider = await crmApiProvider();
    await provider.put(path, payload);
  } catch (error) {
    console.log(error);
    return null;
  }
}

export async function readByIndex(table, indexValue, indexName) {
  try {
    const result = await cli
      .query({
        TableName: table,
        IndexName: `${indexName}-index`,
        ExpressionAttributeNames: {
          "#index": indexName,
        },
        KeyConditionExpression: "#index = :index",
        ExpressionAttributeValues: {
          ":index": indexValue,
        },
      })
      .promise();
    return result;
  } catch (error) {
    console.log(error);
    return null;
  }
}
