#!/usr/bin/env node

/**
 * Script para testar o endpoint MFA corrigido
 */

const axios = require('axios');

console.log('🔐 TESTE DO ENDPOINT MFA CORRIGIDO');
console.log('==================================\n');

const API_BASE_URL = 'https://pdu9dbemj1.execute-api.us-east-1.amazonaws.com/dev';
const FRONTEND_ORIGIN = 'https://dev.dsm.darede.com.br';

/**
 * Testa endpoint GET /mfa (novo)
 */
async function testMfaGet() {
  console.log('🧪 Teste 1: GET /mfa (novo endpoint)\n');
  
  const testCases = [
    {
      name: 'Código simples',
      params: { code: '123456' }
    },
    {
      name: 'Verificação de código',
      params: { code: '123456', action: 'verify' }
    },
    {
      name: 'Status MFA',
      params: { code: '123456', action: 'status', user: '<EMAIL>' }
    },
    {
      name: 'Lista usuários',
      params: { action: 'list' }
    }
  ];
  
  for (const testCase of testCases) {
    try {
      console.log(`🔍 Testando: ${testCase.name}`);
      
      const queryString = new URLSearchParams(testCase.params).toString();
      const url = `${API_BASE_URL}/mfa?${queryString}`;
      
      console.log(`   URL: ${url}`);
      
      const response = await axios.get(url, {
        headers: {
          'Origin': FRONTEND_ORIGIN,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      
      console.log(`   ✅ Status: ${response.status}`);
      console.log(`   📄 Response:`, response.data);
      console.log('');
      
    } catch (error) {
      if (error.response) {
        console.log(`   ❌ Status: ${error.response.status}`);
        console.log(`   📄 Error:`, error.response.data);
      } else {
        console.log(`   ❌ Erro de conexão:`, error.message);
      }
      console.log('');
    }
  }
}

/**
 * Testa endpoint POST /mfa (existente)
 */
async function testMfaPost() {
  console.log('🧪 Teste 2: POST /mfa (endpoint existente)\n');
  
  const testCases = [
    {
      name: 'Leitura de configuração',
      body: { only_read: 1 }
    },
    {
      name: 'Adicionar MFA',
      body: { user: '<EMAIL>', secret: 'test-secret', only_read: 0 }
    }
  ];
  
  for (const testCase of testCases) {
    try {
      console.log(`🔍 Testando: ${testCase.name}`);
      console.log(`   Body:`, testCase.body);
      
      const response = await axios.post(`${API_BASE_URL}/mfa`, testCase.body, {
        headers: {
          'Origin': FRONTEND_ORIGIN,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      
      console.log(`   ✅ Status: ${response.status}`);
      console.log(`   📄 Response:`, response.data);
      console.log('');
      
    } catch (error) {
      if (error.response) {
        console.log(`   ❌ Status: ${error.response.status}`);
        console.log(`   📄 Error:`, error.response.data);
      } else {
        console.log(`   ❌ Erro de conexão:`, error.message);
      }
      console.log('');
    }
  }
}

/**
 * Testa endpoint de configuração de autenticação
 */
async function testAuthConfig() {
  console.log('🧪 Teste 3: GET /auth/config (configuração corrigida)\n');
  
  try {
    const response = await axios.get(`${API_BASE_URL}/auth/config`, {
      headers: {
        'Origin': FRONTEND_ORIGIN,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    console.log(`✅ Status: ${response.status}`);
    console.log('📋 Configuração de autenticação:');
    console.log(`   baseURL: ${response.data.instructions?.axios?.baseURL}`);
    console.log(`   withCredentials: ${response.data.instructions?.axios?.withCredentials}`);
    console.log(`   httpOnlySupported: ${response.data.auth?.httpOnlySupported}`);
    console.log('');
    
    // Verifica se a URL está correta
    const expectedURL = 'https://pdu9dbemj1.execute-api.us-east-1.amazonaws.com/dev';
    const actualURL = response.data.instructions?.axios?.baseURL;
    
    if (actualURL === expectedURL) {
      console.log('✅ URL da API está correta!');
    } else {
      console.log('❌ URL da API está incorreta:');
      console.log(`   Esperado: ${expectedURL}`);
      console.log(`   Atual: ${actualURL}`);
    }
    
  } catch (error) {
    if (error.response) {
      console.log(`❌ Status: ${error.response.status}`);
      console.log(`📄 Error:`, error.response.data);
    } else {
      console.log(`❌ Erro de conexão:`, error.message);
    }
  }
  
  console.log('');
}

/**
 * Simula o fluxo do frontend
 */
async function simulateFrontendFlow() {
  console.log('🧪 Teste 4: Simulação do fluxo do frontend\n');
  
  try {
    // 1. Frontend busca configuração
    console.log('1️⃣ Buscando configuração de autenticação...');
    const configResponse = await axios.get(`${API_BASE_URL}/auth/config`, {
      headers: { 'Origin': FRONTEND_ORIGIN }
    });
    
    const apiBaseURL = configResponse.data.instructions?.axios?.baseURL;
    console.log(`   ✅ API Base URL obtida: ${apiBaseURL}`);
    
    // 2. Frontend faz request para MFA usando a URL correta
    console.log('2️⃣ Fazendo request para MFA com URL correta...');
    const mfaResponse = await axios.get(`${apiBaseURL}/mfa?code=test-code-123`, {
      headers: { 'Origin': FRONTEND_ORIGIN }
    });
    
    console.log(`   ✅ MFA Status: ${mfaResponse.status}`);
    console.log(`   📄 MFA Response:`, mfaResponse.data);
    
    console.log('\n🎉 Fluxo do frontend simulado com sucesso!');
    
  } catch (error) {
    console.log('\n❌ Erro no fluxo do frontend:');
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Error:`, error.response.data);
    } else {
      console.log(`   Erro de conexão:`, error.message);
    }
  }
}

/**
 * Função principal
 */
async function main() {
  console.log('🚀 Iniciando testes do endpoint MFA corrigido\n');
  
  await testAuthConfig();
  await testMfaGet();
  await testMfaPost();
  await simulateFrontendFlow();
  
  console.log('📊 RELATÓRIO FINAL');
  console.log('==================\n');
  
  console.log('✅ Correções implementadas:');
  console.log('   - URL da API corrigida no /auth/config');
  console.log('   - Endpoint GET /mfa criado');
  console.log('   - Endpoint POST /mfa corrigido');
  console.log('   - Formato de resposta padronizado');
  
  console.log('\n💡 Próximos passos:');
  console.log('   1. Frontend deve buscar configuração em /auth/config');
  console.log('   2. Usar a baseURL retornada para todas as requisições');
  console.log('   3. Requests para /mfa agora devem funcionar');
  
  console.log('\n🎯 URL correta da API:');
  console.log('   https://pdu9dbemj1.execute-api.us-east-1.amazonaws.com/dev');
}

// Executa apenas se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
}
