import { accessKeysCustomerAccount } from "../../accessKeysCustomerAccount";

const AWS = require("aws-sdk");
const region = "us-east-1";

export async function verifyMFA(account) {
  console.log("\nVerifying MFA...");

  const newSession = await accessKeysCustomerAccount(account);

  if (newSession) {
    const iam = new AWS.IAM({
      region,
      credentials: {
        accessKeyId: newSession.access_key_id,
        secretAccessKey: newSession.secret_key_id,
        sessionToken: newSession.session_token,
      },
    });

    const mfa = await iam
      .listVirtualMFADevices({})
      .promise()
      .catch(() => {
        return false;
      });
    if (!mfa) return false;

    const devices = mfa.VirtualMFADevices;
    for (let i = 0; i < devices.length; i++) {
      if (devices[i].SerialNumber.includes("root-account-mfa-device")) {
        return true;
      } else {
        return false;
      }
    }
    return false;
  }
  return false;
}
