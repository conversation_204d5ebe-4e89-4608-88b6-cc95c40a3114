mfa:
  handler: src/functions/mfa/all.handler
  name: ${self:custom.dotenv.STAGE}-mfa${self:custom.dotenv.VERSION}
  description: Função para realizar o tratamento com o MFA
  memorySize: 128
  events:
    - http:
        path: /mfa
        method: post
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}

