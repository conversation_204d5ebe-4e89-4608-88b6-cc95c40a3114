/**
 * Endpoint para fornecer configurações de autenticação para o frontend
 * Retorna informações sobre suporte a cookies HttpOnly e configurações CORS
 */

const { responseWithSuccess } = require('../../shared/response-cjs');
const { withPublicCors } = require('../../shared/cors/cors-middleware');

const configHandler = async (event, context) => {
  console.log('Auth config endpoint called');

  try {
    const config = {
      // Configurações de autenticação
      auth: {
        httpOnlySupported: true,
        cookiesEnabled: true,
        withCredentials: true,
        tokenStorage: 'cookies', // 'cookies' ou 'localStorage'
        fallbackToHeaders: true,
        cognitoIntegration: true
      },

      // Configurações de cookies
      cookies: {
        accessTokenName: 'dsm_access_token',
        refreshTokenName: 'dsm_refresh_token',
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        path: '/',
        httpOnly: true
      },

      // Configurações CORS
      cors: {
        allowCredentials: true,
        allowedOrigins: process.env.ALLOWED_ORIGINS ?
          process.env.ALLOWED_ORIGINS.split(',') : ['*']
      },

      // Configurações do Cognito
      cognito: {
        userPoolId: process.env.USER_POOL_ID,
        region: process.env.AWS_REGION_LOCATION || 'us-east-1',
        tokenTypes: ['id', 'access'],
        supportedFlows: ['USER_PASSWORD_AUTH', 'ADMIN_NO_SRP_AUTH']
      },

      // Endpoints disponíveis
      endpoints: {
        config: '/auth/config',
        checkCookieSupport: '/auth/check-cookie-support',
        cognitoToCookie: '/auth/cognito-to-cookie',
        setToken: '/auth/set-token',
        verify: '/auth/verify',
        refresh: '/auth/refresh',
        logout: '/auth/logout',
        mfa: '/mfa',
        cognitoRead: '/cognito/read',
        cognitoToken: '/cognito/token'
      },
      
      // Configurações de token
      tokens: {
        accessTokenExpiry: process.env.JWT_EXPIRES_IN || '8h',
        refreshTokenExpiry: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
        autoRefresh: true,
        refreshThreshold: 30 // minutos antes da expiração
      },
      
      // Informações do ambiente
      environment: {
        nodeEnv: process.env.NODE_ENV || 'development',
        stage: process.env.STAGE || 'dev',
        timestamp: new Date().toISOString()
      },
      
      // Instruções para o frontend
      instructions: {
        axios: {
          withCredentials: true,
          baseURL: process.env.API_BASE_URL || 'https://pdu9dbemj1.execute-api.us-east-1.amazonaws.com/dev',
          headers: {
            'Content-Type': 'application/json'
          }
        },
        usage: [
          '1. Configure axios.defaults.withCredentials = true',
          '2. Use POST /auth/set-token após login bem-sucedido',
          '3. Cookies serão enviados automaticamente nas próximas requisições',
          '4. Use GET /auth/verify para verificar status de autenticação',
          '5. Implemente interceptor para refresh automático'
        ]
      }
    };
    
    console.log('Auth config returned successfully');
    
    return responseWithSuccess(config);
    
  } catch (error) {
    console.error('Erro no auth config:', error);
    
    return responseWithSuccess({
      error: 'Erro ao obter configurações de autenticação',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
};

// Exporta handler com middleware CORS público
exports.handler = withPublicCors(configHandler);
