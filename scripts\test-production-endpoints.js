/**
 * Script para testar os endpoints de autenticação em produção
 * Verifica se os novos endpoints estão funcionando corretamente
 */

const https = require('https');
const { URL } = require('url');

const BASE_URL = 'https://api.dsm.darede.com.br';
const STAGE = 'dev';

let cookies = '';

console.log('🌐 Testando Endpoints de Autenticação em Produção\n');
console.log(`📍 Base URL: ${BASE_URL}`);
console.log(`🏷️  Stage: ${STAGE}\n`);

// Função helper para fazer requisições HTTPS
function makeHttpsRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const url = new URL(`${BASE_URL}${path}`);
    
    const options = {
      hostname: url.hostname,
      port: url.port || 443,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'DSM-Test-Script/1.0',
        ...headers
      }
    };

    if (cookies) {
      options.headers['Cookie'] = cookies;
    }

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        // Captura cookies da resposta
        if (res.headers['set-cookie']) {
          const newCookies = res.headers['set-cookie'];
          console.log(`   📥 Cookies recebidos: ${newCookies.length} cookie(s)`);
          
          // Simula comportamento do browser
          cookies = newCookies.map(cookie => cookie.split(';')[0]).join('; ');
        }
        
        let parsedData = null;
        try {
          parsedData = responseData ? JSON.parse(responseData) : null;
        } catch (e) {
          parsedData = responseData;
        }
        
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: parsedData
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function testProductionEndpoints() {
  try {
    console.log('1️⃣ Testando endpoint /auth/config...');
    const configResponse = await makeHttpsRequest('GET', `/${STAGE}/auth/config`);
    
    if (configResponse.statusCode === 200) {
      console.log('   ✅ Config endpoint funcionando');
      console.log(`   🔧 httpOnlySupported: ${configResponse.data.auth?.httpOnlySupported}`);
      console.log(`   🔧 withCredentials: ${configResponse.data.auth?.withCredentials}`);
      console.log(`   🔧 tokenStorage: ${configResponse.data.auth?.tokenStorage}`);
      console.log(`   🌍 environment: ${configResponse.data.environment?.nodeEnv}`);
    } else {
      console.log(`   ❌ Config endpoint falhou: ${configResponse.statusCode}`);
      console.log(`   📄 Response: ${JSON.stringify(configResponse.data, null, 2)}`);
    }
    
    console.log('\n2️⃣ Testando endpoint /auth/check-cookie-support...');
    const cookieTestResponse = await makeHttpsRequest('GET', `/${STAGE}/auth/check-cookie-support`);
    
    if (cookieTestResponse.statusCode === 200) {
      console.log('   ✅ Cookie support endpoint funcionando');
      console.log(`   🍪 httpOnlySupported: ${cookieTestResponse.data.httpOnlySupported}`);
      console.log(`   🍪 cookieTestSet: ${cookieTestResponse.data.cookieTestSet}`);
      console.log(`   🍪 environment: ${cookieTestResponse.data.environment}`);
      
      // Verifica se cookie foi realmente definido
      const hasCookies = cookies.length > 0;
      console.log(`   📋 Cookies armazenados: ${hasCookies ? 'Sim' : 'Não'}`);
      
      if (hasCookies) {
        console.log(`   📝 Cookies: ${cookies}`);
      }
    } else {
      console.log(`   ❌ Cookie support endpoint falhou: ${cookieTestResponse.statusCode}`);
      console.log(`   📄 Response: ${JSON.stringify(cookieTestResponse.data, null, 2)}`);
    }
    
    console.log('\n3️⃣ Testando outros endpoints de auth...');
    
    // Testar set-token
    console.log('   🔑 Testando /auth/set-token...');
    const setTokenResponse = await makeHttpsRequest('POST', `/${STAGE}/auth/set-token`, {
      token: 'test-token-for-production-test'
    });
    
    if (setTokenResponse.statusCode === 200) {
      console.log('   ✅ Set-token endpoint funcionando');
    } else {
      console.log(`   ❌ Set-token endpoint falhou: ${setTokenResponse.statusCode}`);
    }
    
    // Testar verify
    console.log('   🔍 Testando /auth/verify...');
    const verifyResponse = await makeHttpsRequest('GET', `/${STAGE}/auth/verify`);
    console.log(`   📊 Verify response: ${verifyResponse.statusCode}`);
    
    // Testar logout
    console.log('   🚪 Testando /auth/logout...');
    const logoutResponse = await makeHttpsRequest('POST', `/${STAGE}/auth/logout`);
    console.log(`   📊 Logout response: ${logoutResponse.statusCode}`);
    
    console.log('\n4️⃣ Resumo dos testes:');
    
    const results = {
      config: configResponse.statusCode === 200,
      cookieSupport: cookieTestResponse.statusCode === 200,
      setToken: setTokenResponse.statusCode === 200,
      verify: verifyResponse.statusCode !== 500,
      logout: logoutResponse.statusCode !== 500
    };
    
    console.log('   📊 Resultados:');
    Object.entries(results).forEach(([endpoint, success]) => {
      console.log(`   ${success ? '✅' : '❌'} ${endpoint}: ${success ? 'OK' : 'FALHOU'}`);
    });
    
    const allWorking = Object.values(results).every(r => r);
    
    if (allWorking) {
      console.log('\n🎉 Todos os endpoints estão funcionando!');
      console.log('   O frontend pode usar os novos utilitários de detecção.');
    } else {
      console.log('\n⚠️ Alguns endpoints precisam de atenção.');
      console.log('   Verifique o deploy e as configurações.');
    }
    
    console.log('\n5️⃣ Próximos passos para o frontend:');
    console.log('   1. Copie os arquivos de frontend-utils/ para seu projeto');
    console.log('   2. Implemente a detecção automática usando initializeAuth()');
    console.log('   3. Teste localmente primeiro');
    console.log('   4. Faça deploy do frontend atualizado');
    
  } catch (error) {
    console.error('❌ Erro no teste:', error.message);
    console.error('   Verifique se o backend está deployado corretamente');
  }
}

// Executa o teste
testProductionEndpoints();
