import { parse } from "aws-multipart-parser";
import { SES } from "aws-sdk";
import nodemailer from "nodemailer";
import { responseWithError, responseWithSuccess } from "../../shared/response";
import { assumeRoleOnRootAccount } from "./sts";

exports.handler = async (event) => {
  try {
    console.log("event: ", event);
    let body;
    if (
      event.headers["Content-Type"] === "application/x-www-form-urlencoded" ||
      event.headers["content-type"] === "application/x-www-form-urlencoded"
    ) {
      const params = new URLSearchParams(event.body);
      body = Object.fromEntries(params.entries());
    } else {
      body = parse(event);
    }
    console.log("body: ", body);

    const decriptSubject = Buffer.from(body.subject, "base64").toString("utf8");
    const decriptHtml = Buffer.from(body.htmlTemplate, "base64").toString(
      "utf8"
    );

    const credentials = await assumeRoleOnRootAccount();

    const ses = new SES({
      region: process.env.AWS_REGION_LOCATION,
      credentials,
    });

    const transporter = nodemailer.createTransport({ SES: ses });
    const dosystemsEmail = process.env.SENDER_EMAIL;

    const message = {
      from: dosystemsEmail,
      to: body.emails,
      subject: decriptSubject,
      html: decriptHtml,
    };

    const response = await transporter.sendMail(message);
    return responseWithSuccess(response, "Email enviado com sucesso");
  } catch (err) {
    console.log(err);
    return responseWithError(err);
  }
};
