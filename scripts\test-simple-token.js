/**
 * Script simples para testar geração e verificação de token
 */

const jwt = require('jsonwebtoken');

// Usa a mesma configuração do ambiente
const JWT_SECRET = process.env.JWT_SECRET || '871c4c458f16a9807c5a53027a62bda3e4c01e06ab755aa243f5b0d4f447e5d9d35e244f9d8ac976c4bc1ce58291fd0796d39c93dce8343a46890542b8a29f3be';

console.log('🧪 Teste simples de JWT\n');

// Gera token
const payload = {
  sub: 'test-user',
  email: '<EMAIL>',
  role: 'user',
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + (8 * 60 * 60)
};

const options = {
  algorithm: 'HS256',
  issuer: 'dsm-api',
  audience: 'dsm-clients'
};

console.log('1. Gerando token...');
const token = jwt.sign(payload, JWT_SECRET, options);
console.log(`✅ Token gerado: ${token.substring(0, 50)}...`);

console.log('\n2. Verificando token...');
try {
  const decoded = jwt.verify(token, JWT_SECRET, {
    issuer: 'dsm-api',
    audience: 'dsm-clients',
    algorithms: ['HS256']
  });
  console.log('✅ Token verificado com sucesso');
  console.log(`   Email: ${decoded.email}`);
  console.log(`   Role: ${decoded.role}`);
} catch (error) {
  console.error('❌ Erro na verificação:', error.message);
}

console.log('\n3. Testando com token inválido...');
try {
  jwt.verify('token.invalido.aqui', JWT_SECRET, {
    issuer: 'dsm-api',
    audience: 'dsm-clients',
    algorithms: ['HS256']
  });
  console.log('❌ Token inválido foi aceito (problema!)');
} catch (error) {
  console.log('✅ Token inválido rejeitado corretamente');
}

console.log('\n🎉 Teste concluído!');
