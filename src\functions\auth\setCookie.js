import { parseBody } from '../../shared/parsers';
import { json, message } from '../../shared/response';
import { enhancedSecretsManager } from '../../shared/services/enhanced-secrets-manager';
import { getCorsMiddleware } from '../../middleware/corsMiddleware';
import { getCookieConfig } from '../../config/environments';
import jwt from 'jsonwebtoken';

/**
 * Verify Cognito JWT token
 */
async function verifyCognitoToken(token) {
  try {
    // Decode without verification first to get header
    const decoded = jwt.decode(token, { complete: true });
    
    if (!decoded) {
      throw new Error('Invalid token format');
    }

    // For Cognito tokens, we need to verify against Cognito's public keys
    // For now, we'll do basic validation and trust the token since it comes from Cognito
    const payload = jwt.decode(token);
    
    // Check if token is expired
    const currentTime = Math.floor(Date.now() / 1000);
    if (payload.exp < currentTime) {
      throw new Error('Token expired');
    }
    
    return payload;
  } catch (error) {
    console.error('Token verification failed:', error);
    throw new Error('Invalid token');
  }
}

/**
 * Create secure HTTP-only cookie header
 */
function createSecureCookieHeader(name, value, maxAge = 8 * 60 * 60) {
  const cookieConfig = getCookieConfig();

  return `${name}=${value}; HttpOnly; Secure=${cookieConfig.secure}; SameSite=${cookieConfig.sameSite}; Max-Age=${maxAge}; Path=${cookieConfig.path}; Domain=${cookieConfig.domain}`;
}

/**
 * Handler to set authentication cookie
 */
const setCookieHandler = async (event, context) => {
  try {
    const body = parseBody(event);
    const { token, userInfo } = body;

    if (!token) {
      return await json(await message('error', 'Token is required'), 400);
    }

    // Verify the Cognito token
    const tokenPayload = await verifyCognitoToken(token);
    
    // Create a secure session token with user info
    const secrets = await enhancedSecretsManager.getJWTSecrets();
    const sessionPayload = {
      sub: tokenPayload.sub,
      email: tokenPayload.email,
      cognito_username: tokenPayload['cognito:username'],
      userInfo: userInfo,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (8 * 60 * 60), // 8 hours
      iss: 'dsm-backend',
      aud: 'dsm-frontend'
    };

    const sessionToken = jwt.sign(sessionPayload, secrets.JWT_SECRET);

    // Create refresh token
    const refreshPayload = {
      sub: tokenPayload.sub,
      type: 'refresh',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60), // 7 days
    };

    const refreshToken = jwt.sign(refreshPayload, secrets.JWT_SECRET);

    // Set secure cookies
    const authCookie = createSecureCookieHeader('dsm_auth_token', sessionToken, 8 * 60 * 60);
    const refreshCookie = createSecureCookieHeader('dsm_refresh_token', refreshToken, 7 * 24 * 60 * 60);

    const response = await json(await message('success', 'Authentication cookie set successfully'), 200);

    // Add cookie headers
    response.multiValueHeaders = {
      'Set-Cookie': [authCookie, refreshCookie]
    };

    return response;

  } catch (error) {
    console.error('Error setting authentication cookie:', error);
    return await json(await message('error', error.message), 500);
  }
};

// Apply CORS middleware
export const handler = getCorsMiddleware('auth')(setCookieHandler);
