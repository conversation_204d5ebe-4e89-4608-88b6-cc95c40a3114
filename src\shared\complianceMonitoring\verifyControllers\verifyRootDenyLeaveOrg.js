export async function verifyRootDenyLeavOrg(org, rootId) {
  const allRootAccounts = await org
    .listAccountsForParent({
      ParentId: rootId,
    })
    .promise();

  console.log("allRootAccounts:\n", JSON.stringify(allRootAccounts));

  const policies = await org
    .listPoliciesForTarget({
      Filter: "SERVICE_CONTROL_POLICY",
      TargetId: rootId,
    })
    .promise()
    .catch((err) => {
      console.log({ err });
      return false;
    });

  console.log({ policies });
  console.log(JSON.stringify(policies));

  if (policies.Policies.length === 0) {
    console.log(
      `There is no DenyLeaveOrganization policy attached to org root account: Id - ${rootId}`
    );
    return false;
  }

  const policy = policies?.Policies?.find(
    (p) => p.Name.toLowerCase() === "denyleaveorganization"
  );

  console.log({ policy });

  if (!policy) {
    console.log(
      `There is no DenyLeaveOrganization policy attached to org root account: Id - ${rootId}`
    );
    return false;
  } else {
    console.log(
      `\tDenyLeaveOrganization policy attached to org root account: Id - ${rootId}`
    );
    return true;
  }
}
