import { InvoiceEntity } from "../../entities/invoice-entity";
import { mockedData } from "../../__mocks__/invoices/mockData";

describe("Testing Get Invoices function", () => {
  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe("Testing validateParams function from Invoice Entity", () => {
    it("shoud return 'Month is invalid'", async () => {
      const entity = new InvoiceEntity();

      const paramsCase1 = { month: 0, year: 2023 };
      expect(() => entity.validateParams(paramsCase1)).toThrow(
        "Month is invalid"
      );

      const paramsCase2 = { month: 13, year: 2023 };
      expect(() => entity.validateParams(paramsCase2)).toThrow(
        "Month is invalid"
      );
    });

    it("shoud return invalid message when month is not a number", async () => {
      const entity = new InvoiceEntity();

      const paramsCase1 = { month: "", year: 2023 };
      expect(() => entity.validateParams(paramsCase1)).toThrow(
        "Month is not a number"
      );

      const paramsCase2 = { month: null, year: 2023 };
      expect(() => entity.validateParams(paramsCase2)).toThrow(
        "Month is not a number"
      );
    });
  });
});

describe("formatInvoicesParameters", () => {
  it("should return params with full_date", () => {
    const entity = new InvoiceEntity();
    const params = entity.formatInvoicesParameters("2023-01");
    expect(params).toEqual({
      TableName: `${process.env.FINOPS_STAGE}-finops`,
      IndexName: "full_date-index",
      ExpressionAttributeNames: {
        "#full_date": "full_date",
      },
      KeyConditionExpression: "#full_date = :full_date",
      ExpressionAttributeValues: {
        ":full_date": "2023-01",
      },
    });
  });
  it("should throw an error when queryParams is not provided", () => {
    const invoiceEntity = new InvoiceEntity();
    expect(() => {
      invoiceEntity.formatInvoicesParameters();
    }).toThrow("queryParams is required");
  });
});

describe("formatInvoices", () => {
  test("should format invoices correctly", () => {
    const query = mockedData;

    const body = { year: 2024, month: 3 };

    const entity = new InvoiceEntity();

    const result = entity.formatInvoices(query, body);

    expect(result).toHaveLength(1);
    expect(result[0].accounts).toHaveLength(4);
    expect(result[0].accounts[0].invoice_id).toBe("**********");
    expect(result[0].accounts[0].payer_account_id).toBe("************");
    expect(result[0].accounts[0].sub_account).toBe("************");
  });
});
