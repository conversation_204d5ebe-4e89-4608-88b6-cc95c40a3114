export class ProposalEntity {
  clientDynamo = null;
  constructor(clientDynamo) {
    this.clientDynamo = clientDynamo;
  }

  generateInputGetProposal({ status, id, active }) {
    let params = {
      TableName: `${process.env.FINOPS_STAGE}-proposals`,
    };

    if (id) {
      params["KeyConditionExpression"] = "id = :id";
      params["ExpressionAttributeValues"] = {
        ":id": id,
      };
    } else if (status) {
      params["IndexName"] = "status-index";
      params["KeyConditionExpression"] = "#status = :status";
      params["ExpressionAttributeValues"] = {
        ":status": status,
      };
      params["ExpressionAttributeNames"] = {
        "#status": "status",
      };
    }

    return params;
  }

  async getPropostals(input) {
    const response = await this.clientDynamo.query(input).promise();
    return response.Items;
  }
}
