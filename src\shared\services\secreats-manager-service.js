import { SecretsManager } from 'aws-sdk'
import { enhancedSecretsManager } from './enhanced-secrets-manager'

export const makeSecretsManager = (params = null) =>
    new SecretsManager(params || { region: process.env.AWS_REGION_LOCATION })

// Legacy function - kept for backward compatibility
const secreatsManager = makeSecretsManager()
export async function getWafToken() {
    try {
        // Use enhanced secrets manager for better error handling and caching
        return await enhancedSecretsManager.getWafToken();
    } catch (error) {
        // Fallback to legacy method
        console.warn('Enhanced secrets manager failed, falling back to legacy method:', error.message);
        const token = await secreatsManager.getSecretValue({
          SecretId: process.env.DSM_API_WAF_ARN,
        })
        .promise();

        return JSON.parse(token.SecretString).token
    }
}