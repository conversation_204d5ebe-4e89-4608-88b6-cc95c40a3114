#!/usr/bin/env node

/**
 * Verificação final completa antes do deploy
 * Identifica TODOS os problemas que precisam ser corrigidos
 */

const fs = require('fs');
const path = require('path');
const YAML = require('yamljs');

console.log('🔍 VERIFICAÇÃO FINAL PRE-DEPLOY');
console.log('===============================\n');

/**
 * Verifica se uma função JavaScript tem problemas
 */
function analyzeFunction(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    const issues = [];
    
    // Verifica se tem handler
    const hasHandler = content.includes('export const handler') || content.includes('exports.handler');
    
    if (!hasHandler) return { issues: [], hasHandler: false };
    
    // Verifica middleware CORS
    const hasWithCorsImport = content.includes('withAuthCors') || 
                             content.includes('withPublicCors') || 
                             content.includes('withCors');
    
    const hasWithCorsUsage = content.includes('withAuthCors(') || 
                            content.includes('withPublicCors(') || 
                            content.includes('withCors(');
    
    if (!hasWithCorsImport || !hasWithCorsUsage) {
      issues.push('❌ Sem middleware CORS');
    }
    
    // Verifica imports problemáticos
    if (content.includes('jwt-utils-secrets')) {
      issues.push('⚠️ Usa jwt-utils-secrets (pode falhar em produção)');
    }
    
    if (content.includes('secreats-manager-service')) {
      issues.push('⚠️ Usa secrets manager (pode falhar)');
    }
    
    // Verifica se usa AWS SDK sem layers
    if (content.includes('aws-sdk') || content.includes('AWS.')) {
      issues.push('ℹ️ Usa AWS SDK (precisa de AwsCoreLambdaLayer)');
    }
    
    return { issues, hasHandler: true };
    
  } catch (error) {
    return { issues: [`❌ Erro ao ler arquivo: ${error.message}`], hasHandler: false };
  }
}

/**
 * Verifica configurações YAML
 */
function analyzeYamlHandler(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const config = YAML.parse(content);
    
    const issues = [];
    
    for (const [functionName, functionConfig] of Object.entries(config)) {
      if (!functionConfig.events) continue;
      
      for (const event of functionConfig.events) {
        if (!event.http) continue;
        
        const corsConfig = event.http.cors;
        
        if (corsConfig === true) {
          issues.push(`❌ ${functionName}: cors: true (deveria ser false)`);
        } else if (corsConfig && typeof corsConfig === 'object' && corsConfig.origin) {
          issues.push(`❌ ${functionName}: CORS complexo (deveria ser false)`);
        }
      }
    }
    
    return issues;
    
  } catch (error) {
    return [`❌ Erro ao processar YAML: ${error.message}`];
  }
}

/**
 * Verifica funções críticas específicas
 */
function checkCriticalFunctions() {
  const criticalFunctions = [
    'src/functions/auth/set-token.js',
    'src/functions/auth/refresh.js',
    'src/functions/auth/logout.js',
    'src/functions/auth/verify.js',
    'src/functions/cognito/read.js',
    'src/functions/dynamodb/read.js',
    'src/functions/dynamodb/create.js',
    'src/functions/mfa/all.js'
  ];
  
  const issues = [];
  
  for (const funcPath of criticalFunctions) {
    if (fs.existsSync(funcPath)) {
      const analysis = analyzeFunction(funcPath);
      if (analysis.issues.length > 0) {
        issues.push(`📄 ${funcPath}:`);
        analysis.issues.forEach(issue => issues.push(`   ${issue}`));
      }
    } else {
      issues.push(`❌ Função crítica não encontrada: ${funcPath}`);
    }
  }
  
  return issues;
}

/**
 * Verifica todas as funções JavaScript
 */
function scanAllFunctions() {
  const functionsDir = './src/functions';
  const issues = [];
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const itemPath = path.join(dir, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        scanDirectory(itemPath);
      } else if (item.endsWith('.js')) {
        const analysis = analyzeFunction(itemPath);
        if (analysis.hasHandler && analysis.issues.length > 0) {
          issues.push(`📄 ${itemPath}:`);
          analysis.issues.forEach(issue => issues.push(`   ${issue}`));
        }
      }
    }
  }
  
  scanDirectory(functionsDir);
  return issues;
}

/**
 * Verifica todos os handlers YAML
 */
function scanAllYamlHandlers() {
  const handlersDir = './handlers';
  const issues = [];
  
  const handlerFiles = fs.readdirSync(handlersDir).filter(file => file.endsWith('.yml'));
  
  for (const handlerFile of handlerFiles) {
    const handlerPath = path.join(handlersDir, handlerFile);
    const handlerIssues = analyzeYamlHandler(handlerPath);
    
    if (handlerIssues.length > 0) {
      issues.push(`📁 ${handlerFile}:`);
      handlerIssues.forEach(issue => issues.push(`   ${issue}`));
    }
  }
  
  return issues;
}

/**
 * Verifica configurações de ambiente
 */
function checkEnvironmentConfig() {
  const issues = [];
  
  // Verifica .env.dev
  if (!fs.existsSync('.env.dev')) {
    issues.push('❌ Arquivo .env.dev não encontrado');
  } else {
    const envContent = fs.readFileSync('.env.dev', 'utf8');
    
    const requiredVars = [
      'JWT_SECRET',
      'ALLOWED_ORIGINS',
      'COOKIE_DOMAIN',
      'NODE_ENV'
    ];
    
    for (const varName of requiredVars) {
      if (!envContent.includes(varName)) {
        issues.push(`⚠️ Variável ${varName} não encontrada em .env.dev`);
      }
    }
  }
  
  return issues;
}

/**
 * Verifica middleware CORS
 */
function checkCorsMiddleware() {
  const middlewarePath = './src/shared/cors/cors-middleware.js';
  const issues = [];
  
  if (!fs.existsSync(middlewarePath)) {
    issues.push('❌ Middleware CORS não encontrado');
  } else {
    const content = fs.readFileSync(middlewarePath, 'utf8');
    
    if (!content.includes('withAuthCors')) {
      issues.push('❌ withAuthCors não encontrado no middleware');
    }
    
    if (!content.includes('withPublicCors')) {
      issues.push('❌ withPublicCors não encontrado no middleware');
    }
  }
  
  return issues;
}

/**
 * Função principal
 */
async function main() {
  console.log('1️⃣ Verificando funções críticas...');
  const criticalIssues = checkCriticalFunctions();
  
  console.log('2️⃣ Verificando configurações YAML...');
  const yamlIssues = scanAllYamlHandlers();
  
  console.log('3️⃣ Verificando middleware CORS...');
  const corsIssues = checkCorsMiddleware();
  
  console.log('4️⃣ Verificando configurações de ambiente...');
  const envIssues = checkEnvironmentConfig();
  
  console.log('5️⃣ Verificando todas as funções JavaScript...');
  const allFunctionIssues = scanAllFunctions();
  
  // Relatório final
  console.log('\n📊 RELATÓRIO FINAL PRE-DEPLOY');
  console.log('=============================\n');
  
  console.log('🔥 PROBLEMAS CRÍTICOS (IMPEDEM FUNCIONAMENTO):');
  if (criticalIssues.length === 0) {
    console.log('✅ Nenhum problema crítico encontrado');
  } else {
    criticalIssues.forEach(issue => console.log(issue));
  }
  
  console.log('\n📋 PROBLEMAS DE CONFIGURAÇÃO YAML:');
  if (yamlIssues.length === 0) {
    console.log('✅ Todas as configurações YAML estão corretas');
  } else {
    yamlIssues.forEach(issue => console.log(issue));
  }
  
  console.log('\n🌐 PROBLEMAS DE MIDDLEWARE CORS:');
  if (corsIssues.length === 0) {
    console.log('✅ Middleware CORS está correto');
  } else {
    corsIssues.forEach(issue => console.log(issue));
  }
  
  console.log('\n⚙️ PROBLEMAS DE AMBIENTE:');
  if (envIssues.length === 0) {
    console.log('✅ Configurações de ambiente estão corretas');
  } else {
    envIssues.forEach(issue => console.log(issue));
  }
  
  console.log('\n📄 PROBLEMAS EM OUTRAS FUNÇÕES:');
  const nonCriticalFunctionIssues = allFunctionIssues.filter(issue => 
    !criticalIssues.some(critical => issue.includes(critical.split(':')[0]))
  );
  
  if (nonCriticalFunctionIssues.length === 0) {
    console.log('✅ Todas as outras funções estão corretas');
  } else {
    console.log('ℹ️ Problemas não-críticos encontrados (podem ser corrigidos depois):');
    nonCriticalFunctionIssues.slice(0, 10).forEach(issue => console.log(issue));
    if (nonCriticalFunctionIssues.length > 10) {
      console.log(`   ... e mais ${nonCriticalFunctionIssues.length - 10} problemas`);
    }
  }
  
  // Decisão final
  const totalCriticalIssues = criticalIssues.length + yamlIssues.length + corsIssues.length + envIssues.length;
  
  console.log('\n🎯 DECISÃO DE DEPLOY:');
  console.log('====================');
  
  if (totalCriticalIssues === 0) {
    console.log('🚀 ✅ PRONTO PARA DEPLOY!');
    console.log('✅ Todos os problemas críticos foram resolvidos');
    console.log('✅ Funções críticas estão funcionais');
    console.log('✅ CORS está configurado corretamente');
    console.log('🎉 Deploy pode ser feito com segurança');
  } else {
    console.log('⚠️ ❌ NÃO PRONTO PARA DEPLOY');
    console.log(`❌ ${totalCriticalIssues} problemas críticos encontrados`);
    console.log('🔧 Corrija os problemas críticos antes do deploy');
  }
  
  console.log(`\n📈 ESTATÍSTICAS:`);
  console.log(`- Problemas críticos: ${totalCriticalIssues}`);
  console.log(`- Problemas não-críticos: ${nonCriticalFunctionIssues.length}`);
  console.log(`- Status: ${totalCriticalIssues === 0 ? '✅ PRONTO' : '❌ REQUER CORREÇÃO'}`);
}

// Executa apenas se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
}
