const AWS = require("aws-sdk");

const region = "us-east-1";
const sts = new AWS.STS({
  region,
  accessKeyId: `${process.env.FINOPS_SWITCH_ACCOUNT_ACCESS_KEY}`,
  secretAccessKey: `${process.env.FINOPS_SWITCH_ACCOUNT_SECRET_KEY}`,
});

export async function assumeRole(account) {
  console.log("Assuming role for account:", account);
  if (!account || account === "") {
    console.log("Couldn't assume role ! Invalid account.");
    return false;
  }
  let newSession = "";
  const res = await sts
    .assumeRole({
      RoleArn: "arn:aws:iam::" + account + ":role/darede-full",
      RoleSessionName: "darede-compliance-monitoring",
    })
    .promise()
    .catch((err) => {
      console.log(
        `Couldn't assume role darede-full for this account ! 
        This account probably doesn't have trust relationship for <PERSON><PERSON>'s root account`
      );
      console.log(err);
      return false;
    });
  if (res) {
    newSession = res;
  }
  return newSession;
}

export async function assumeRoleRoot(account) {
  console.log("Assuming role for root account:", account);
  if (!account || account === "") {
    console.log("Couldn't assume role ! Invalid account.");
    return false;
  }
  let newSession = "";
  const res = await sts
    .assumeRole({
      RoleArn: "arn:aws:iam::" + account + ":role/dsm-compliance-monitoring",
      RoleSessionName: "darede-compliance-monitoring",
    })
    .promise();
  console.log("Assumed role dsm-compliance-monitoring on root account");
  if (res) {
    newSession = res;
  }
  return newSession;
}
