# Script para corrigir todas as referências ${env:...} para ${self:custom.dotenv...}
# nos arquivos YAML de handlers

$handlersPath = "handlers"
$ymlFiles = Get-ChildItem -Path $handlersPath -Filter "*.yml"

# Mapeamento das variáveis que precisam ser corrigidas
$replacements = @{
    '${env:STAGE}' = '${self:custom.dotenv.STAGE}'
    '${env:API_NAME}' = '${self:custom.dotenv.API_NAME}'
    '${env:VERSION}' = '${self:custom.dotenv.VERSION}'
    '${env:AWS_REGION_LOCATION}' = '${self:custom.dotenv.AWS_REGION_LOCATION}'
    '${env:ACCOUNT_ID}' = '${self:custom.dotenv.ACCOUNT_ID}'
    '${env:AWS_API_GATEWAY_COGNITO_NAME}' = '${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}'
    '${env:USER_POOL_ID}' = '${self:custom.dotenv.USER_POOL_ID}'
}

foreach ($file in $ymlFiles) {
    Write-Host "Processando arquivo: $($file.Name)"
    
    $content = Get-Content -Path $file.FullName -Raw
    $originalContent = $content
    
    # Aplicar todas as substituições
    foreach ($old in $replacements.Keys) {
        $new = $replacements[$old]
        $content = $content -replace [regex]::Escape($old), $new
    }
    
    # Salvar apenas se houve mudanças
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  - Arquivo atualizado"
    } else {
        Write-Host "  - Nenhuma alteracao necessaria"
    }
}

Write-Host "Processamento concluido!"
