import { parseQueryString, parsePath } from '../../shared/parsers'
import pdfMake from 'pdfmake/build/pdfmake'
import vfsFonts from 'pdfmake/build/vfs_fonts'

import {
    responseWithBadRequest,
    responseWithError,
    responseWithNotFound,
    responseWithSuccess,
} from '../../shared/response'

import { makeS3 } from '../../shared/services/s3-service'
import { makeLambda } from '../../shared/services/lambda-service'
import { moneyMask } from '../../shared/masks/money-maks'

const s3 = makeS3()
const lambda = makeLambda()

const REPORTS_BUCKET_NAME = process.env.REPORTS_BUCKET_NAME

export const handler = async (event, context) => {
    try {
        let { year, month } = parseQueryString(event)
        let { payer } = parsePath(event)

        if (event.isInvoke) {
            year = event.year
            month = event.month
            payer = event.payer
        }

        const fileKey = `billing/${year}-${month}/${payer}.pdf`
        let url

        const entities = await getEntities({ payer, month, year })

        const { vfs } = vfsFonts.pdfMake
        pdfMake.vfs = vfs

        pdfMake.tableLayouts = {
            exampleLayout: {
                hLineWidth: function (i, node) {
                    if (i === 0 || i === node.table.body.length) {
                        return 0;
                    }
                    return (i === node.table.headerRows) ? 2 : 1;
                },
                vLineWidth: function (i) {
                    return 0;
                },
                hLineColor: function (i) {
                    return i === 1 ? 'black' : '#aaa';
                },
                paddingLeft: function (i) {
                    return i === 0 ? 0 : 8;
                },
                paddingRight: function (i, node) {
                    return (i === node.table.widths.length - 1) ? 0 : 8;
                },
                fillColor: function (rowIndex, node, columnIndex) {
                    if (rowIndex === 0)
                        return (rowIndex % 2 === 0) ? '#04A467' : null;
                }
            }
        };

        const servicesByCost = await getAllServiceCost({ payerAccount: payer, month, year })

        if (Object.keys(servicesByCost).length == 0)
            return responseWithNotFound("No entities in this payer account")

        const servicesTable = await generateAllCostsServiceTable({
            entities: servicesByCost,
            payer,
            month,
            year
        })


        const accountTables = await generateAccountTables({ entities, payer, month, year })

        const documentDefinition = {
            pageSize: 'A4',

            header: function () {
                return {
                    margin: [20, 10, 20, 10],
                    layout: 'noBorders',
                    fillColor: '#04A467',
                    table: {
                        widths: ['*'],
                        body: [
                            [
                                { text: 'DAREDE', fontSize: 14, style: 'title', color: '#FFF' },
                            ]
                        ],
                    },
                };
            },
            footer: function (currentPage, pageCount) {
                return {
                    table: {
                        body: [
                            [
                                { text: currentPage.toString() + ' / ' + pageCount, alignment: 'right', fontSize: 8, margin: [10, 20, 0, 0] }
                            ],
                        ]
                    },
                    layout: 'noBorders'
                };
            },
            content: [
                { text: `Relatório de Billing`, style: 'titleContent' },
                { text: `${month}-${year}`, style: 'titleContent' },
                '\n',
                ...servicesTable,
                { text: '\n', pageBreak: 'before' },
                ...accountTables
            ],
            styles: {
                header: {
                    fontSize: 10,
                    bold: true
                },
                title: {
                    fontSize: 14,
                    alignment: 'center',
                    bold: true,
                },
                titleContent: {
                    fontSize: 12,
                    bold: true,
                    alignment: 'center',
                    margin: [-20, 0, 0, 8]
                },
                tableRow: {
                    fontSize: 9,
                    bold: true,
                    color: '#FFF',
                    bold: true
                },
                accountTitle: {
                    fontSize: 14,
                    bold: true,
                    margin: [-20, 0, 0, 8]
                },
                serviceTitle: {
                    margin: [-20, 0, 0, 0],
                    alignment: 'center',
                    fontSize: 14,
                    bold: true
                },
                totalServiceCost: {
                    margin: [-20, -10, 0, 0],
                    fontSize: 14,
                    alignment: 'right',
                    bold: true
                },
                services: {
                    margin: [-20, 0, 0, 0],
                    fontSize: 14,
                    color: '#FFC000',
                    bold: true
                },
                serviceCost: {
                    margin: [-20, 0, 0, 0],
                    fontSize: 10,
                    bold: true
                },
                regions: {
                    margin: [-10, 0, 0, 0],
                    fontSize: 12,
                    bold: true,
                },
                summaryTitle: {
                    margin: [-10, 0, 0, 0],
                    alignment: 'left',
                    fontSize: 12,
                    bold: true
                },
                regionsCost: {
                    fontSize: 10,
                    alignment: 'right',
                    bold: true
                },
                description: {
                    margin: [-10, 0, 0, 0],
                    fontSize: 10,
                    color: '#666666',
                },
                descriptionCost: {
                    fontSize: 10,
                    color: '#666666',
                    margin: [0, -10, 0, 0],
                    alignment: 'right',
                },
                descriptionNegativeCost: {
                    fontSize: 10,
                    color: '#ff0000',
                    margin: [0, -10, 0, 0],
                    alignment: 'right',
                },
                entity: {
                    margin: [-20, 0, 0, 0],
                    fontSize: 16,
                    alignment: 'center',
                    bold: true
                },
            },
        }

        const buffer = await new Promise(resolve => {
            pdfMake.createPdf(documentDefinition).getBuffer(result => resolve(result))
        })


        try {
            await s3.deleteObject({
                Bucket: REPORTS_BUCKET_NAME,
                Key: fileKey,
            }).promise()

            await s3.upload({
                Bucket: REPORTS_BUCKET_NAME,
                Key: fileKey,
                ContentType: 'application/pdf',
                Body: Buffer.from(buffer)
            }).promise()

            url = await getFileBucket({ payer, month, year })
            return responseWithSuccess(url, 'Query successfully')
        } catch (error) {
            console.log(error)
        }

        const response =

            url = await getFileBucket({ payer, month, year })
        if (url)
            return responseWithSuccess(url, 'success')

        return responseWithSuccess(response)

    } catch (error) {
        console.log(error)
        return responseWithSuccess(error)
    }
}

async function getFileBucket({ year, month, payer }) {

    try {
        const metada = await s3.headObject({
            Bucket: REPORTS_BUCKET_NAME,
            Key: `billing/${year}-${month}/${payer}.pdf`
        }).promise()

        if (metada) {
            const url = await s3.getSignedUrlPromise('getObject', {
                Bucket: REPORTS_BUCKET_NAME,
                Key: `billing/${year}-${month}/${payer}.pdf`
            })

            return url
        }

        return ''

    } catch (error) {
        return ''
    }
}

export async function getEntities({ payer, month, year, account = null }) {
    try {
        let payload = { isInvoke: true, payer, month, year }

        if (account)
            payload['account']

        const response = await lambda.invoke({
            FunctionName: 'hml-dsm-back-end-get-account-costs-by-payer',
            Payload: JSON.stringify(payload)
        }).promise()

        const data = JSON.parse(response.Payload)

        return JSON.parse(data.body)
    } catch (error) {
        console.log(error)
        return []
    }
}

async function generateAllCostsServiceTable({ entities }) {
    let accountInfo = []

    const {
        cost,
        taxes,
        services,
        savings_plan,
        costsByInvoices,
        costWithoutDiscount,
        costWithMarketPlace,
        costsByInvoicesMarketPlace,
    } = entities

    const Taxes = services?.filter(i => i.entity === "Taxes")
    const SavingsPlan = services?.filter(i => i.entity === "Savings Plans")

    accountInfo.push({ text: "Resumo:", style: 'summaryTitle' })
    accountInfo.push({ text: '\n' })

    if (costsByInvoices.length > 0) {
        costsByInvoices.forEach(invoice => {
            const isNegative = invoice.cost < 0
            const invoiceName = invoice.bill_invoice_id ? `Custos AWS - Invoice${invoice.bill_invoice_id}` : 'Custos AWS'

            const absoluteCost = Math.abs(invoice.cost)
            const costDescritpion = `${isNegative ? '- ' : ''} $${moneyMask(absoluteCost.toFixed(2))}`

            accountInfo.push({ text: invoiceName, style: 'description' })
            accountInfo.push({ text: costDescritpion, style: isNegative ? 'descriptionNegativeCost' : 'descriptionCost' })
            accountInfo.push({ text: '\n' })
        })
    } else {
        accountInfo.push({ text: "Cost AWS Services", style: 'description' })
        accountInfo.push({ text: `$${moneyMask(costWithoutTaxes.toFixed(2))}`, style: 'descriptionCost' })
        accountInfo.push({ text: '\n' })
    }

    if (costsByInvoicesMarketPlace.length > 0) {
        costsByInvoicesMarketPlace.forEach(market => {
            accountInfo.push({ text: 'Market Place', style: 'description' })
            accountInfo.push({ text: `$${moneyMask(market.cost.toFixed(2))}`, style: 'descriptionCost' })
            accountInfo.push({ text: '\n' })
        })
    }

    accountInfo.push({ text: "Taxes", style: 'description' })
    accountInfo.push({ text: `$${moneyMask(taxes.toFixed(2))}`, style: 'descriptionCost' })
    accountInfo.push({ text: '\n' })

    accountInfo.push({ text: "Savings Plans", style: 'description' })
    accountInfo.push({ text: `• $${moneyMask(savings_plan.toFixed(2))}`, style: 'descriptionNegativeCost' })
    accountInfo.push({ text: '\n' })

    accountInfo.push({ text: "TOTAL", style: 'regions' })
    accountInfo.push({ text: `$${moneyMask(costWithMarketPlace.toFixed(2))}`, style: 'descriptionCost' })
    accountInfo.push({ text: '\n' })
    accountInfo.push({ text: '\n' })

    accountInfo.push({ text: "Taxes", style: 'services' })
    accountInfo.push({ text: `TOTAL: $${moneyMask(taxes.toFixed(2))}`, style: 'descriptionCost' })
    accountInfo.push({ text: '\n' })


    Taxes.forEach(service => {
        accountInfo.push({ text: service.name, style: 'description' })
        accountInfo.push({ text: `$ ${moneyMask(service.cost.toFixed(2))}`, style: 'descriptionCost' })

        accountInfo.push({ text: '\n' })

    })


    accountInfo.push({ text: 'Savings Plans', style: 'services' })
    accountInfo.push({ text: `TOTAL: $ ${moneyMask(savings_plan.toFixed(2))}`, style: 'descriptionCost' })
    accountInfo.push({ text: '\n' })

    SavingsPlan.forEach(service => {

        accountInfo.push({ text: service.name, style: 'description' })
        accountInfo.push({ text: `$ ${moneyMask(service.cost.toFixed(2))}`, style: 'descriptionCost' })

        accountInfo.push({ text: '\n' })

    })

    accountInfo.push({ text: `SERVICOS`, style: 'serviceTitle' })
    accountInfo.push({ text: `TOTAL: $ ${moneyMask(cost.toFixed(2))}`, style: 'totalServiceCost' })

    services.filter(i => i.entity !== "Savings Plans" && i.entity !== "Taxes").forEach(service => {
        accountInfo.push({ text: service.name, style: 'services' })
        accountInfo.push({ text: '\n' })

        service.regions.forEach(region => {
            accountInfo.push({ text: region.location, style: 'regions' })
            accountInfo.push({ text: '\n' })

            region.items.forEach(item => {
                accountInfo.push({ text: `• ${item.description}`, style: 'description' })
                accountInfo.push({ text: `$ ${moneyMask(item.cost.toFixed(2))}`, style: 'descriptionCost' })
            })

            accountInfo.push({ text: '\n' })
            accountInfo.push({ text: `TOTAL REGIÃO: $ ${moneyMask(region.cost.toFixed(2))}`, style: 'regionsCost' })
        })

        accountInfo.push({ text: `TOTAL SERVIÇO: $ ${moneyMask(service.cost.toFixed(2))}`, style: 'serviceCost' })
        accountInfo.push({ text: '\n' })


    });

    return accountInfo

}
const getServiceMktPlace = async ({ payer, month, year, entity, isMarketPlace = 'true' }) => {
    try {
        let payload = { isInvoke: true, payer, month, year, isMarketPlace, entity }


        const response = await lambda.invoke({
            FunctionName: 'hml-dsm-back-end-get-account-cost-by-service',
            Payload: JSON.stringify(payload)
        }).promise()


        const data = JSON.parse(response.Payload)
        const formatedResponse = JSON.parse(data.body)
        return formatedResponse.services
    } catch (error) {
        console.log(error)
        return []
    }
}

const currentMktServices = async (payer, month, year, entity) => {
    const response = await getServiceMktPlace(payer, month, year, entity)

    return response
}

async function generateAccountTables({ entities, payer, month, year }) {
    let accountTables = []


    for (let entityPosition = 0; entityPosition < entities.length; entityPosition++) {

        const sortedEntities = entities.sort((x, y) => {
            let a = x.name.toUpperCase();
            let b = y.name.toUpperCase();
            return a == b ? 0 : a > b ? 1 : -1;
        })

        const entity = sortedEntities[entityPosition];
        const isMarketPlace = !entity.name.includes("AWS")
        const accounts = entity.accounts
        const entityName = isMarketPlace ? `Market Place - ${entity.name}` : `${entity.name}`

        accountTables.push({ text: entityName, style: 'entity' })
        accountTables.push({ text: `$ ${moneyMask(entity.cost.toFixed(2))}`, style: 'entity', margin: [0, -18, 0, 0], alignment: 'right' })
        accountTables.push({ text: '\n' })

        for (let accountPosition = 0; accountPosition < accounts.length; accountPosition++) {
            const account = accounts[accountPosition];

            const accountTable = await renderTableByAccount({
                account: account.account,
                payer: payer,
                month: month,
                year: year,
                accountCost: account.cost,
                isMarketPlace: isMarketPlace ? true : false,
                entity: isMarketPlace ? entity.name : null
            })

            accountTables.push(accountTable)
            accountTables.push({ text: '\n', pageBreak: 'before' })
        }

    }

    return accountTables

}

async function renderTableByAccount({ account, accountCost, payer, month, year, isMarketPlace, entity }) {
    const services = isMarketPlace ? await currentMktServices({ payer, month, year, entity }) : await getServiceCostByAccount({
        account,
        payerAccount: payer,
        month,
        year
    })

    let accountInfo = []

    const payerMessage = account === payer ? '(payer)' : ''
    accountInfo.push({ text: `Conta - ${account} ${payerMessage}`, style: 'accountTitle' })
    accountInfo.push({ text: `$ ${moneyMask(accountCost.toFixed(2))}`, style: 'accountTitle', margin: [0, -18, 0, 0], alignment: 'right' })

    services.forEach(service => {
        accountInfo.push({ text: service.name, style: 'services' })
        accountInfo.push({ text: '\n' })

        service.regions.forEach(region => {
            accountInfo.push({ text: region.location, style: 'regions' })
            accountInfo.push({ text: '\n' })

            region.items.forEach(item => {
                accountInfo.push({ text: `• ${item.description}`, style: 'description' })
                accountInfo.push({ text: `$ ${moneyMask(item.cost.toFixed(2))}`, style: 'descriptionCost' })
            })

            accountInfo.push({ text: '\n' })
            accountInfo.push({ text: `TOTAL REGIÃO: $ ${moneyMask(region.cost.toFixed(2))}`, style: 'regionsCost' })
        })

        accountInfo.push({ text: `TOTAL SERVIÇO: $ ${moneyMask(service.cost.toFixed(2))}`, style: 'serviceCost' })
        accountInfo.push({ text: '\n' })


    });

    if (services.length === 0) {
        accountInfo.push({ text: '\n' })
        accountInfo.push({ text: `TOTAL SERVIÇO: $ ${moneyMask('0.00')}`, style: 'serviceCost' })
        accountInfo.push({ text: '\n' })
    }

    return accountInfo

}

export async function getServiceCostByAccount({ account, payerAccount, month, year }) {
    try {

        let payload = { isInvoke: true, payer: payerAccount, month, year, account }

        const response = await lambda.invoke({
            FunctionName: 'hml-dsm-back-end-get-account-cost-by-service',
            Payload: JSON.stringify(payload)
        }).promise()

        const data = JSON.parse(response.Payload)

        const body = JSON.parse(data.body)

        const { services } = body

        if (!services)
            return []

        services.sort((a, b) => a.name.localeCompare(b.name))

        return services

    } catch (error) {
        console.log(error)
        return []
    }
}

export async function getAllServiceCost({ payerAccount, month, year }) {
    try {

        let payload = { isInvoke: true, payer: payerAccount, month, year }

        const response = await lambda.invoke({
            FunctionName: 'hml-dsm-back-end-get-account-cost-by-service',
            Payload: JSON.stringify(payload)
        }).promise()


        const data = JSON.parse(response.Payload)

        const body = JSON.parse(data.body)

        const { services } = body

        if (!services)
            return []

        services.sort((a, b) => a.name.localeCompare(b.name))
        return body

    } catch (error) {
        console.log(error)
        return []
    }
}