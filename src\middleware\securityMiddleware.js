import crypto from 'crypto';
import { json, message } from '../shared/response';

/**
 * Security Middleware Collection
 * Implements various security measures for the API
 */

/**
 * Content Security Policy (CSP) Middleware
 */
export const cspMiddleware = async (event, context, next) => {
  try {
    const response = await next(event, context);
    
    // Generate nonce for inline scripts/styles
    const nonce = crypto.randomBytes(16).toString('base64');
    
    const cspDirectives = [
      "default-src 'self'",
      `script-src 'self' 'nonce-${nonce}' https://www.google.com https://www.gstatic.com https://apis.google.com`,
      `style-src 'self' 'unsafe-inline' https://fonts.googleapis.com`,
      `font-src 'self' https://fonts.gstatic.com`,
      `img-src 'self' data: https: blob:`,
      `connect-src 'self' https://api.dsm.darede.com.br https://hml.dsm.darede.com.br wss: ws:`,
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "object-src 'none'",
      "media-src 'self'",
      "worker-src 'self' blob:",
      "manifest-src 'self'",
      "upgrade-insecure-requests"
    ].join('; ');

    // Add CSP header
    response.headers = {
      ...response.headers,
      'Content-Security-Policy': cspDirectives,
      'X-Content-Security-Policy': cspDirectives, // Legacy support
      'X-WebKit-CSP': cspDirectives, // Legacy support
      'X-CSP-Nonce': nonce,
    };

    return response;
  } catch (error) {
    console.error('CSP Middleware error:', error);
    return await next(event, context);
  }
};

/**
 * Security Headers Middleware
 */
export const securityHeadersMiddleware = async (event, context, next) => {
  try {
    const response = await next(event, context);
    
    const securityHeaders = {
      // Prevent clickjacking
      'X-Frame-Options': 'DENY',
      
      // Prevent MIME type sniffing
      'X-Content-Type-Options': 'nosniff',
      
      // Enable XSS protection
      'X-XSS-Protection': '1; mode=block',
      
      // Referrer policy
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      
      // Permissions policy
      'Permissions-Policy': [
        'camera=()',
        'microphone=()',
        'geolocation=()',
        'payment=()',
        'usb=()',
        'magnetometer=()',
        'accelerometer=()',
        'gyroscope=()'
      ].join(', '),
      
      // HSTS (only for HTTPS)
      ...(process.env.STAGE === 'prod' && {
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload'
      }),
      
      // Cache control for sensitive endpoints
      ...(event.path?.includes('/auth') && {
        'Cache-Control': 'no-store, no-cache, must-revalidate, private',
        'Pragma': 'no-cache',
        'Expires': '0'
      }),
    };

    response.headers = {
      ...response.headers,
      ...securityHeaders,
    };

    return response;
  } catch (error) {
    console.error('Security Headers Middleware error:', error);
    return await next(event, context);
  }
};

/**
 * Input Validation Middleware
 */
export const inputValidationMiddleware = (validationRules = {}) => {
  return async (event, context, next) => {
    try {
      const body = event.body ? JSON.parse(event.body) : {};
      const queryParams = event.queryStringParameters || {};
      const pathParams = event.pathParameters || {};

      // Validate body parameters
      if (validationRules.body) {
        const bodyValidation = validateInput(body, validationRules.body);
        if (!bodyValidation.isValid) {
          return await json(await message('error', 'Invalid request body', bodyValidation.errors), 400);
        }
      }

      // Validate query parameters
      if (validationRules.query) {
        const queryValidation = validateInput(queryParams, validationRules.query);
        if (!queryValidation.isValid) {
          return await json(await message('error', 'Invalid query parameters', queryValidation.errors), 400);
        }
      }

      // Validate path parameters
      if (validationRules.path) {
        const pathValidation = validateInput(pathParams, validationRules.path);
        if (!pathValidation.isValid) {
          return await json(await message('error', 'Invalid path parameters', pathValidation.errors), 400);
        }
      }

      return await next(event, context);
    } catch (error) {
      console.error('Input Validation Middleware error:', error);
      return await json(await message('error', 'Invalid request format'), 400);
    }
  };
};

/**
 * Input validation helper
 */
function validateInput(data, rules) {
  const errors = [];

  for (const [field, rule] of Object.entries(rules)) {
    const value = data[field];

    // Required field check
    if (rule.required && (value === undefined || value === null || value === '')) {
      errors.push(`Field '${field}' is required`);
      continue;
    }

    // Skip validation if field is not required and not present
    if (!rule.required && (value === undefined || value === null)) {
      continue;
    }

    // Type validation
    if (rule.type && typeof value !== rule.type) {
      errors.push(`Field '${field}' must be of type ${rule.type}`);
      continue;
    }

    // String validations
    if (rule.type === 'string') {
      if (rule.minLength && value.length < rule.minLength) {
        errors.push(`Field '${field}' must be at least ${rule.minLength} characters long`);
      }
      if (rule.maxLength && value.length > rule.maxLength) {
        errors.push(`Field '${field}' must be at most ${rule.maxLength} characters long`);
      }
      if (rule.pattern && !rule.pattern.test(value)) {
        errors.push(`Field '${field}' format is invalid`);
      }
    }

    // Number validations
    if (rule.type === 'number') {
      if (rule.min !== undefined && value < rule.min) {
        errors.push(`Field '${field}' must be at least ${rule.min}`);
      }
      if (rule.max !== undefined && value > rule.max) {
        errors.push(`Field '${field}' must be at most ${rule.max}`);
      }
    }

    // Array validations
    if (rule.type === 'array') {
      if (rule.minItems && value.length < rule.minItems) {
        errors.push(`Field '${field}' must have at least ${rule.minItems} items`);
      }
      if (rule.maxItems && value.length > rule.maxItems) {
        errors.push(`Field '${field}' must have at most ${rule.maxItems} items`);
      }
    }

    // Custom validation
    if (rule.validate && typeof rule.validate === 'function') {
      const customValidation = rule.validate(value);
      if (customValidation !== true) {
        errors.push(customValidation || `Field '${field}' is invalid`);
      }
    }

    // Sanitization
    if (rule.sanitize && typeof rule.sanitize === 'function') {
      data[field] = rule.sanitize(value);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedData: data,
  };
}

/**
 * XSS Protection Middleware
 */
export const xssProtectionMiddleware = async (event, context, next) => {
  try {
    // Sanitize input data
    if (event.body) {
      const body = JSON.parse(event.body);
      const sanitizedBody = sanitizeObject(body);
      event.body = JSON.stringify(sanitizedBody);
    }

    if (event.queryStringParameters) {
      event.queryStringParameters = sanitizeObject(event.queryStringParameters);
    }

    return await next(event, context);
  } catch (error) {
    console.error('XSS Protection Middleware error:', error);
    return await next(event, context);
  }
};

/**
 * Sanitize object recursively
 */
function sanitizeObject(obj) {
  if (typeof obj === 'string') {
    return sanitizeString(obj);
  }
  
  if (Array.isArray(obj)) {
    return obj.map(sanitizeObject);
  }
  
  if (obj && typeof obj === 'object') {
    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
      sanitized[sanitizeString(key)] = sanitizeObject(value);
    }
    return sanitized;
  }
  
  return obj;
}

/**
 * Sanitize string to prevent XSS
 */
function sanitizeString(str) {
  if (typeof str !== 'string') return str;
  
  return str
    .replace(/[<>]/g, '') // Remove < and >
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .replace(/script/gi, 'scr1pt') // Neutralize script tags
    .trim();
}

/**
 * CSRF Protection Middleware
 */
export const csrfProtectionMiddleware = async (event, context, next) => {
  try {
    // Skip CSRF check for GET requests
    if (event.httpMethod === 'GET') {
      return await next(event, context);
    }

    const csrfToken = event.headers['X-CSRF-Token'] || event.headers['x-csrf-token'];
    const sessionToken = extractTokenFromCookies(event);

    if (!csrfToken) {
      return await json(await message('error', 'CSRF token required'), 403);
    }

    // Validate CSRF token (implement your validation logic)
    const isValidCSRF = await validateCSRFToken(csrfToken, sessionToken);
    
    if (!isValidCSRF) {
      return await json(await message('error', 'Invalid CSRF token'), 403);
    }

    return await next(event, context);
  } catch (error) {
    console.error('CSRF Protection Middleware error:', error);
    return await json(await message('error', 'CSRF validation failed'), 403);
  }
};

/**
 * Extract token from cookies helper
 */
function extractTokenFromCookies(event) {
  const cookies = event.headers?.Cookie || event.headers?.cookie || '';
  const cookieArray = cookies.split(';');
  
  for (const cookie of cookieArray) {
    const [name, value] = cookie.trim().split('=');
    if (name === 'dsm_auth_token') {
      return value;
    }
  }
  
  return null;
}

/**
 * Validate CSRF token
 */
async function validateCSRFToken(csrfToken, sessionToken) {
  // Implement your CSRF validation logic here
  // This is a simple example - in production, use a more robust method
  return csrfToken && sessionToken && csrfToken.length > 10;
}

export default {
  cspMiddleware,
  securityHeadersMiddleware,
  inputValidationMiddleware,
  xssProtectionMiddleware,
  csrfProtectionMiddleware,
};
