import { sendEmail } from "../../shared/emails";
import { makeS3 } from "../../shared/services/s3-service";
import { makeSQS } from "../../shared/services/sqs-service";

import {
  STATUS_CODE,
  responseWithError,
  responseWithSuccess,
  responseWithBadRequest,
} from "../../shared/response";

import { errorValidator } from "../../shared/validators";

const AWS_REGION_LOCATION = process.env.AWS_REGION_LOCATION;
const ACCOUNT_ID = process.env.ACCOUNT_ID;
const BUCKET_NAME = process.env.EMAIL_AUTOMATION_BUCKET_NAME;
const INFO_QUEUE_NAME = process.env.EMAIL_AUTOMATION_INFO_QUEUE_NAME;

const FILES_PREFIX = {
  IMAGES: "images",
  TEMPLATE: "template",
};

const sqs = makeSQS();
const s3Bucket = makeS3();

exports.handler = async (event) => {
  try {
    if (event.Records.length > 0) {
      const { s3 } = event.Records[0];

      const templateName = s3.object.key;
      const template = await getTemplate(templateName);
      const groupId = templateName.replace(/(template\/)|(.html)/g, "");
      const attachments = [];

      let templateHTML = template.Body.toString();

      const files = await listImages();
      const filesGroup = files.Contents.filter((file) =>
        file.Key.includes(groupId)
      );

      for (let index = 0; index < filesGroup.length; index++) {
        const file = filesGroup[index];
        const fileName = file.Key.replace(/images\/|.png|.jpeg|.webp/g, "");
        const signedURL = await generateSignedUrl(file.Key);

        templateHTML = templateHTML.replace(fileName, fileName);

        attachments.push({
          filename: file.Key,
          path: signedURL,
          cid: file.Key, //same cid value as in the html img src
        });
      }

      const emailInformation = await retrieveEmailInfomation(groupId);

      if (emailInformation) {
        console.log("Enviando email");
        const { subject, emails, from } = emailInformation;
        await sendEmail(templateHTML, subject, emails, from, attachments);
      }

      return responseWithSuccess(null, "Email enviado com sucesso");
    }

    return responseWithBadRequest(500, "error", {
      message: "Não foi possivel enviar email",
    });
  } catch (error) {
    const { statusCode, message } = errorValidator(error);

    if (STATUS_CODE.BAD_REQUEST === statusCode)
      return responseWithBadRequest(message);

    return responseWithError(message);
  }
};

async function getTemplate(templateName) {
  return await s3Bucket
    .getObject({
      Bucket: BUCKET_NAME,
      Key: templateName,
    })
    .promise();
}

async function listImages() {
  return await s3Bucket
    .listObjectsV2({
      Bucket: BUCKET_NAME,
      Prefix: FILES_PREFIX.IMAGES,
    })
    .promise();
}

async function generateSignedUrl(fileName) {
  return await s3Bucket.getSignedUrlPromise("getObject", {
    Bucket: BUCKET_NAME,
    Key: fileName,
  });
}

async function retrieveEmailInfomation(groupId) {
  let queueInfoURL = await verifyStatusQueue(INFO_QUEUE_NAME);

  let emailInformation = null;
  const responseSQS = await sqs
    .receiveMessage({
      QueueUrl: queueInfoURL,
      AttributeNames: ["MessageGroupId"],
      MaxNumberOfMessages: 10,
    })
    .promise();

  if (responseSQS.Messages) {
    responseSQS.Messages.forEach((message) => {
      const receiptHandle = message.ReceiptHandle;
      const body = JSON.parse(message.Body);
      if (body.groupdId === groupId) {
        emailInformation = body;

        sqs
          .deleteMessage({
            QueueUrl: queueInfoURL,
            ReceiptHandle: receiptHandle,
          })
          .promise();
      }
    });
  }

  return emailInformation;
}

async function verifyStatusQueue(queueName) {
  const queueList = await listQueues();
  const queueURL = generateQueueURL(queueName);

  if (queueList.QueueUrls)
    return queueList.QueueUrls.find((url) => url === queueURL);

  return null;
}

async function listQueues() {
  return await sqs.listQueues().promise();
}

function generateQueueURL(queue) {
  return `https://sqs.${AWS_REGION_LOCATION}.amazonaws.com/${ACCOUNT_ID}/${queue}`;
}
