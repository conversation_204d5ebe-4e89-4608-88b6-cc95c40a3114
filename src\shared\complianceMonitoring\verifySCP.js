import { assumeRole, assumeRoleRoot } from "./assumeRole";

const AWS = require("aws-sdk");

const region = "us-east-1";

export async function verifySCP(account) {
  await assumeRoleRoot(`${process.env.ROOT_ACCOUNT_ID}`);

  console.log("\nVerifying SCP in organization...");

  const newSession = await accessKeysCustomerAccount(account);

  if (newSession) {
    const organizations = new AWS.Organizations({
      region,
      credentials: {
        accessKeyId: newSession.access_key_id,
        secretAccessKey: newSession.secret_key_id,
        sessionToken: newSession.session_token,
      },
      MaxResults: 3,
    });

    let parentId = await organizations
      .listParents({ ChildId: account })
      .promise()
      .catch(() => {
        return false;
      });
    if (!parentId) return false;
    parentId = parentId.Parents[0].Id;

    let scps = await organizations
      .listPoliciesForTarget({
        TargetId: parentId,
        Filter: "SERVICE_CONTROL_POLICY",
      })
      .promise()
      .catch(() => {
        return false;
      });
    if (!scps) return false;

    const res = scps.Policies.find((p) => p.Name === "FullAWSAccess");
    if (res !== undefined) {
      return true;
    } else {
      return false;
    }
  } else {
    return false;
  }
}
