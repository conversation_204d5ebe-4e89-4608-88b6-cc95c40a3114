/**
 * Script para validar se todas as correções estão implementadas corretamente
 * antes do deploy
 */

const fs = require('fs');
const path = require('path');

function validateHandlerFiles() {
  console.log('🔍 Validando arquivos de handlers...\n');
  
  const handlerFiles = [
    'handlers/dynamodb.yml',
    'handlers/customers.yml', 
    'handlers/switchRole.yml',
    'handlers/cognito.yml',
    'handlers/webhook.yml'
  ];
  
  let issues = [];
  
  handlerFiles.forEach(file => {
    if (!fs.existsSync(file)) {
      issues.push(`❌ Arquivo não encontrado: ${file}`);
      return;
    }
    
    const content = fs.readFileSync(file, 'utf8');
    
    // Verificar se ainda tem CORS wildcard
    if (content.includes('origin: "*"')) {
      issues.push(`❌ ${file}: Ainda contém CORS wildcard 'origin: "*"'`);
    }
    
    // Verificar se ainda tem authorizer ativo (não comentado)
    const activeAuthorizerRegex = /^\s*authorizer:\s*\${self:custom\.authorizer}/gm;
    const matches = content.match(activeAuthorizerRegex);
    if (matches) {
      issues.push(`❌ ${file}: Ainda contém ${matches.length} authorizer(s) ativo(s)`);
    }
    
    // Verificar se tem cors: false
    if (!content.includes('cors: false')) {
      issues.push(`⚠️  ${file}: Pode não ter 'cors: false' em todos os endpoints`);
    }
    
    console.log(`✅ ${file}: Validado`);
  });
  
  return issues;
}

function validateFunctionFiles() {
  console.log('\n🔍 Validando arquivos de funções...\n');
  
  const functionFiles = [
    'src/functions/dynamodb/read.js',
    'src/functions/dynamodb/readAudits.js',
    'src/functions/dynamodb/update.js',
    'src/functions/dynamodb/delete.js',
    'src/functions/switchRole/readSwitchRoleData.js',
    'src/functions/switchRole/invokeStateMachine.js'
  ];
  
  let issues = [];
  
  functionFiles.forEach(file => {
    if (!fs.existsSync(file)) {
      issues.push(`❌ Arquivo não encontrado: ${file}`);
      return;
    }
    
    const content = fs.readFileSync(file, 'utf8');
    
    // Verificar se tem imports necessários
    if (!content.includes('withAuthCors')) {
      issues.push(`❌ ${file}: Falta import 'withAuthCors'`);
    }
    
    if (!content.includes('withSecureAuth')) {
      issues.push(`❌ ${file}: Falta import 'withSecureAuth'`);
    }
    
    // Verificar se tem middleware aplicado
    if (!content.includes('withAuthCors(withSecureAuth(')) {
      issues.push(`❌ ${file}: Middleware não aplicado corretamente`);
    }
    
    console.log(`✅ ${file}: Validado`);
  });
  
  return issues;
}

function validateEnvironmentConfig() {
  console.log('\n🔍 Validando configuração de ambiente...\n');
  
  let issues = [];
  
  if (!fs.existsSync('.env.dev')) {
    issues.push('❌ Arquivo .env.dev não encontrado');
    return issues;
  }
  
  const envContent = fs.readFileSync('.env.dev', 'utf8');
  
  // Verificar ALLOWED_ORIGINS
  if (!envContent.includes('ALLOWED_ORIGINS=')) {
    issues.push('❌ ALLOWED_ORIGINS não definido em .env.dev');
  } else {
    const allowedOriginsLine = envContent.split('\n').find(line => line.startsWith('ALLOWED_ORIGINS='));
    if (allowedOriginsLine) {
      const origins = allowedOriginsLine.split('=')[1];
      
      if (!origins.includes('dev.dsm.darede.com.br')) {
        issues.push('❌ dev.dsm.darede.com.br não está em ALLOWED_ORIGINS');
      }
      
      if (!origins.includes('dsm.darede.com.br')) {
        issues.push('❌ dsm.darede.com.br não está em ALLOWED_ORIGINS');
      }
      
      console.log(`✅ ALLOWED_ORIGINS: ${origins}`);
    }
  }
  
  // Verificar JWT_DECRIPTION_CREDENTIALS
  if (!envContent.includes('JWT_DECRIPTION_CREDENTIALS=')) {
    issues.push('❌ JWT_DECRIPTION_CREDENTIALS não definido');
  } else {
    console.log('✅ JWT_DECRIPTION_CREDENTIALS: Definido');
  }
  
  return issues;
}

function validateCorsMiddleware() {
  console.log('\n🔍 Validando middleware CORS...\n');
  
  let issues = [];
  
  const corsFile = 'src/shared/cors/cors-middleware.js';
  
  if (!fs.existsSync(corsFile)) {
    issues.push(`❌ Arquivo não encontrado: ${corsFile}`);
    return issues;
  }
  
  const content = fs.readFileSync(corsFile, 'utf8');
  
  // Verificar se tem função isOriginAllowed melhorada
  if (!content.includes('isOriginAllowed')) {
    issues.push('❌ Função isOriginAllowed não encontrada');
  }
  
  // Verificar se suporta wildcards
  if (!content.includes('*.')) {
    issues.push('❌ Suporte a wildcards pode estar ausente');
  }
  
  // Verificar se tem logs de debug
  if (!content.includes('console.log')) {
    issues.push('⚠️  Logs de debug podem estar ausentes');
  }
  
  console.log('✅ Middleware CORS: Validado');
  
  return issues;
}

function main() {
  console.log('🚀 Validando todas as correções implementadas...\n');
  console.log('='.repeat(60));
  
  let allIssues = [];
  
  // Validar handlers
  allIssues = allIssues.concat(validateHandlerFiles());
  
  // Validar funções
  allIssues = allIssues.concat(validateFunctionFiles());
  
  // Validar configuração de ambiente
  allIssues = allIssues.concat(validateEnvironmentConfig());
  
  // Validar middleware CORS
  allIssues = allIssues.concat(validateCorsMiddleware());
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 RESULTADO DA VALIDAÇÃO:');
  console.log('='.repeat(60));
  
  if (allIssues.length === 0) {
    console.log('🎉 TODAS AS CORREÇÕES ESTÃO IMPLEMENTADAS CORRETAMENTE!');
    console.log('✅ Pronto para deploy');
    console.log('\n💡 Para fazer o deploy:');
    console.log('   serverless deploy --stage dev');
    console.log('\n💡 Para monitorar o deploy:');
    console.log('   node scripts/monitor-deploy.js');
  } else {
    console.log(`❌ ENCONTRADOS ${allIssues.length} PROBLEMA(S):`);
    console.log('');
    allIssues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue}`);
    });
    console.log('\n⚠️  Corrija os problemas antes do deploy');
  }
  
  console.log('\n📈 Status das correções:');
  console.log(`   Handlers validados: ${allIssues.filter(i => i.includes('handlers/')).length === 0 ? '✅' : '❌'}`);
  console.log(`   Funções validadas: ${allIssues.filter(i => i.includes('src/functions/')).length === 0 ? '✅' : '❌'}`);
  console.log(`   Configuração validada: ${allIssues.filter(i => i.includes('.env')).length === 0 ? '✅' : '❌'}`);
  console.log(`   Middleware validado: ${allIssues.filter(i => i.includes('middleware')).length === 0 ? '✅' : '❌'}`);
}

main();
