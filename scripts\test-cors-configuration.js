/**
 * Script para testar configuração CORS com cookies HttpOnly
 * Testa todos os cenários CORS necessários para funcionamento correto
 */

const http = require('http');

const BASE_URL = 'http://localhost:8000';
const API_PATH = '/dev';

console.log('🌐 Testando Configuração CORS para Cookies HttpOnly\n');

// Origens para testar
const testOrigins = [
  'https://dsm.darede.com.br',
  'http://localhost:3000',
  'http://localhost:8080',
  'https://malicious-site.com' // Deve ser rejeitada
];

// Função helper para fazer requisições HTTP com headers específicos
function makeRequestWithOrigin(method, path, origin, data = null, extraHeaders = {}) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 8000,
      path: `${API_PATH}${path}`,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Origin': origin,
        ...extraHeaders
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        let parsedData = null;
        try {
          parsedData = responseData ? JSON.parse(responseData) : null;
        } catch (e) {
          parsedData = responseData;
        }
        
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: parsedData
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Testa requisição preflight OPTIONS
async function testPreflightRequest(origin) {
  console.log(`   🔍 Testando preflight para origem: ${origin}`);
  
  const response = await makeRequestWithOrigin('OPTIONS', '/auth/config', origin, null, {
    'Access-Control-Request-Method': 'POST',
    'Access-Control-Request-Headers': 'Content-Type, Authorization, Cookie'
  });
  
  const corsHeaders = {
    allowOrigin: response.headers['access-control-allow-origin'],
    allowCredentials: response.headers['access-control-allow-credentials'],
    allowMethods: response.headers['access-control-allow-methods'],
    allowHeaders: response.headers['access-control-allow-headers'],
    maxAge: response.headers['access-control-max-age']
  };
  
  console.log(`      Status: ${response.statusCode}`);
  console.log(`      Allow-Origin: ${corsHeaders.allowOrigin || 'não definido'}`);
  console.log(`      Allow-Credentials: ${corsHeaders.allowCredentials || 'não definido'}`);
  console.log(`      Allow-Methods: ${corsHeaders.allowMethods || 'não definido'}`);
  console.log(`      Max-Age: ${corsHeaders.maxAge || 'não definido'}`);
  
  return corsHeaders;
}

// Testa requisição real com cookies
async function testActualRequest(origin) {
  console.log(`   📡 Testando requisição real para origem: ${origin}`);
  
  const response = await makeRequestWithOrigin('GET', '/auth/config', origin, null, {
    'Cookie': 'dsm_access_token=test-token; dsm_refresh_token=test-refresh'
  });
  
  const corsHeaders = {
    allowOrigin: response.headers['access-control-allow-origin'],
    allowCredentials: response.headers['access-control-allow-credentials'],
    setCookie: response.headers['set-cookie']
  };
  
  console.log(`      Status: ${response.statusCode}`);
  console.log(`      Allow-Origin: ${corsHeaders.allowOrigin || 'não definido'}`);
  console.log(`      Allow-Credentials: ${corsHeaders.allowCredentials || 'não definido'}`);
  console.log(`      Set-Cookie: ${corsHeaders.setCookie ? 'presente' : 'ausente'}`);
  
  return corsHeaders;
}

async function testCorsConfiguration() {
  try {
    console.log('1️⃣ Testando configurações CORS para diferentes origens...\n');
    
    for (const origin of testOrigins) {
      console.log(`🌍 Testando origem: ${origin}`);
      
      // Testa preflight
      const preflightResult = await testPreflightRequest(origin);
      
      // Testa requisição real
      const actualResult = await testActualRequest(origin);
      
      // Análise dos resultados
      const isOriginAllowed = preflightResult.allowOrigin === origin;
      const credentialsEnabled = preflightResult.allowCredentials === 'true';
      const hasRequiredMethods = preflightResult.allowMethods && 
        preflightResult.allowMethods.includes('POST') && 
        preflightResult.allowMethods.includes('GET');
      const hasRequiredHeaders = preflightResult.allowHeaders && 
        preflightResult.allowHeaders.includes('Cookie');
      
      console.log(`   📊 Análise:`);
      console.log(`      ✅ Origem permitida: ${isOriginAllowed ? 'Sim' : 'Não'}`);
      console.log(`      ✅ Credenciais habilitadas: ${credentialsEnabled ? 'Sim' : 'Não'}`);
      console.log(`      ✅ Métodos necessários: ${hasRequiredMethods ? 'Sim' : 'Não'}`);
      console.log(`      ✅ Headers necessários: ${hasRequiredHeaders ? 'Sim' : 'Não'}`);
      
      if (origin.includes('malicious')) {
        if (!isOriginAllowed) {
          console.log(`      🛡️ Origem maliciosa corretamente rejeitada`);
        } else {
          console.log(`      ⚠️ PROBLEMA: Origem maliciosa foi aceita!`);
        }
      } else {
        if (isOriginAllowed && credentialsEnabled) {
          console.log(`      ✅ Configuração CORS correta para cookies HttpOnly`);
        } else {
          console.log(`      ❌ Configuração CORS inadequada para cookies HttpOnly`);
        }
      }
      
      console.log('');
    }
    
    console.log('2️⃣ Testando cenários específicos de cookies...\n');
    
    // Teste com token Cognito
    console.log('🍪 Testando conversão Cognito → Cookies...');
    const cognitoResponse = await makeRequestWithOrigin(
      'POST', 
      '/auth/cognito-to-cookie', 
      'http://localhost:3000',
      { token: 'fake-cognito-token' }
    );
    
    console.log(`   Status: ${cognitoResponse.statusCode}`);
    console.log(`   CORS Headers: ${cognitoResponse.headers['access-control-allow-origin'] ? 'Presentes' : 'Ausentes'}`);
    console.log(`   Set-Cookie: ${cognitoResponse.headers['set-cookie'] ? 'Presente' : 'Ausente'}`);
    
    // Teste de verificação
    console.log('\n🔍 Testando verificação com cookies...');
    const verifyResponse = await makeRequestWithOrigin(
      'GET', 
      '/auth/verify', 
      'http://localhost:3000',
      null,
      { 'Cookie': 'dsm_access_token=test-token' }
    );
    
    console.log(`   Status: ${verifyResponse.statusCode}`);
    console.log(`   CORS Headers: ${verifyResponse.headers['access-control-allow-origin'] ? 'Presentes' : 'Ausentes'}`);
    
    console.log('\n3️⃣ Resumo da Configuração CORS...\n');
    
    // Verifica configuração geral
    const configResponse = await makeRequestWithOrigin('GET', '/auth/config', 'http://localhost:3000');
    
    if (configResponse.statusCode === 200) {
      console.log('✅ Endpoint de configuração acessível');
      console.log('✅ Headers CORS aplicados corretamente');
      
      if (configResponse.headers['access-control-allow-credentials'] === 'true') {
        console.log('✅ Credenciais habilitadas para cookies HttpOnly');
      } else {
        console.log('❌ Credenciais não habilitadas - cookies não funcionarão');
      }
      
      if (configResponse.headers['access-control-allow-origin'] && 
          !configResponse.headers['access-control-allow-origin'].includes('*')) {
        console.log('✅ Origens específicas configuradas (seguro)');
      } else {
        console.log('⚠️ Origem wildcard (*) detectada - pode causar problemas com credenciais');
      }
      
    } else {
      console.log('❌ Problema na configuração básica');
    }
    
    console.log('\n🎯 Recomendações:');
    console.log('1. Certifique-se que Access-Control-Allow-Credentials: true');
    console.log('2. Use origens específicas, não wildcard (*)');
    console.log('3. Inclua Cookie nos headers permitidos');
    console.log('4. Configure Max-Age adequado para cache de preflight');
    console.log('5. Teste em ambiente de produção com HTTPS');
    
  } catch (error) {
    console.error('❌ Erro no teste CORS:', error.message);
  }
}

// Executa o teste
testCorsConfiguration();
