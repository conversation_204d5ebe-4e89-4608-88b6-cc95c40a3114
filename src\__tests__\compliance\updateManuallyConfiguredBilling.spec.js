import { updateManuallyConfiguredBilling } from "../../shared/complianceMonitoring/updateManuallyConfiguredBilling";
import { readOne, put } from "../../model/dynamo";
jest.mock("../../model/dynamo", () => ({
  readOne: jest.fn(),
  put: jest.fn(),
}));

describe("updateManuallyConfiguredBilling", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });
  it("should return true if no payer account found", async () => {
    afterEach(() => {
      jest.clearAllMocks();
    });
    readOne.mockResolvedValueOnce({
      Item: {
        id: "customerId1",
        accounts: [
          { account_id: "111" },
          { account_id: "222" },
          { account_id: "333" },
        ],
      },
    });
    put.mockResolvedValueOnce(true);

    const contract = { id: "contractId1", billingConfigured: false };

    const result = await updateManuallyConfiguredBilling(
      "customerId1",
      contract,
      ["222", "444"]
    );
    expect(result).toBe(true);
  });
  it("should return false if no payer account found", async () => {
    readOne.mockResolvedValueOnce({
      Item: {
        id: "customerId1",
        accounts: [{ account_id: "222" }, { account_id: "333" }],
      },
    });

    const contract = { id: "contractId1", billingConfigured: false };

    const result = await updateManuallyConfiguredBilling(
      "customerId1",
      contract,
      ["111", "444"]
    );
    expect(result).toBe(false);
  });
});
