/**
 * Middleware de autenticação usando AWS Secrets Manager
 * Versão refatorada com gerenciamento seguro de credenciais
 */

const { verifyToken, isTokenNearExpiry, generateToken } = require('./jwt-utils-secrets');
const { getAccessTokenFromCookies, createAuthCookieHeaders } = require('./cookie-utils-cjs');
const { responseWithError, responseWithBadRequest, responseWithUnauthorized } = require('../response-cjs');

/**
 * Extrai token do header Authorization ou cookies (prioridade para cookies)
 * @param {Object} event - Evento <PERSON>da
 * @returns {string|null} Token extraído ou null
 */
const extractToken = (event) => {
  // Primeiro tenta extrair dos cookies (mais seguro)
  const cookieToken = getAccessTokenFromCookies(event);
  if (cookieToken) {
    console.log('Token extraído dos cookies HttpOnly');
    return cookieToken;
  }
  
  // Fallback para Authorization header
  const authHeader = event.headers?.Authorization || event.headers?.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    console.log('Token extraído do header Authorization (fallback)');
    return authHeader.substring(7);
  }
  
  return null;
};

/**
 * Valida permissões do usuário
 * @param {Object} user - Dados do usuário
 * @param {Array} requiredPermissions - Permissões necessárias
 * @param {string} requiredRole - Role necessária
 * @returns {boolean} True se autorizado
 */
const validatePermissions = (user, requiredPermissions = [], requiredRole = null) => {
  // Verifica role se especificada
  if (requiredRole && user.role !== requiredRole) {
    console.log(`Acesso negado: role '${user.role}' não é '${requiredRole}'`);
    return false;
  }
  
  // Verifica permissões se especificadas
  if (requiredPermissions.length > 0) {
    const userPermissions = user.permissions || [];
    const hasAllPermissions = requiredPermissions.every(permission => 
      userPermissions.includes(permission)
    );
    
    if (!hasAllPermissions) {
      console.log(`Acesso negado: permissões insuficientes. Necessárias: ${requiredPermissions}, Usuário tem: ${userPermissions}`);
      return false;
    }
  }
  
  return true;
};

/**
 * Middleware de autenticação principal usando Secrets Manager
 * @param {Object} event - Evento Lambda
 * @param {Object} context - Contexto Lambda
 * @param {Object} options - Opções de configuração
 * @returns {Object} Resultado da autenticação
 */
const authenticateRequest = async (event, context, options = {}) => {
  try {
    const {
      requireAuth = true,
      requiredPermissions = [],
      requiredRole = null,
      allowExpiredTokens = false
    } = options;
    
    // Se autenticação não é requerida, passa direto
    if (!requireAuth) {
      return {
        isAuthenticated: true,
        user: null,
        skipAuth: true
      };
    }
    
    // Extrai token
    const token = extractToken(event);
    
    if (!token) {
      console.log('Token de autenticação não fornecido');
      return {
        isAuthenticated: false,
        error: 'Token de autenticação não fornecido',
        response: responseWithUnauthorized('Token de autenticação requerido')
      };
    }
    
    // Verifica e decodifica o token usando Secrets Manager
    let decoded;
    try {
      decoded = await verifyToken(token);
      console.log(`Token válido para usuário: ${decoded.email} (Secrets Manager)`);
    } catch (error) {
      console.log(`Token inválido: ${error.message}`);
      
      if (!allowExpiredTokens || !error.message.includes('expirado')) {
        return {
          isAuthenticated: false,
          error: error.message,
          response: responseWithUnauthorized(`Token inválido: ${error.message}`)
        };
      }
    }
    
    // Valida permissões
    if (!validatePermissions(decoded, requiredPermissions, requiredRole)) {
      return {
        isAuthenticated: false,
        error: 'Permissões insuficientes',
        response: responseWithUnauthorized('Acesso negado: permissões insuficientes')
      };
    }
    
    // Verifica se o token está próximo do vencimento
    const needsRefresh = isTokenNearExpiry(token, 30); // 30 minutos antes do vencimento
    
    // Adiciona informações do usuário ao evento
    event.user = {
      id: decoded.sub || decoded.userId,
      email: decoded.email,
      role: decoded.role,
      permissions: decoded.permissions || [],
      name: decoded.name,
      tokenSource: decoded.tokenSource || 'secrets-manager',
      cognitoSub: decoded.cognitoSub,
      jwtId: decoded.jti,
      ...decoded
    };
    
    event.tokenNeedsRefresh = needsRefresh;
    
    console.log(`Autenticação bem-sucedida para usuário: ${event.user.email} (Secrets Manager)`);
    
    return {
      isAuthenticated: true,
      user: event.user,
      needsRefresh,
      token: token
    };
    
  } catch (error) {
    console.error('Erro no middleware de autenticação (Secrets Manager):', error);
    return {
      isAuthenticated: false,
      error: 'Erro interno de autenticação',
      response: responseWithError('Erro interno de autenticação')
    };
  }
};

/**
 * Wrapper para funções Lambda que requer autenticação usando Secrets Manager
 * @param {Function} handler - Handler da função Lambda
 * @param {Object} options - Opções de configuração
 * @returns {Function} Handler wrapeado com autenticação
 */
const withSecureAuth = (handler, options = {}) => {
  const {
    requireAuth = true,
    autoRefresh = true,
    requiredPermissions = [],
    requiredRole = null,
    allowExpiredTokens = false
  } = options;
  
  return async (event, context) => {
    try {
      // Executa autenticação
      const authResult = await authenticateRequest(event, context, {
        requireAuth,
        requiredPermissions,
        requiredRole,
        allowExpiredTokens
      });
      
      if (!authResult.isAuthenticated) {
        return authResult.response;
      }
      
      // Executa handler original
      const result = await handler(event, context);
      
      // Auto-refresh de token se necessário e habilitado
      if (autoRefresh && authResult.needsRefresh && result.statusCode === 200 && !authResult.skipAuth) {
        try {
          console.log('Renovando token automaticamente usando Secrets Manager...');
          
          // Gera novo token usando Secrets Manager
          const newToken = await generateToken({
            sub: event.user.id,
            userId: event.user.id,
            email: event.user.email,
            role: event.user.role,
            permissions: event.user.permissions,
            name: event.user.name,
            tokenSource: 'secrets-manager',
            cognitoSub: event.user.cognitoSub,
            refreshedAt: Math.floor(Date.now() / 1000)
          });
          
          // Cria novos cookies
          const newCookieHeaders = createAuthCookieHeaders(newToken, null);
          
          // Adiciona cookies à resposta
          if (!result.headers) {
            result.headers = {};
          }
          
          if (result.headers['Set-Cookie']) {
            if (Array.isArray(result.headers['Set-Cookie'])) {
              result.headers['Set-Cookie'].push(...newCookieHeaders);
            } else {
              result.headers['Set-Cookie'] = [result.headers['Set-Cookie'], ...newCookieHeaders];
            }
          } else {
            result.headers['Set-Cookie'] = newCookieHeaders;
          }
          
          console.log(`Token renovado automaticamente para usuário: ${event.user.email} (Secrets Manager)`);
          
        } catch (refreshError) {
          console.error('Erro ao renovar token automaticamente (Secrets Manager):', refreshError);
          // Não falha a requisição, apenas loga o erro
        }
      }
      
      return result;
      
    } catch (error) {
      console.error('Erro no wrapper de autenticação (Secrets Manager):', error);
      return responseWithError('Erro interno de autenticação');
    }
  };
};

/**
 * Middleware simplificado para endpoints públicos
 * @param {Function} handler - Handler da função Lambda
 * @returns {Function} Handler wrapeado
 */
const withPublicAccess = (handler) => {
  return withSecureAuth(handler, {
    requireAuth: false,
    autoRefresh: false
  });
};

/**
 * Middleware para endpoints que requerem role específica
 * @param {Function} handler - Handler da função Lambda
 * @param {string} requiredRole - Role necessária
 * @returns {Function} Handler wrapeado
 */
const withRoleAuth = (handler, requiredRole) => {
  return withSecureAuth(handler, {
    requireAuth: true,
    requiredRole,
    autoRefresh: true
  });
};

/**
 * Middleware para endpoints que requerem permissões específicas
 * @param {Function} handler - Handler da função Lambda
 * @param {Array} requiredPermissions - Permissões necessárias
 * @returns {Function} Handler wrapeado
 */
const withPermissionAuth = (handler, requiredPermissions) => {
  return withSecureAuth(handler, {
    requireAuth: true,
    requiredPermissions,
    autoRefresh: true
  });
};

module.exports = {
  authenticateRequest,
  withSecureAuth,
  withPublicAccess,
  withRoleAuth,
  withPermissionAuth,
  extractToken,
  validatePermissions
};
