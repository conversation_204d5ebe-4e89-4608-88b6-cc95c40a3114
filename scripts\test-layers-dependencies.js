#!/usr/bin/env node

/**
 * Script para testar se todas as dependências das layers estão funcionando
 * Verifica se os pacotes podem ser importados corretamente
 */

console.log('🧪 Testando dependências das Lambda Layers');
console.log('==========================================\n');

// Função para testar importação de um pacote
function testPackageImport(packageName, layerName) {
  try {
    const pkg = require(packageName);
    console.log(`✅ ${layerName}: ${packageName} - OK`);
    return true;
  } catch (error) {
    console.log(`❌ ${layerName}: ${packageName} - ERRO: ${error.message}`);
    return false;
  }
}

// Função para testar funcionalidade básica de um pacote
function testPackageFunctionality(packageName, testFunction, layerName) {
  try {
    testFunction();
    console.log(`✅ ${layerName}: ${packageName} (funcionalidade) - OK`);
    return true;
  } catch (error) {
    console.log(`❌ ${layerName}: ${packageName} (funcionalidade) - ERRO: ${error.message}`);
    return false;
  }
}

let allTestsPassed = true;

// ========================================
// TESTES DA UTILS LAYER
// ========================================
console.log('📦 UTILS LAYER');
console.log('==============');

// Testa importações básicas
allTestsPassed &= testPackageImport('axios', 'utilsLayer');
allTestsPassed &= testPackageImport('cookie-parser', 'utilsLayer');
allTestsPassed &= testPackageImport('date-fns', 'utilsLayer');
allTestsPassed &= testPackageImport('dotenv', 'utilsLayer');
allTestsPassed &= testPackageImport('jsonwebtoken', 'utilsLayer');
allTestsPassed &= testPackageImport('nodemailer', 'utilsLayer');
allTestsPassed &= testPackageImport('uuid', 'utilsLayer');
allTestsPassed &= testPackageImport('yamljs', 'utilsLayer');
allTestsPassed &= testPackageImport('yup', 'utilsLayer');

// Testa funcionalidades críticas
allTestsPassed &= testPackageFunctionality('jsonwebtoken', () => {
  const jwt = require('jsonwebtoken');
  const token = jwt.sign({ test: 'data' }, 'secret', { expiresIn: '1h' });
  jwt.verify(token, 'secret');
}, 'utilsLayer');

allTestsPassed &= testPackageFunctionality('cookie-parser', () => {
  const cookieParser = require('cookie-parser');
  // Testa se é uma função
  if (typeof cookieParser !== 'function') {
    throw new Error('cookie-parser não é uma função');
  }
}, 'utilsLayer');

allTestsPassed &= testPackageFunctionality('uuid', () => {
  const { v4: uuidv4 } = require('uuid');
  const id = uuidv4();
  if (!id || id.length !== 36) {
    throw new Error('UUID gerado é inválido');
  }
}, 'utilsLayer');

allTestsPassed &= testPackageFunctionality('date-fns', () => {
  const { format } = require('date-fns');
  const formatted = format(new Date(), 'yyyy-MM-dd');
  if (!formatted.match(/^\d{4}-\d{2}-\d{2}$/)) {
    throw new Error('Formatação de data falhou');
  }
}, 'utilsLayer');

console.log('');

// ========================================
// TESTES DA AWS CORE LAYER
// ========================================
console.log('📦 AWS CORE LAYER');
console.log('=================');

allTestsPassed &= testPackageImport('aws-sdk', 'awsCoreLayer');
allTestsPassed &= testPackageImport('aws-multipart-parser', 'awsCoreLayer');
allTestsPassed &= testPackageImport('athena-express', 'awsCoreLayer');

// Testa funcionalidades AWS
allTestsPassed &= testPackageFunctionality('aws-sdk', () => {
  const AWS = require('aws-sdk');
  const s3 = new AWS.S3();
  if (!s3.listBuckets) {
    throw new Error('AWS SDK S3 não inicializado corretamente');
  }
}, 'awsCoreLayer');

allTestsPassed &= testPackageFunctionality('aws-multipart-parser', () => {
  const { parse } = require('aws-multipart-parser');
  if (typeof parse !== 'function') {
    throw new Error('aws-multipart-parser parse não é uma função');
  }
}, 'awsCoreLayer');

console.log('');

// ========================================
// TESTES DA DOCUMENT GENERATION LAYER
// ========================================
console.log('📦 DOCUMENT GENERATION LAYER');
console.log('============================');

allTestsPassed &= testPackageImport('pdfmake', 'documentGenerationLayer');
allTestsPassed &= testPackageImport('excel4node', 'documentGenerationLayer');

// Testa funcionalidades de geração de documentos
allTestsPassed &= testPackageFunctionality('excel4node', () => {
  const xl = require('excel4node');
  const wb = new xl.Workbook();
  if (!wb.addWorksheet) {
    throw new Error('Excel4node não inicializado corretamente');
  }
}, 'documentGenerationLayer');

allTestsPassed &= testPackageFunctionality('pdfmake', () => {
  const pdfMake = require('pdfmake/build/pdfmake');
  if (!pdfMake.createPdf) {
    throw new Error('PDFMake não inicializado corretamente');
  }
}, 'documentGenerationLayer');

console.log('');

// ========================================
// RESUMO DOS TESTES
// ========================================
console.log('📊 RESUMO DOS TESTES');
console.log('===================');

if (allTestsPassed) {
  console.log('✅ Todos os testes passaram!');
  console.log('✅ Todas as layers estão funcionando corretamente');
  console.log('✅ Dependências instaladas e funcionais');
  console.log('');
  console.log('🚀 LAYERS PRONTAS PARA DEPLOY!');
} else {
  console.log('❌ Alguns testes falharam');
  console.log('❌ Verifique as dependências das layers');
  console.log('❌ Execute npm install nas pastas das layers');
  process.exit(1);
}

console.log('');
console.log('📋 CONFIGURAÇÃO ATUAL DAS LAYERS:');
console.log('- utilsLayer: axios, cookie-parser, date-fns, dotenv, jsonwebtoken, nodemailer, uuid, yamljs, yup');
console.log('- awsCoreLayer: aws-sdk, aws-multipart-parser, athena-express');
console.log('- documentGenerationLayer: pdfmake, excel4node');
