/**
 * Testes de integração para endpoints de autenticação
 * Testa os endpoints rodando no Serverless Offline
 */

// Configurar variáveis de ambiente para teste
process.env.JWT_SECRET = 'your-super-secret-jwt-key-change-in-production-min-32-chars';
process.env.JWT_EXPIRES_IN = '8h';
process.env.JWT_REFRESH_EXPIRES_IN = '7d';
process.env.ALLOWED_ORIGINS = 'http://localhost:3000,https://dev.dsm.darede.com.br';
process.env.NODE_ENV = 'test';

const http = require('http');
const { generateToken } = require('../../auth/jwt-utils-cjs');

const BASE_URL = 'http://localhost:8000/';
let cookies = '';

// Função helper para fazer requisições HTTP
function makeRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 8000,
      path: path,
      // path: `/dev${path}`,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (cookies) {
      options.headers['Cookie'] = cookies;
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        if (res.headers['set-cookie']) {
          cookies = res.headers['set-cookie'].join('; ');
        }
        
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: responseData
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

describe('Auth Endpoints Integration Tests', () => {
  const testUser = {
    sub: 'user123',
    email: '<EMAIL>',
    role: 'admin',
    name: 'Test User',
    permissions: ['users:read', 'users:write']
  };

  let validToken;

  beforeAll(() => {
    validToken = generateToken(testUser);
  });

  beforeEach(() => {
    cookies = '';
  });

  describe('POST /auth/set-token', () => {
    test('should set token in cookies successfully', async () => {
      const response = await makeRequest('POST', '/auth/set-token', { token: validToken });
      
      expect(response.statusCode).toBe(200);
      expect(response.headers['set-cookie']).toBeDefined();
      
      const body = JSON.parse(response.body);
      expect(body.message).toContain('Token armazenado com sucesso');
      expect(body.user.email).toBe(testUser.email);
      expect(body.tokenSet).toBe(true);
    });

    test('should reject request without token', async () => {
      const response = await makeRequest('POST', '/auth/set-token', {});
      
      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.message).toContain('Token é obrigatório');
    });

    test('should reject invalid token', async () => {
      const response = await makeRequest('POST', '/auth/set-token', { token: 'invalid-token' });
      
      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.message).toContain('Token inválido');
    });
  });

  describe('GET /auth/verify', () => {
    test('should verify authentication with valid cookies', async () => {
      // First set the token
      await makeRequest('POST', '/auth/set-token', { token: validToken });
      
      // Then verify
      const response = await makeRequest('GET', '/auth/verify');
      
      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.body);
      expect(body.authenticated).toBe(true);
      expect(body.user.email).toBe(testUser.email);
      expect(body.tokenStatus.valid).toBe(true);
    });

    test('should reject verification without cookies', async () => {
      const response = await makeRequest('GET', '/auth/verify');
      
      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.message).toContain('Token de acesso não encontrado');
    });
  });

  describe('POST /auth/logout', () => {
    test('should logout and clear cookies', async () => {
      // First set the token
      await makeRequest('POST', '/auth/set-token', { token: validToken });
      
      // Then logout
      const response = await makeRequest('POST', '/auth/logout');
      
      expect(response.statusCode).toBe(200);
      expect(response.headers['set-cookie']).toBeDefined();
      
      const body = JSON.parse(response.body);
      expect(body.message).toContain('Logout realizado com sucesso');
      
      // Verify cookies are cleared (Max-Age=0)
      const setCookies = Array.isArray(response.headers['set-cookie']) 
        ? response.headers['set-cookie'] 
        : [response.headers['set-cookie']];
      
      setCookies.forEach(cookie => {
        expect(cookie).toContain('Max-Age=0');
      });
    });
  });

  describe('Auth Flow Integration', () => {
    test('should complete full authentication flow', async () => {
      // 1. Set token
      const setResponse = await makeRequest('POST', '/auth/set-token', { token: validToken });
      expect(setResponse.statusCode).toBe(200);
      
      // 2. Verify authentication
      const verifyResponse = await makeRequest('GET', '/auth/verify');
      expect(verifyResponse.statusCode).toBe(200);
      
      // 3. Logout
      const logoutResponse = await makeRequest('POST', '/auth/logout');
      expect(logoutResponse.statusCode).toBe(200);
      
      // 4. Verify logout cleared authentication
      const verifyAfterLogoutResponse = await makeRequest('GET', '/auth/verify');
      expect(verifyAfterLogoutResponse.statusCode).toBe(400);
    });
  });
});
