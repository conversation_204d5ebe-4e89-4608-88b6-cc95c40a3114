import { makeS3 } from "../../shared/services/s3-service";
import { errorValidator } from "../../shared/validators";

import { parse } from "aws-multipart-parser";

import {
  STATUS_CODE,
  responseWithError,
  responseWithSuccess,
  responseWithBadRequest,
} from "../../shared/response";

const s3 = makeS3();

export const handler = async (event) => {
  try {
    let { bucketName, obj, fileName, contentType } = parse(event);

    const decriptFileName = Buffer.from(fileName, "base64").toString("utf8");
    const decriptBucketName = Buffer.from(bucketName, "base64").toString(
      "utf8"
    );
    const decriptContentType = Buffer.from(contentType, "base64").toString(
      "utf8"
    );

    let params = {
      Body: obj,
      Key: encodeURI(decriptFileName),
      ContentEncoding: "base64",
      Bucket: decriptBucketName,
      ContentType: decriptContentType,
    };

    if (decriptContentType.includes("image")) {
      params.Body = Buffer.from(
        params.Body.replace(/^data:image\/\w+;base64,/, ""),
        "base64"
      );
    } else if (decriptContentType.includes("application")) {
      params.Body = Buffer.from(
        params.Body.replace(/^data:application\/\w+;base64,/, ""),
        "base64"
      );
    }

    const responseS3 = await s3.upload(params).promise();
    console.log({ responseS3 });

    return responseWithSuccess(responseS3, "Upload realizado com sucesso");
  } catch (error) {
    const { statusCode, message } = errorValidator(error);

    if (STATUS_CODE.BAD_REQUEST === statusCode)
      return responseWithBadRequest(message);

    return responseWithError(message);
  }
};
