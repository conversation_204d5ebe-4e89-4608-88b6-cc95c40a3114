import { accessKeysCustomerAccount } from "../../accessKeysCustomerAccount";

const AWS = require("aws-sdk");

const region = "us-east-1";

export async function verifySCP(account) {
  console.log("\nVerifying SCP in organization...");

  const newSession = await accessKeysCustomerAccount(account);

  if (newSession) {
    const organizations = new AWS.Organizations({
      region,
      credentials: {
        accessKeyId: newSession.access_key_id,
        secretAccessKey: newSession.secret_key_id,
        sessionToken: newSession.session_token,
      },
      MaxResults: 3,
    });

    let scps = await organizations
      .listRoots()
      .promise()
      .catch(() => {
        return false;
      });

    if (!scps) return false;

    console.log("SCPS\n", JSON.stringify(scps));

    let scpEnabled = false;
    for (let i = 0; i < scps?.Roots?.length; i++) {
      const root = scps?.Roots[i];

      for (let j = 0; j < root?.PolicyTypes?.length; j++) {
        const policyType = root?.PolicyTypes[j];
        if (
          policyType.Type === "SERVICE_CONTROL_POLICY" &&
          policyType.Status === "ENABLED"
        ) {
          scpEnabled = true;
          break;
        }
      }

      console.log({ scpEnabled });

      if (scpEnabled) {
        return true;
      }
    }
  } else {
    return false;
  }
}
