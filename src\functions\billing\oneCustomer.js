import { parseBody } from '../../shared/parsers'
import { message, json } from '../../shared/response'
import { create, put } from '../../model/dynamo'

import aws from 'aws-sdk'
import AthenaExpress from 'athena-express'
import { v4 } from 'uuid'

async function sendDataToUser (status, statusString, data) {
  const msg = await message(statusString, data)
  return await json(msg, status)
}

export const handler = async (event, context) => {
  const body = parseBody(event)

  aws.config.update({
    accessKeyId: body.access_key_id,
    secretAccessKey: body.secret_key_id,
    region: process.env.AWS_REGION_LOCATION
  })

  const athenaExpress = new AthenaExpress({
    aws,
    db: body.db_prefix + '_' + body.db_athena,
    s3: 's3://' + body.s3_output
  })

  let query
  try {
    query = await athenaExpress.query(`
      SELECT line_item_usage_account_id,
      round(sum(line_item_unblended_cost), 2) AS unblended_cost,
      month,
      bill_invoice_id,
      bill_billing_entity,
      bill_payer_account_id,
      line_item_currency_code,
      line_item_legal_entity,
      round(sum(discount_total_discount), 2) AS discount_total_discount,
      round(sum(savings_plan_total_commitment_to_date), 2) AS savings_plan_total_commitment_to_date
      FROM ${body.db_athena}
      WHERE year='${body.year}' and month='${body.month}'
      GROUP BY line_item_usage_account_id, month, bill_invoice_id, bill_billing_entity, bill_payer_account_id, line_item_currency_code, line_item_legal_entity
      ORDER by 2 DESC;
    `)
  } catch (err) {
    query = await athenaExpress.query(`
      SELECT line_item_usage_account_id,
      round(sum(line_item_unblended_cost), 2) AS unblended_cost,
      month,
      bill_invoice_id,
      bill_billing_entity,
      bill_payer_account_id,
      line_item_currency_code,
      line_item_legal_entity,
      round(sum(discount_bundled_discount), 2) AS discount_total_discount,
      round(sum(savings_plan_total_commitment_to_date), 2) AS savings_plan_total_commitment_to_date
      FROM ${body.db_athena}
      WHERE year='${body.year}' and month='${body.month}'
      GROUP BY line_item_usage_account_id, month, bill_invoice_id, bill_billing_entity, bill_payer_account_id, line_item_currency_code, line_item_legal_entity
      ORDER by 2 DESC;
    `)
  }

  let totalUnblended = 0
  let totalDiscount = 0
  let totalSavingPlans = 0
  const onlySubAccounts = []
  query.Items.forEach((queryData) => {
    totalUnblended += queryData.unblended_cost
    totalDiscount += queryData.discount_total_discount
    totalSavingPlans += queryData.savings_plan_total_commitment_to_date

    onlySubAccounts.push({
      sub_account: queryData.line_item_usage_account_id,
      cost: queryData.unblended_cost,
      legal_entity: queryData.line_item_legal_entity,
      bill_entity: queryData.bill_billing_entity
    })
  })

  return await sendDataToUser(200, 'success', {
    total: totalDiscount + totalUnblended + totalSavingPlans,
    totalUnblended,
    totalDiscount,
    totalSavingPlans,
    resultQuery: query.Items
  })
}
