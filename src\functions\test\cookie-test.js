/**
 * Endpoint de teste para verificar configuração de cookies
 * Usado para debug de problemas de cookies HttpOnly
 */

const { responseWithSuccess } = require('../../shared/response-cjs');
const { withPublicCors } = require('../../shared/cors/cors-middleware');
const { parseCookies } = require('../../shared/auth/cookie-utils-cjs');

const cookieTestHandler = async (event, context) => {
  console.log('🍪 Cookie Test endpoint chamado');
  console.log('Headers recebidos:', event.headers);
  
  // Parse dos cookies
  const cookies = parseCookies(event);
  console.log('Cookies parseados:', cookies);
  
  // Informações sobre cookies
  const cookieInfo = {
    method: event.httpMethod,
    path: event.path,
    origin: event.headers?.Origin || event.headers?.origin,
    timestamp: new Date().toISOString(),
    
    // Headers de cookies
    cookieHeader: event.headers?.Cookie || event.headers?.cookie,
    
    // Cookies parseados
    parsedCookies: cookies,
    cookieCount: Object.keys(cookies).length,
    
    // Cookies específicos da aplicação
    applicationCookies: {
      accessToken: cookies['dsm_access_token'] ? 'Presente' : 'Ausente',
      refreshToken: cookies['dsm_refresh_token'] ? 'Presente' : 'Ausente'
    },
    
    // Informações do ambiente
    environment: {
      nodeEnv: process.env.NODE_ENV,
      cookieDomain: process.env.COOKIE_DOMAIN,
      stage: process.env.STAGE
    }
  };
  
  console.log('✅ Cookie Test - Informações coletadas:', cookieInfo);
  
  // Resposta com cookie de teste
  const response = responseWithSuccess({
    message: 'Cookie Test endpoint funcionando',
    status: 'success',
    data: cookieInfo
  });
  
  // Adiciona cookie de teste na resposta
  if (!response.headers) {
    response.headers = {};
  }
  
  // Cookie de teste simples
  response.headers['Set-Cookie'] = 'test_cookie=test_value; Path=/; HttpOnly; SameSite=Lax; Secure';
  
  return response;
};

// Exporta com middleware de CORS público (não requer autenticação)
export const handler = withPublicCors(cookieTestHandler);
