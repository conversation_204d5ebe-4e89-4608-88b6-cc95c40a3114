import { verifyPolicyDenyLeaveOrgInOU } from "./verifyPolicyDenyLeaveOrgInOU";
import { verifyRootDenyLeavOrg } from "./verifyRootDenyLeaveOrg";

export async function verifyOUDenyLeaveOrg(org, rootId) {
  const allOrganizationsUnits = await org
    .listOrganizationalUnitsForParent({
      ParentId: rootId,
    })
    .promise();

  console.log(JSON.stringify(allOrganizationsUnits));

  let hasAtLeastOneOUWithoutDenyLeavOrg = false;

  for (let i = 0; i < allOrganizationsUnits?.OrganizationalUnits?.length; i++) {
    const orgUnit = allOrganizationsUnits.OrganizationalUnits[i];

    console.log("\nVerifying org unit ", orgUnit.Name);

    const orgUnitAccountHasCorrectTarget = await verifyPolicyDenyLeaveOrgInOU(
      org,
      orgUnit.Id,
      orgUnit.Name
    );

    console.log({ orgUnitAccountHasCorrectTarget });

    if (!orgUnitAccountHasCorrectTarget) {
      // return false;
      hasAtLeastOneOUWithoutDenyLeavOrg = true;
    }
  }

  if (hasAtLeastOneOUWithoutDenyLeavOrg) {
    return false;
  } else {
    return true;
  }
}
