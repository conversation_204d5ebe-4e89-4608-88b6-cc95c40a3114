import { Lamb<PERSON> } from "aws-sdk";
import { getSecretAccounts } from "../billing/secret";
import { message, json } from "../../shared/response";

const OTRS_ACCOUNT = process.env.OTRS_ACCOUNT;
const DISPARITY_CONTRACT_FUNCTION = process.env.DISPARITY_CONTRACT_FUNCTION;

async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}
export const handler = async (event, context) => {
  try {
    const secret = await getSecretAccounts(OTRS_ACCOUNT);

    const lambda = new Lambda({
      region: process.env.AWS_REGION_LOCATION,
      credentials: {
        accessKeyId: secret.access_key_id,
        secretAccessKey: secret.secret_key_id,
        sessionToken: secret.session_token,
      },
    });

    await lambda
      .invoke({
        FunctionName: DISPARITY_CONTRACT_FUNCTION,
        Payload: event.Records[0].Sns.Message,
      })
      .promise();

    return await sendDataToUser(200, "success", {});
  } catch (error) {
    return await sendDataToUser(500, "error", error);
  }
};
