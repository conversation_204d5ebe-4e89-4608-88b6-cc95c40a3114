generate-link:
  handler: src/functions/s3/linkGenerator.handler
  name: ${env:STAGE}-generate-link${env:VERSION}
  description: Função para geração de link para ações em objetos no S3
  memorySize: 128
  events:
    - http:
        path: /s3/link
        method: post
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

upload-obj-to-bucket:
  handler: src/functions/s3/uploadObjToBucket.handler
  name: ${env:STAGE}-upload-obj-to-bucket${env:VERSION}
  description: Função para fazer upload de objetos para um determinado bucket
  memorySize: 128
  events:
    - http:
        path: /upload/object
        method: post
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

copy-obj-to-another-bucket:
  handler: src/functions/s3/copyObjToAnotherBucket.handler
  name: ${env:STAGE}-copy-obj-to-another-bucket${env:VERSION}
  description: Função para fazer a cópia de objetos para um determinado bucket
  memorySize: 128
  events:
    - http:
        path: /copy/object
        method: post
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

delete-multiple-objects:
  handler: src/functions/s3/deleteMultipleObjects.handler
  name: ${env:STAGE}-delete-multiple-objects${env:VERSION}
  description: Função para fazer a deleção de objetos de um determinado bucket
  memorySize: 128
  events:
    - http:
        path: /delete/objects
        method: post
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

list-bucket-objects:
  handler: src/functions/s3/listObjectsInBucket.handler
  name: ${env:STAGE}-list-bucket-objects${env:VERSION}
  description: Função para fazer a listagem de objetos de um determinado bucket
  memorySize: 128
  events:
    - http:
        path: /list/objects
        method: post
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}


