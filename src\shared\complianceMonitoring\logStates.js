export async function logStates(mfa, cur, tagCritical, account) {
  if (mfa) {
    console.log("MFA is configured to this account: ", { account });
  } else {
    console.log("MFA isNOT configured to this account: ", { account });
  }

  if (cur) {
    console.log("CUR is configured to this account: ", { account });
  } else {
    console.log("CUR isNOT configured to this account: ", { account });
  }

  if (tagCritical) {
    console.log("Tag Critical is configured to this account: ", {
      account,
    });
  } else {
    console.log("Tag Critical isNOT configured to this account: ", {
      account,
    });
  }
}
