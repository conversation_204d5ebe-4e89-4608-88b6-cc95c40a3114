// import { parseBody, parseHeaders } from '../../shared/parsers'
import { querySequence, readAll } from "../../model/dynamo";
import { getCustomerName } from "../../shared/complianceMonitoring/getCustomerName";
import { splitCustomersArr } from "../../shared/complianceMonitoring/splitCustomerInSubArray";
import { parseBody } from "../../shared/parsers";
import { message, json } from "../../shared/response";
import { main } from "../compliance/checkAccountsStatus";

import { Lambda } from "aws-sdk";
const lambda = new Lambda({
  region: process.env.AWS_REGION_LOCATION,
});

async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

export const handler = async (event, context) => {
  let customers = await readAll(`${process.env.FINOPS_STAGE}-customers`);
  customers = customers.Items;
  console.log("customers.length: ", customers.length);

  const customersSplitted = await splitCustomersArr(customers, 500);

  console.log({ customersSplitted });

  const body = parseBody(event); // coment this to run locally
  console.log({ body });
  let token =
    body.NextToken && !isNaN(body.NextToken) ? Number(body.NextToken) : 0;
  console.log({ token });

  function invokeLambdaAsync(params) {
    return new Promise((resolve, reject) => {
      lambda.invoke(params, (err, data) => {
        if (err) {
          reject(err);
        } else {
          resolve(data);
        }
      });
    });
  }

  console.log(typeof customersSplitted);

  if (customersSplitted) {
    let invocationPromises = [];
    customersSplitted[token].forEach((customer) => {
      // main(customer.id);

      const payload = { customerId: `${customer.id}` };

      const params = {
        FunctionName:
          process.env.FINOPS_STAGE + "-compliance-check-accounts-status",
        InvocationType: "Event",
        Payload: JSON.stringify(payload),
      };

      invocationPromises.push(invokeLambdaAsync(params));
    });

    await Promise.all(invocationPromises);
    token += 1;
  }

  if (token >= customersSplitted.length) {
    token = false;
    console.log("Token reached customersSplitted length.");
  }

  // return await sendDataToUser(200, "success", {
  //   NextToken: token,
  // });
  return {
    statusCode: 200,
    message: "success",
    body: {
      NextToken: token,
    },
  };
};
