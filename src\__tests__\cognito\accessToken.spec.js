import { handler } from "../../functions/cognito/accessToken";

describe("Get Token OTRS", () => {
  it("should return access token on successful request", async () => {
    const mockAccessToken = "mockAccessToken";
    const expected = {
      body: JSON.stringify({ status: "success", data: mockAccessToken }),
      headers: { "Access-Control-Allow-Origin": "*" },
      statusCode: 200,
    };

    jest.spyOn(require("axios"), "post").mockResolvedValue({
      data: {
        access_token: mockAccessToken,
      },
    });

    const result = await handler();

    expect(result).toEqual(expected);
  });
  it("should return error 500", async () => {
    const expected = {
      body: JSON.stringify({ status: "error", data: {} }),
      headers: { "Access-Control-Allow-Origin": "*" },
      statusCode: 500,
    };

    jest.spyOn(require("axios"), "post").mockResolvedValue({
      error: "invalid_client",
    });

    const result = await handler();

    expect(result).toEqual(expected);
  });
});
