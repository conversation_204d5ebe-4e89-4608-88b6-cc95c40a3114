/**
 * Backend Environment Configuration
 * Centralized configuration for different environments
 */

export const ENVIRONMENTS = {
  LOCAL: 'local',
  DEV: 'dev',
  HML: 'hml', 
  PROD: 'prod'
};

/**
 * Get current environment
 */
export const getCurrentEnvironment = () => {
  return process.env.STAGE?.toLowerCase() || ENVIRONMENTS.LOCAL;
};

/**
 * Environment-specific configurations
 */
export const getEnvironmentConfig = (environment = getCurrentEnvironment()) => {
  const configs = {
    [ENVIRONMENTS.LOCAL]: {
      name: 'Local Development',
      stage: 'local',
      region: process.env.AWS_REGION_LOCATION || 'us-east-1',
      isProduction: false,
      enableDebug: true,
      enableDetailedLogging: true,
      corsOrigins: [
        'http://localhost:3000',
        'http://localhost:8000',
        'http://127.0.0.1:3000',
        'http://127.0.0.1:8000'
      ],
      cookieDomain: 'localhost',
      cookieSecure: false,
      apiUrls: {
        frontend: 'http://localhost:3000',
        api: 'http://localhost:8000'
      }
    },
    
    [ENVIRONMENTS.DEV]: {
      name: 'Development',
      stage: 'dev',
      region: process.env.AWS_REGION_LOCATION || 'us-east-1',
      isProduction: false,
      enableDebug: true,
      enableDetailedLogging: true,
      corsOrigins: [
        'https://dev.dsm.darede.com.br',
        'https://www.dev.dsm.darede.com.br'
      ],
      cookieDomain: '.dsm.darede.com.br',
      cookieSecure: true,
      apiUrls: {
        frontend: 'https://dev.dsm.darede.com.br',
        api: 'https://pdu9dbemj1.execute-api.us-east-1.amazonaws.com/dev'
      }
    },
    
    [ENVIRONMENTS.HML]: {
      name: 'Homologation',
      stage: 'hml',
      region: process.env.AWS_REGION_LOCATION || 'us-east-1',
      isProduction: false,
      enableDebug: false,
      enableDetailedLogging: true,
      corsOrigins: [
        'https://hml.dsm.com.br',
        'https://www.hml.dsm.com.br'
      ],
      cookieDomain: '.dsm.com.br',
      cookieSecure: true,
      apiUrls: {
        frontend: 'https://hml.dsm.com.br',
        api: 'https://api.hml.dsm.com.br'
      }
    },
    
    [ENVIRONMENTS.PROD]: {
      name: 'Production',
      stage: 'prod',
      region: process.env.AWS_REGION_LOCATION || 'us-east-1',
      isProduction: true,
      enableDebug: false,
      enableDetailedLogging: false,
      corsOrigins: [
        'https://dsm.com.br',
        'https://www.dsm.com.br'
      ],
      cookieDomain: '.dsm.com.br',
      cookieSecure: true,
      apiUrls: {
        frontend: 'https://dsm.com.br',
        api: 'https://api.dsm.com.br'
      }
    }
  };
  
  return configs[environment] || configs[ENVIRONMENTS.LOCAL];
};

/**
 * Get CORS origins for current environment
 */
export const getCorsOrigins = (environment = getCurrentEnvironment()) => {
  return getEnvironmentConfig(environment).corsOrigins;
};

/**
 * Get cookie configuration for current environment
 */
export const getCookieConfig = (environment = getCurrentEnvironment()) => {
  const config = getEnvironmentConfig(environment);
  
  return {
    domain: config.cookieDomain,
    secure: config.cookieSecure,
    sameSite: 'Strict',
    httpOnly: true,
    path: '/'
  };
};

/**
 * Get API URLs for current environment
 */
export const getApiUrls = (environment = getCurrentEnvironment()) => {
  const config = getEnvironmentConfig(environment);
  return config.apiUrls;
};

/**
 * Environment detection utilities
 */
export const isLocal = () => getCurrentEnvironment() === ENVIRONMENTS.LOCAL;
export const isDev = () => getCurrentEnvironment() === ENVIRONMENTS.DEV;
export const isHml = () => getCurrentEnvironment() === ENVIRONMENTS.HML;
export const isProd = () => getCurrentEnvironment() === ENVIRONMENTS.PROD;
export const isProduction = () => getEnvironmentConfig().isProduction;

/**
 * Debug and logging utilities
 */
export const shouldEnableDebug = () => getEnvironmentConfig().enableDebug;
export const shouldEnableDetailedLogging = () => getEnvironmentConfig().enableDetailedLogging;

/**
 * Get environment info for logging
 */
export const getEnvironmentInfo = () => {
  const env = getCurrentEnvironment();
  const config = getEnvironmentConfig(env);
  
  return {
    environment: env,
    name: config.name,
    stage: config.stage,
    region: config.region,
    isProduction: config.isProduction,
    timestamp: new Date().toISOString(),
    nodeVersion: process.version,
    platform: process.platform
  };
};

/**
 * Validate environment configuration
 */
export const validateEnvironmentConfig = () => {
  const env = getCurrentEnvironment();
  const config = getEnvironmentConfig(env);
  
  const requiredEnvVars = [
    'AWS_REGION_LOCATION',
    'JWT_DECRIPTION_CREDENTIALS'
  ];
  
  const missing = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
  
  return {
    isValid: true,
    environment: env,
    config: config
  };
};

/**
 * Get security headers based on environment
 */
export const getSecurityHeaders = (environment = getCurrentEnvironment()) => {
  const config = getEnvironmentConfig(environment);
  
  const baseHeaders = {
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin'
  };
  
  if (config.isProduction) {
    baseHeaders['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload';
  }
  
  return baseHeaders;
};

/**
 * Get rate limiting configuration based on environment
 */
export const getRateLimitConfig = (environment = getCurrentEnvironment()) => {
  const configs = {
    [ENVIRONMENTS.LOCAL]: {
      maxRequests: 1000,
      windowMs: 15 * 60 * 1000, // 15 minutes
      skipSuccessfulRequests: true
    },
    [ENVIRONMENTS.DEV]: {
      maxRequests: 500,
      windowMs: 15 * 60 * 1000,
      skipSuccessfulRequests: true
    },
    [ENVIRONMENTS.HML]: {
      maxRequests: 200,
      windowMs: 15 * 60 * 1000,
      skipSuccessfulRequests: false
    },
    [ENVIRONMENTS.PROD]: {
      maxRequests: 100,
      windowMs: 15 * 60 * 1000,
      skipSuccessfulRequests: false
    }
  };
  
  return configs[environment] || configs[ENVIRONMENTS.LOCAL];
};

// Export current environment config as default
export default getEnvironmentConfig();
