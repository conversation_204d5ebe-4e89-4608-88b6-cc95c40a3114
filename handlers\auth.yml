# set-token:
#   handler: src/functions/auth/set-token.handler
#   name: ${self:custom.dotenv.STAGE}-set-token${self:custom.dotenv.VERSION}
#   description: Função para armazenar token JWT em cookies HttpOnly seguros (DESABILITADO - HttpOnly)
#   memorySize: 128
#   timeout: 30
#   events:
#     - http:
#         path: /auth/set-token
#         method: post
#         cors: true

# set-auth-cookie:
#   handler: src/functions/auth/setCookie.handler
#   name: ${self:custom.dotenv.STAGE}-set-auth-cookie${self:custom.dotenv.VERSION}
#   description: Função para definir cookie de autenticação HttpOnly (DESABILITADO - HttpOnly)
#   memorySize: 128
#   events:
#     - http:
#         path: /auth/set-cookie
#         method: post
#         cors: true
#     - http:
#         path: /auth/set-cookie
#         method: options
#         cors: true

# refresh-token:
#   handler: src/functions/auth/refresh.handler
#   name: ${self:custom.dotenv.STAGE}-refresh-token${self:custom.dotenv.VERSION}
#   description: Função para renovar token de acesso usando refresh token (DESABILITADO - HttpOnly)
#   memorySize: 128
#   timeout: 30
#   events:
#     - http:
#         path: /auth/refresh
#         method: post
#         cors: true
#     - http:
#         path: /auth/refresh
#         method: options
#         cors: true

# verify-auth:
#   handler: src/functions/auth/verify.handler
#   name: ${self:custom.dotenv.STAGE}-verify-auth${self:custom.dotenv.VERSION}
#   description: Função para verificar autenticação via cookie (DESABILITADO - HttpOnly)
#   memorySize: 128
#   timeout: 30
#   events:
#     - http:
#         path: /auth/verify
#         method: get
#         cors: true
#     - http:
#         path: /auth/verify
#         method: options
#         cors: true

# logout:
#   handler: src/functions/auth/logout.handler
#   name: ${self:custom.dotenv.STAGE}-logout${self:custom.dotenv.VERSION}
#   description: Função para realizar logout e limpar cookies de autenticação (DESABILITADO - HttpOnly)
#   memorySize: 128
#   timeout: 30
#   events:
#     - http:
#         path: /auth/logout
#         method: post
#         cors: true
#     - http:
#         path: /auth/logout
#         method: options
#         cors: true

# check-cookie-support:
#   handler: src/functions/auth/check-cookie-support.handler
#   name: ${self:custom.dotenv.STAGE}-check-cookie-support${self:custom.dotenv.VERSION}
#   description: Função para verificar suporte a cookies HttpOnly no cliente (DESABILITADO - HttpOnly)
#   memorySize: 128
#   timeout: 30
#   events:
#     - http:
#         path: /auth/check-cookie-support
#         method: get
#         cors: true

# config:
#   handler: src/functions/auth/config.handler
#   name: ${self:custom.dotenv.STAGE}-auth-config${self:custom.dotenv.VERSION}
#   description: Função para fornecer configurações de autenticação para o frontend (DESABILITADO - HttpOnly)
#   memorySize: 128
#   timeout: 30
#   events:
#     - http:
#         path: /auth/config
#         method: get
#         cors: true

# cognito-to-cookie:
#   handler: src/functions/auth/cognito-to-cookie.handler
#   name: ${self:custom.dotenv.STAGE}-cognito-to-cookie${self:custom.dotenv.VERSION}
#   description: Função para converter token Cognito em cookies HttpOnly seguros (DESABILITADO - HttpOnly)
#   memorySize: 128
#   timeout: 30
#   events:
#     - http:
#         path: /auth/cognito-to-cookie
#         method: post
#         cors: true

