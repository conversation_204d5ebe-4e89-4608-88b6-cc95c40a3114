import { <PERSON><PERSON><PERSON><PERSON>, STS } from "aws-sdk";

const cli = new SecretsManager({
  region: process.env.AWS_REGION_LOCATION,
});

export async function getSecretAccounts(account) {
  const token = await cli
    .getSecretValue({
      SecretId: process.env.AWS_SECRET_VALUE_ACCOUNTS,
    })
    .promise();

  const stAccount = account?.toLowerCase();
  const accounts = JSON.parse(token.SecretString);
  const objPart = {};

  for (let i = 0; i < Object.entries(accounts).length; i++) {
    // if (Object.entries(accounts)[i][0].includes(stAccount + '_access_key')) {
    //   objPart.access_key_id = Object.entries(accounts)[i][1]
    // };

    // if (Object.entries(accounts)[i][0].includes(stAccount + '_secret_key')) {
    //   objPart.secret_key_id = Object.entries(accounts)[i][1]
    // };

    if (Object.entries(accounts)[i][0].includes(stAccount + "_db_athena")) {
      objPart.db_athena = Object.entries(accounts)[i][1];
    }

    if (Object.entries(accounts)[i][0].includes(stAccount + "_s3_output")) {
      objPart.s3_output = Object.entries(accounts)[i][1];
    }

    if (Object.entries(accounts)[i][0].includes(stAccount + "_db_prefix")) {
      objPart.db_prefix = Object.entries(accounts)[i][1];
    }
  }

  const region = process.env.AWS_REGION_LOCATION;

  const stsDSMAccount = new STS({ region });
  const credentialsRootAccount = await stsDSMAccount
    .assumeRole({
      RoleArn: "arn:aws:iam::************:role/jump-access-roles",
      RoleSessionName: "darede-************",
    })
    .promise();

  const stsRootAccount = new STS({
    region,
    credentials: {
      accessKeyId: credentialsRootAccount.Credentials.AccessKeyId,
      secretAccessKey: credentialsRootAccount.Credentials.SecretAccessKey,
      sessionToken: credentialsRootAccount.Credentials.SessionToken,
    },
  });
  const credentialsCustomerAccount = await stsRootAccount
    .assumeRole({
      RoleArn: "arn:aws:iam::" + account + ":role/darede-full",
      RoleSessionName: "darede-" + account,
    })
    .promise();

  objPart.access_key_id = credentialsCustomerAccount.Credentials.AccessKeyId;
  objPart.secret_key_id =
    credentialsCustomerAccount.Credentials.SecretAccessKey;
  objPart.session_token = credentialsCustomerAccount.Credentials.SessionToken;

  return objPart;
}
