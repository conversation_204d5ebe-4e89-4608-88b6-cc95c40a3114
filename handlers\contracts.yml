get-contracts:
  handler: src/functions/contracts/get-contracts.handler
  name: ${self:custom.dotenv.STAGE}-get-contracts${self:custom.dotenv.VERSION}
  description: Função para buscar os clientes do DSM
  memorySize: 128
  timeout: 30
  events:
    - http:
        path: /contracts
        method: get
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}

get-reasons-to-deactivate-contract:
  handler: src/functions/contracts/get-reasons-to-deactivate-contract.handler
  name: ${self:custom.dotenv.STAGE}-get-reasons-to-deactivate-contract${self:custom.dotenv.VERSION}
  description: Função para fazer a listagem de opções dos motivos para desativar um contrato.
  memorySize: 128
  events:
    - http:
        path: /contracts/reasons-to-deactivate
        method: get
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}

create-contract:
  handler: src/functions/contracts/create-contract.handler
  name: ${self:custom.dotenv.STAGE}-create-contract${self:custom.dotenv.VERSION}
  description: Função para realizar a criação de um contrato.
  memorySize: 128
  events:
    - http:
        path: /contracts/create
        method: post
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}


