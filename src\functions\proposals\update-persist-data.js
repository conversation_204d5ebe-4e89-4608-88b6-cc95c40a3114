import {
    STATUS_CODE,
    responseWithError,
    responseWithSuccess,
    responseWithBadRequest,
  } from "../../shared/response";
  
  import { errorValidator } from "../../shared/validators";
  
  import { parseBody } from "../../shared/parsers";
  import { create, putNo<PERSON>ey, readOne } from '../../model/dynamo'
  
const TABLE = `${process.env.FINOPS_STAGE}-proposal-persist`

  exports.handler = async (event) => {
    try {
      const body = parseBody(event);

      const id = body.id

      let persistData = null
      try {
        persistData = await readOne(TABLE, id)
        console.log(`Updating persist data for proposal persist id: ${id}`)
        
        persistData = await putNoKey(TABLE, body) 

      } catch (error) {
        console.log(`Register not found with id ${id}`)
        console.log(`Creating persist data for proposal persist id: ${id}`)

        persistData = await create(TABLE, body) 

      }

      return responseWithSuccess(persistData, "Issue created successfully");

    } catch (error) {
      console.log("error", error);
      const { statusCode, message } = errorValidator(error);
      if (STATUS_CODE.BAD_REQUEST === statusCode)
        return responseWithBadRequest(message);
  
      return responseWithError(error);
    }
  };
  