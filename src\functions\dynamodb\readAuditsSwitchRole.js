import { readAuditsByDateRange } from "../../model/dynamo";
import { parseQueryString } from "../../shared/parsers";
import { json, message } from "../../shared/response";

async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

export const handler = async (event, context) => {
  const { startDate, endDate, type, searchParam } = parseQueryString(event);

  try {
    return await sendDataToUser(
      200,
      "success",
      await readAuditsByDateRange({ startDate, endDate, type, searchParam })
    );
  } catch (error) {
    console.log(error);
    return await sendDataToUser(500, "error", error);
  }
};