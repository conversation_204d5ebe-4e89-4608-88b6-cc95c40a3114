const jwt = require('jsonwebtoken');
const { getJWTDecryptionCredentials } = require('../services/secreats-manager-service');

const getJWTConfig = async () => {
  const credentials = await getJWTDecryptionCredentials();
  return {
    secret: credentials.JWT_SECRET,
    expiresIn: credentials.JWT_EXPIRES_IN || '8h',
    refreshExpiresIn: credentials.JWT_REFRESH_EXPIRES_IN || '7d'
  };
};

const generateToken = async (payload, expiresIn = null) => {
  try {
    const config = await getJWTConfig();
    return jwt.sign(payload, config.secret, {
      expiresIn: expiresIn || config.expiresIn,
      issuer: 'dsm-backend',
      audience: 'dsm-frontend'
    });
  } catch (error) {
    console.error('Erro ao gerar token:', error);
    throw new Error('Falha ao gerar token de autenticação');
  }
};

/**
 * Gera um refresh token
 * @param {Object} payload - Dados do usuário para incluir no token
 * @returns {string} Refresh token JWT
 */
const generateRefreshToken = async (payload) => {
  try {
    const config = await getJWTConfig();
    return jwt.sign(
      { ...payload, type: 'refresh' },
      config.secret,
      {
        expiresIn: config.refreshExpiresIn,
        issuer: 'dsm-backend',
        audience: 'dsm-frontend'
      }
    );
  } catch (error) {
    console.error('Erro ao gerar refresh token:', error);
    throw new Error('Falha ao gerar refresh token');
  }
};

/**
 * Verifica e decodifica um token JWT
 * @param {string} token - Token JWT para verificar
 * @returns {Object} Payload decodificado do token
 */
const verifyToken = async (token) => {
  try {
    const config = await getJWTConfig();
    return jwt.verify(token, config.secret, {
      issuer: 'dsm-backend',
      audience: 'dsm-frontend'
    });
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new Error('Token expirado');
    } else if (error.name === 'JsonWebTokenError') {
      throw new Error('Token inválido');
    } else {
      console.error('Erro ao verificar token:', error);
      throw new Error('Falha na verificação do token');
    }
  }
};

/**
 * Verifica se um token está próximo do vencimento
 * @param {string} token - Token JWT para verificar
 * @param {number} thresholdMinutes - Minutos antes do vencimento para considerar "próximo"
 * @returns {boolean} True se o token está próximo do vencimento
 */
const isTokenNearExpiry = (token, thresholdMinutes = 30) => {
  try {
    const decoded = jwt.decode(token);
    if (!decoded || !decoded.exp) return true;
    
    const now = Math.floor(Date.now() / 1000);
    const timeUntilExpiry = decoded.exp - now;
    const thresholdSeconds = thresholdMinutes * 60;
    
    return timeUntilExpiry <= thresholdSeconds;
  } catch (error) {
    console.error('Erro ao verificar proximidade de expiração:', error);
    return true; 
  }
};

/**
 * Extrai informações básicas do token sem verificar assinatura
 * @param {string} token - Token JWT
 * @returns {Object|null} Payload decodificado ou null se inválido
 */
const decodeToken = (token) => {
  try {
    return jwt.decode(token);
  } catch (error) {
    console.error('Erro ao decodificar token:', error);
    return null;
  }
};

/**
 * Verifica se um token é um refresh token válido
 * @param {string} token - Token para verificar
 * @returns {boolean} True se é um refresh token válido
 */
const isValidRefreshToken = async (token) => {
  try {
    const decoded = await verifyToken(token);
    return decoded.type === 'refresh';
  } catch (error) {
    return false;
  }
};

module.exports = {
  generateToken,
  generateRefreshToken,
  verifyToken,
  isTokenNearExpiry,
  decodeToken,
  isValidRefreshToken
};


