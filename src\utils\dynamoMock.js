/**
 * DynamoDB Mock for Local Development
 * Returns sample data for development purposes
 */

const mockData = {
  'dev-permissions': {
    'mock-user-123': {
      id: 'mock-user-123',
      email: '<EMAIL>',
      role: 'admin',
      permissions: ['read', 'write', 'admin'],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  },
  'dev-customers': [
    {
      id: 'customer-1',
      name: 'Cliente Exemplo 1',
      email: '<EMAIL>',
      status: 1,
      hasActiveContracts: true,
      createdAt: new Date().toISOString()
    },
    {
      id: 'customer-2', 
      name: 'Cliente Exemplo 2',
      email: '<EMAIL>',
      status: 1,
      hasActiveContracts: false,
      createdAt: new Date().toISOString()
    }
  ],
  'dev-contracts': [
    {
      id: 'contract-1',
      customerId: 'customer-1',
      status: 'active',
      value: 1000.00,
      startDate: new Date().toISOString(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
    }
  ]
};

/**
 * Mock DynamoDB operations for local development
 */
export const dynamoMock = {
  /**
   * Mock GetItem operation
   */
  getItem: (params) => {
    console.log('🔧 DynamoDB Mock - GetItem:', params);
    
    const { TableName, Key } = params;
    const table = mockData[TableName];
    
    if (!table) {
      return Promise.resolve({ Item: null });
    }
    
    // For permissions table, use the id from Key
    if (TableName === 'dev-permissions') {
      const id = Key.id?.S || Key.id;
      const item = table[id];
      return Promise.resolve({ 
        Item: item ? convertToAttributeValue(item) : null 
      });
    }
    
    return Promise.resolve({ Item: null });
  },

  /**
   * Mock Query operation
   */
  query: (params) => {
    console.log('🔧 DynamoDB Mock - Query:', params);
    
    const { TableName } = params;
    const table = mockData[TableName];
    
    if (!table) {
      return Promise.resolve({ Items: [], Count: 0 });
    }
    
    // For customers table, return filtered results
    if (TableName === 'dev-customers') {
      const items = Array.isArray(table) ? table : Object.values(table);
      return Promise.resolve({
        Items: items.map(convertToAttributeValue),
        Count: items.length
      });
    }
    
    return Promise.resolve({ Items: [], Count: 0 });
  },

  /**
   * Mock Scan operation
   */
  scan: (params) => {
    console.log('🔧 DynamoDB Mock - Scan:', params);
    
    const { TableName } = params;
    const table = mockData[TableName];
    
    if (!table) {
      return Promise.resolve({ Items: [], Count: 0 });
    }
    
    const items = Array.isArray(table) ? table : Object.values(table);
    return Promise.resolve({
      Items: items.map(convertToAttributeValue),
      Count: items.length
    });
  }
};

/**
 * Convert plain object to DynamoDB AttributeValue format
 */
function convertToAttributeValue(obj) {
  const result = {};
  
  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'string') {
      result[key] = { S: value };
    } else if (typeof value === 'number') {
      result[key] = { N: value.toString() };
    } else if (typeof value === 'boolean') {
      result[key] = { BOOL: value };
    } else if (Array.isArray(value)) {
      result[key] = { SS: value.map(String) };
    } else if (value === null || value === undefined) {
      result[key] = { NULL: true };
    } else {
      result[key] = { S: JSON.stringify(value) };
    }
  }
  
  return result;
}

/**
 * Check if we're in local development mode
 */
export const isLocalDevelopment = () => {
  return process.env.IS_OFFLINE === 'true' || 
         process.env.NODE_ENV === 'development' ||
         process.env.STAGE === 'local';
};
