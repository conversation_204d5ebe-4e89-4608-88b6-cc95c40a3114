/**
 * Script para testar as correções de CORS
 * Testa especificamente o subdomínio dev.dsm.darede.com.br
 */

const axios = require('axios');

// URL base da API
const BASE_URL = 'https://pdu9dbemj1.execute-api.us-east-1.amazonaws.com/dev';

// Origens para testar
const TEST_ORIGINS = [
  'https://dev.dsm.darede.com.br',
  'https://dsm.darede.com.br',
  'http://localhost:3000',
  'https://api.dsm.darede.com.br'
];

// Endpoints para testar
const TEST_ENDPOINTS = [
  { path: '/test/cors', method: 'GET', description: 'Endpoint de teste CORS' },
  { path: '/auth/config', method: 'GET', description: 'Configuração de autenticação' },
  { path: '/read/id/user', method: 'GET', description: 'Endpoint que estava falhando', headers: { 'DynamoDB': 'dev-permissions' } }
];

/**
 * Faz uma requisição com origem específica
 */
async function makeRequestWithOrigin(method, path, origin, extraHeaders = {}) {
  try {
    const config = {
      method: method.toLowerCase(),
      url: `${BASE_URL}${path}`,
      headers: {
        'Origin': origin,
        'Content-Type': 'application/json',
        ...extraHeaders
      },
      validateStatus: () => true, // Não rejeitar por status HTTP
      timeout: 10000
    };

    console.log(`🔍 Testando ${method} ${path} com origem ${origin}`);
    const response = await axios(config);
    
    return {
      status: response.status,
      headers: response.headers,
      data: response.data,
      corsHeaders: {
        'access-control-allow-origin': response.headers['access-control-allow-origin'],
        'access-control-allow-credentials': response.headers['access-control-allow-credentials'],
        'access-control-allow-methods': response.headers['access-control-allow-methods'],
        'access-control-allow-headers': response.headers['access-control-allow-headers']
      }
    };
  } catch (error) {
    return {
      status: 0,
      error: error.message,
      headers: {},
      data: null,
      corsHeaders: {}
    };
  }
}

/**
 * Testa preflight (OPTIONS)
 */
async function testPreflight(path, origin) {
  try {
    const config = {
      method: 'options',
      url: `${BASE_URL}${path}`,
      headers: {
        'Origin': origin,
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type,DynamoDB'
      },
      validateStatus: () => true,
      timeout: 10000
    };

    console.log(`🔍 Testando preflight OPTIONS ${path} com origem ${origin}`);
    const response = await axios(config);
    
    return {
      status: response.status,
      corsHeaders: {
        'access-control-allow-origin': response.headers['access-control-allow-origin'],
        'access-control-allow-credentials': response.headers['access-control-allow-credentials'],
        'access-control-allow-methods': response.headers['access-control-allow-methods'],
        'access-control-allow-headers': response.headers['access-control-allow-headers']
      }
    };
  } catch (error) {
    return {
      status: 0,
      error: error.message,
      corsHeaders: {}
    };
  }
}

/**
 * Executa todos os testes
 */
async function runTests() {
  console.log('🚀 Iniciando testes de CORS após correções...\n');
  
  for (const origin of TEST_ORIGINS) {
    console.log(`\n📍 Testando origem: ${origin}`);
    console.log('='.repeat(50));
    
    for (const endpoint of TEST_ENDPOINTS) {
      console.log(`\n🔧 ${endpoint.description}`);
      
      // Teste preflight primeiro
      const preflightResult = await testPreflight(endpoint.path, origin);
      console.log(`   Preflight: ${preflightResult.status === 200 ? '✅' : '❌'} Status ${preflightResult.status}`);
      
      if (preflightResult.corsHeaders['access-control-allow-origin']) {
        console.log(`   CORS Origin: ${preflightResult.corsHeaders['access-control-allow-origin']}`);
      }
      
      // Teste requisição real
      const result = await makeRequestWithOrigin(
        endpoint.method, 
        endpoint.path, 
        origin, 
        endpoint.headers || {}
      );
      
      console.log(`   Requisição: ${result.status < 400 ? '✅' : '❌'} Status ${result.status}`);
      
      if (result.error) {
        console.log(`   Erro: ${result.error}`);
      }
      
      if (result.data && result.data.error) {
        console.log(`   Erro API: ${result.data.message || result.data.error}`);
      }
    }
  }
  
  console.log('\n🏁 Testes concluídos!');
}

// Executa os testes
runTests().catch(console.error);
