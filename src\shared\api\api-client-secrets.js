/**
 * Cliente de API usando credenciais do AWS Secrets Manager
 * Implementa autenticação segura e cache de credenciais
 */

const axios = require('axios');
const { getAPICredentials } = require('../secrets/secrets-manager');

// Cache para credenciais de API
let apiCredentialsCache = null;
let cacheExpiry = 0;

/**
 * Obtém credenciais de API do Secrets Manager com cache
 * @returns {Promise<Object>} Credenciais de API
 */
async function getAPIConfig() {
  const now = Date.now();
  
  // Verifica se o cache ainda é válido (10 minutos)
  if (apiCredentialsCache && now < cacheExpiry) {
    return apiCredentialsCache;
  }

  try {
    console.log('Carregando credenciais de API do Secrets Manager...');
    apiCredentialsCache = await getAPICredentials();
    cacheExpiry = now + (10 * 60 * 1000); // Cache por 10 minutos
    console.log('Credenciais de API carregadas com sucesso');
    return apiCredentialsCache;
  } catch (error) {
    console.error('Erro ao carregar credenciais de API:', error);
    
    // Fallback para variáveis de ambiente se Secrets Manager falhar
    if (process.env.DSM_API_URL) {
      console.warn('Usando fallback para credenciais de API das variáveis de ambiente');
      return {
        username: process.env.DSM_API_USERNAME || '',
        password: process.env.DSM_API_PASSWORD || '',
        apiKey: process.env.DSM_API_KEY || '',
        baseUrl: process.env.DSM_API_URL,
        timeout: 30000
      };
    }
    
    throw new Error('Não foi possível obter credenciais de API do Secrets Manager nem das variáveis de ambiente');
  }
}

/**
 * Cria uma instância do Axios configurada com credenciais do Secrets Manager
 * @param {Object} options - Opções adicionais para o cliente
 * @returns {Promise<Object>} Instância configurada do Axios
 */
async function createAPIClient(options = {}) {
  try {
    const config = await getAPIConfig();
    
    const clientConfig = {
      baseURL: config.baseUrl,
      timeout: config.timeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'DSM-Backend/1.0',
        ...options.headers
      },
      ...options
    };

    // Adiciona autenticação baseada no tipo disponível
    if (config.apiKey) {
      clientConfig.headers['X-API-Key'] = config.apiKey;
      console.log('Cliente API configurado com API Key');
    } else if (config.username && config.password) {
      clientConfig.auth = {
        username: config.username,
        password: config.password
      };
      console.log('Cliente API configurado com Basic Auth');
    }

    const client = axios.create(clientConfig);

    // Interceptor para logs de requisições
    client.interceptors.request.use(
      (config) => {
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('Erro na requisição API:', error);
        return Promise.reject(error);
      }
    );

    // Interceptor para logs de respostas
    client.interceptors.response.use(
      (response) => {
        console.log(`API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        if (error.response) {
          console.error(`API Error: ${error.response.status} ${error.response.config.url}`);
          console.error('Error data:', error.response.data);
        } else if (error.request) {
          console.error('API Network Error:', error.message);
        } else {
          console.error('API Setup Error:', error.message);
        }
        return Promise.reject(error);
      }
    );

    return client;

  } catch (error) {
    console.error('Erro ao criar cliente API:', error);
    throw error;
  }
}

/**
 * Faz uma requisição GET autenticada
 * @param {string} endpoint - Endpoint da API
 * @param {Object} params - Parâmetros da query
 * @param {Object} options - Opções adicionais
 * @returns {Promise<Object>} Resposta da API
 */
async function apiGet(endpoint, params = {}, options = {}) {
  try {
    const client = await createAPIClient(options);
    const response = await client.get(endpoint, { params });
    return response.data;
  } catch (error) {
    console.error(`Erro na requisição GET ${endpoint}:`, error.message);
    throw error;
  }
}

/**
 * Faz uma requisição POST autenticada
 * @param {string} endpoint - Endpoint da API
 * @param {Object} data - Dados para enviar
 * @param {Object} options - Opções adicionais
 * @returns {Promise<Object>} Resposta da API
 */
async function apiPost(endpoint, data = {}, options = {}) {
  try {
    const client = await createAPIClient(options);
    const response = await client.post(endpoint, data);
    return response.data;
  } catch (error) {
    console.error(`Erro na requisição POST ${endpoint}:`, error.message);
    throw error;
  }
}

/**
 * Faz uma requisição PUT autenticada
 * @param {string} endpoint - Endpoint da API
 * @param {Object} data - Dados para enviar
 * @param {Object} options - Opções adicionais
 * @returns {Promise<Object>} Resposta da API
 */
async function apiPut(endpoint, data = {}, options = {}) {
  try {
    const client = await createAPIClient(options);
    const response = await client.put(endpoint, data);
    return response.data;
  } catch (error) {
    console.error(`Erro na requisição PUT ${endpoint}:`, error.message);
    throw error;
  }
}

/**
 * Faz uma requisição DELETE autenticada
 * @param {string} endpoint - Endpoint da API
 * @param {Object} options - Opções adicionais
 * @returns {Promise<Object>} Resposta da API
 */
async function apiDelete(endpoint, options = {}) {
  try {
    const client = await createAPIClient(options);
    const response = await client.delete(endpoint);
    return response.data;
  } catch (error) {
    console.error(`Erro na requisição DELETE ${endpoint}:`, error.message);
    throw error;
  }
}

/**
 * Testa a conectividade com a API
 * @returns {Promise<Object>} Resultado do teste
 */
async function testAPIConnection() {
  try {
    const config = await getAPIConfig();
    const client = await createAPIClient({ timeout: 5000 });
    
    // Tenta fazer uma requisição simples (ajuste o endpoint conforme necessário)
    const response = await client.get('/health', { 
      validateStatus: () => true // Aceita qualquer status
    });
    
    return {
      success: true,
      status: response.status,
      baseUrl: config.baseUrl,
      responseTime: response.headers['x-response-time'] || 'N/A',
      timestamp: new Date().toISOString()
    };
    
  } catch (error) {
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Limpa o cache de credenciais de API
 */
function clearAPICache() {
  apiCredentialsCache = null;
  cacheExpiry = 0;
  console.log('Cache de credenciais de API limpo');
}

/**
 * Obtém estatísticas do cache de API
 * @returns {Object} Estatísticas do cache
 */
function getAPICacheStats() {
  return {
    cached: !!apiCredentialsCache,
    expiresAt: cacheExpiry ? new Date(cacheExpiry).toISOString() : null,
    isValid: apiCredentialsCache && Date.now() < cacheExpiry
  };
}

/**
 * Wrapper para requisições com retry automático
 * @param {Function} apiFunction - Função de API para executar
 * @param {Array} args - Argumentos para a função
 * @param {number} maxRetries - Número máximo de tentativas
 * @returns {Promise<Object>} Resultado da requisição
 */
async function withRetry(apiFunction, args, maxRetries = 3) {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await apiFunction(...args);
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        break;
      }
      
      // Limpa cache em caso de erro de autenticação
      if (error.response?.status === 401 || error.response?.status === 403) {
        console.log('Erro de autenticação detectado, limpando cache...');
        clearAPICache();
      }
      
      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
      console.log(`Tentativa ${attempt} falhou, tentando novamente em ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
}

module.exports = {
  createAPIClient,
  apiGet,
  apiPost,
  apiPut,
  apiDelete,
  testAPIConnection,
  clearAPICache,
  getAPICacheStats,
  withRetry,
  getAPIConfig
};
