import aws from "aws-sdk";
import AthenaExpress from "athena-express";
import { responseWithSuccess, responseWithError } from "../../shared/response";

export const handler = async (event, context) => {
  try {
    const athena = new AthenaExpress({
      aws,
      db: "default",
      s3: `${process.env.BUCKET_REASONS_TO_DEACTIVATE_CONTRACT}`,
    });

    const result = await athena.query(
      `SELECT * FROM ${process.env.TABLE_REASONS_TO_DEACTIVATE_CONTRACT}`
    );

    return responseWithSuccess(result, "Query successfully");
  } catch (error) {
    console.log(error);
    return responseWithError(error);
  }
};
