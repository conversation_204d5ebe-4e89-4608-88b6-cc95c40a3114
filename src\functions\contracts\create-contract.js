import { ContractEntity } from "../../entities/contract-entity";
import { parseBody, parseQueryString } from "../../shared/parsers";
import * as Yup from "yup";

import {
  STATUS_CODE,
  responseWithError,
  responseWithSuccess,
  responseWithBadRequest,
  responseWithNotFound,
} from "../../shared/response";

import { errorValidator } from "../../shared/validators";

import { makeDynamoDB } from "../../shared/services/dynamo-service";
import { create } from "../../model/dynamo";
import { v4 } from "uuid";
import { StepFunctions } from "aws-sdk";

const stepFunctions = new StepFunctions({
  region: process.env.AWS_REGION_LOCATION,
});

export const handler = async (event, context) => {
  try {
    const body = parseBody(event);
    const { name } = body;

    const caseInsentiveCheck = [name, name.toLowerCase(), name.toUpperCase()];
    body["id"] = v4();
    const clientDynamo = makeDynamoDB();
    const dbEntity = new ContractEntity(clientDynamo);
    for (let i = 0; i < caseInsentiveCheck.length; i++) {
      const inputGetContracts = dbEntity.generateInputGetContractByName({
        name: caseInsentiveCheck[i],
      });
      const contracts = await dbEntity.getContracts(inputGetContracts);
      if (contracts.length > 0)
        return responseWithBadRequest(
          "A contract with this name already exists"
        );
    }

    const createContractResponse = await create(
      `${process.env.FINOPS_STAGE}-contracts`,
      body
    );

    try {
      console.log(
        `Sending register event - table: ${process.env.FINOPS_STAGE}-contracts - id: ${body.id}`
      );
      await stepFunctions
        .startExecution({
          stateMachineArn: process.env.CREATE_EVENT_STEP_FUNCTION,
          input: JSON.stringify({
            eventType: "CREATE",
            tableName: `${process.env.FINOPS_STAGE}-contracts`,
            item: body,
          }),
        })
        .promise();
    } catch (error) {
      console.log(
        `Could not send register event - table: ${process.env.FINOPS_STAGE}-contracts - id: ${body.id}`
      );
      console.log(error);
    }

    return responseWithSuccess(createContractResponse, "Query successfully");
  } catch (error) {
    console.log(error);
    return responseWithError(error);
  }
};
