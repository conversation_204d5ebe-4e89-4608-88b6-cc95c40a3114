import { SES } from "aws-sdk";
import nodemailer from "nodemailer";

export default class Email {
  constructor(from, to, subject, html, { cc, bcc, attachments } = {}) {
    const ses = new SES({
      region: process.env.AWS_REGION_LOCATION,
      credentials: {
        accessKeyId: process.env.ACCESS_KEY_ID_USER_ROOT,
        secretAccessKey: process.env.SECRET_KEY_ID_USER_ROOT,
      },
    });

    this.transporter = nodemailer.createTransport({ SES: ses });
    this.from = from;
    this.to = to;
    this.cc = cc;
    this.bcc = bcc;
    this.subject = subject;
    this.html = html;
    this.attachments = attachments;
  }

  async send() {
    const message = {
      from: this.from,
      to: this.to,
      subject: this.subject,
      html: this.html,
    };

    if (this.cc != undefined) {
      message.cc = this.cc;
    }

    if (this.bcc != undefined) {
      message.bcc = this.bcc;
    }

    if (this.attachments != undefined) {
      message.attachments = this.attachments;
    }

    const response = await new Promise((rsv, rjt) => {
      this.transporter.sendMail(message, function (error, info) {
        if (error) {
          return rjt(error)
        }
        rsv('Email enviado');
      });
    });

    return response;
  }
}