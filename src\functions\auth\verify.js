import { json, message } from '../../shared/response';
import { enhancedSecretsManager } from '../../shared/services/enhanced-secrets-manager';
import { getCorsMiddleware } from '../../middleware/corsMiddleware';
import jwt from 'jsonwebtoken';

/**
 * Extract token from cookies
 */
function extractTokenFromCookies(event) {
  const cookies = event.headers?.Cookie || event.headers?.cookie || '';
  const cookieArray = cookies.split(';');
  
  for (const cookie of cookieArray) {
    const [name, value] = cookie.trim().split('=');
    if (name === 'dsm_auth_token') {
      return value;
    }
  }
  
  return null;
}

/**
 * Verify JWT token
 */
async function verifyToken(token) {
  try {
    const secrets = await enhancedSecretsManager.getJWTSecrets();
    const decoded = jwt.verify(token, secrets.JWT_SECRET);
    
    // Check if token is expired
    const currentTime = Math.floor(Date.now() / 1000);
    if (decoded.exp < currentTime) {
      throw new Error('Token expired');
    }
    
    return decoded;
  } catch (error) {
    console.error('Token verification failed:', error);
    throw new Error('Invalid token');
  }
}

/**
 * Handler to verify authentication
 */
const verifyHandler = async (event, context) => {
  try {
    // Extract token from cookies
    const token = extractTokenFromCookies(event);
    
    if (!token) {
      return await json(await message('success', { authenticated: false, reason: 'No token found' }), 200);
    }
    
    // Verify token
    const decoded = await verifyToken(token);
    
    // Return authentication status with user info
    const response = await json(await message('success', {
      authenticated: true,
      user: {
        sub: decoded.sub,
        email: decoded.email,
        userInfo: decoded.userInfo,
        exp: decoded.exp,
        iat: decoded.iat
      }
    }), 200);

    return response;
    
  } catch (error) {
    console.error('Authentication verification error:', error);
    
    const response = await json(await message('success', {
      authenticated: false,
      reason: error.message
    }), 200);

    return response;
  }
};

// Apply CORS middleware
export const handler = getCorsMiddleware('auth')(verifyHandler);
