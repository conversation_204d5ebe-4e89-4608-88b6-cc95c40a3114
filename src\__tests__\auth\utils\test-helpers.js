/**
 * Helpers e utilitários para testes de autenticação
 */

const http = require('http');
const { generateToken, verifyToken } = require('../../../shared/auth/jwt-utils-cjs');

// Configuração padrão para testes
const TEST_CONFIG = {
  baseUrl: 'http://localhost:8000/dev',
  defaultUser: {
    sub: 'test-user-123',
    email: '<EMAIL>',
    role: 'admin',
    name: 'Test User',
    permissions: ['users:read', 'users:write']
  }
};

/**
 * Classe para gerenciar sessão de teste com cookies
 */
class TestSession {
  constructor() {
    this.cookies = '';
  }

  /**
   * Faz uma requisição HTTP mantendo cookies da sessão
   */
  async makeRequest(method, path, data = null, headers = {}) {
    return new Promise((resolve, reject) => {
      const options = {
        hostname: 'localhost',
        port: 8000,
        path: `/dev${path}`,
        method: method,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        }
      };

      if (this.cookies) {
        options.headers['Cookie'] = this.cookies;
      }

      const req = http.request(options, (res) => {
        let responseData = '';
        
        res.on('data', (chunk) => {
          responseData += chunk;
        });
        
        res.on('end', () => {
          // Atualiza cookies da sessão
          if (res.headers['set-cookie']) {
            this.cookies = res.headers['set-cookie'].join('; ');
          }
          
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: responseData,
            json: () => {
              try {
                return JSON.parse(responseData);
              } catch (error) {
                return null;
              }
            }
          });
        });
      });

      req.on('error', (err) => {
        reject(err);
      });

      if (data) {
        req.write(JSON.stringify(data));
      }
      
      req.end();
    });
  }

  /**
   * Limpa cookies da sessão
   */
  clearCookies() {
    this.cookies = '';
  }

  /**
   * Obtém cookies atuais da sessão
   */
  getCookies() {
    return this.cookies;
  }
}

/**
 * Cria um token JWT válido para testes
 */
function createTestToken(userData = {}) {
  const user = { ...TEST_CONFIG.defaultUser, ...userData };
  return generateToken(user);
}

/**
 * Cria um token JWT inválido para testes
 */
function createInvalidToken() {
  return 'invalid.jwt.token';
}

/**
 * Cria um token JWT expirado para testes
 */
function createExpiredToken(userData = {}) {
  const user = { ...TEST_CONFIG.defaultUser, ...userData };
  // Gera token com expiração de 1 segundo no passado
  return generateToken(user, '-1s');
}

/**
 * Verifica se o servidor está rodando
 */
async function checkServerRunning() {
  const session = new TestSession();
  try {
    const response = await session.makeRequest('GET', '/auth/verify');
    return true;
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      return false;
    }
    return true; 
  }
}

/**
 * Faz login completo e retorna sessão autenticada
 */
async function createAuthenticatedSession(userData = {}) {
  const session = new TestSession();
  const token = createTestToken(userData);
  
  const response = await session.makeRequest('POST', '/auth/set-token', { token });
  
  if (response.statusCode !== 200) {
    throw new Error(`Failed to authenticate: ${response.body}`);
  }
  
  return session;
}

/**
 * Matchers customizados para Jest
 */
const customMatchers = {
  /**
   * Verifica se a resposta contém cookies HttpOnly válidos
   */
  toHaveValidAuthCookies(received) {
    const setCookies = received.headers['set-cookie'];
    
    if (!setCookies) {
      return {
        message: () => 'Expected response to have Set-Cookie headers',
        pass: false
      };
    }
    
    const cookies = Array.isArray(setCookies) ? setCookies : [setCookies];
    
    const hasAccessToken = cookies.some(cookie => 
      cookie.includes('dsm_access_token') && 
      cookie.includes('HttpOnly') &&
      cookie.includes('SameSite=lax')
    );
    
    const hasRefreshToken = cookies.some(cookie => 
      cookie.includes('dsm_refresh_token') && 
      cookie.includes('HttpOnly') &&
      cookie.includes('SameSite=lax')
    );
    
    if (hasAccessToken && hasRefreshToken) {
      return {
        message: () => 'Expected response not to have valid auth cookies',
        pass: true
      };
    }
    
    return {
      message: () => 'Expected response to have valid auth cookies (access and refresh tokens with HttpOnly and SameSite flags)',
      pass: false
    };
  },

  /**
   * Verifica se a resposta contém cookies de logout (Max-Age=0)
   */
  toHaveLogoutCookies(received) {
    const setCookies = received.headers['set-cookie'];
    
    if (!setCookies) {
      return {
        message: () => 'Expected response to have Set-Cookie headers for logout',
        pass: false
      };
    }
    
    const cookies = Array.isArray(setCookies) ? setCookies : [setCookies];
    
    const hasLogoutCookies = cookies.every(cookie => 
      cookie.includes('Max-Age=0') || cookie.includes('Expires=Thu, 01 Jan 1970')
    );
    
    if (hasLogoutCookies) {
      return {
        message: () => 'Expected response not to have logout cookies',
        pass: true
      };
    }
    
    return {
      message: () => 'Expected response to have logout cookies (Max-Age=0)',
      pass: false
    };
  }
};

/**
 * Setup para testes de integração
 */
function setupAuthTests() {
  // Configurar variáveis de ambiente para testes
  process.env.JWT_SECRET = 'test-secret-key-for-testing-purposes-min-32-chars';
  process.env.JWT_EXPIRES_IN = '8h';
  process.env.JWT_REFRESH_EXPIRES_IN = '7d';
  process.env.ALLOWED_ORIGINS = 'http://localhost:3000';
  process.env.NODE_ENV = 'test';
  
  // Adicionar matchers customizados ao Jest se disponível
  if (typeof expect !== 'undefined' && expect.extend) {
    expect.extend(customMatchers);
  }
}

module.exports = {
  TestSession,
  TEST_CONFIG,
  createTestToken,
  createInvalidToken,
  createExpiredToken,
  checkServerRunning,
  createAuthenticatedSession,
  customMatchers,
  setupAuthTests
};
