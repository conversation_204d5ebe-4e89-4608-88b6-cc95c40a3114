{"name": "dynamodb-back-end", "version": "1.0.0", "description": "API de comunicação com o DynamoDB", "main": "index.js", "scripts": {"prepare": "husky install", "test": "npx jest --only<PERSON><PERSON>ed", "test:auth": "npx jest src/__tests__/auth --verbose", "test:auth:integration": "npx jest src/__tests__/auth/integration --verbose", "test:auth:manual": "node scripts/test-auth-local.js", "test:cookie:detection": "node scripts/test-cookie-detection.js", "test:production": "node scripts/test-production-endpoints.js", "test:cognito:conversion": "node scripts/test-cognito-conversion.js", "test:secure:auth": "node scripts/test-secure-auth-system.js", "test:cors": "node scripts/test-cors-configuration.js", "test:secrets": "node scripts/test-secrets-manager.js", "test:set-token": "node scripts/test-set-token-api.js", "test:jwt-consistency": "node scripts/test-jwt-consistency.js", "check:jwt-secret": "node scripts/check-jwt-secret-structure.js", "migrate:secrets": "node scripts/migrate-to-secrets-manager.js", "migrate:secrets:apply": "node scripts/migrate-to-secrets-manager.js --no-dry-run", "examples:secrets": "node examples/secrets-manager-usage.js", "dev": "cross-env DISABLE_WEBPACK_BAR=true serverless offline --stage local", "dev:simple": "serverless offline --stage local --verbose", "start": "npm run dev", "start:local": "npm run dev", "build": "serverless package --stage local", "build:dev": "serverless package --stage dev", "build:hml": "serverless package --stage hml", "build:prod": "serverless package --stage prod", "local": "serverless offline --stage dev", "deploy": "serverless deploy --stage $STAGE --force --verbose", "deploy:dev": "serverless deploy --stage dev --verbose", "deploy:hml": "serverless deploy --stage hml --verbose", "deploy:prod": "serverless deploy --stage prod --verbose"}, "author": "Dosystems", "license": "ISC", "devDependencies": {"@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "@types/jest": "^29.5.8", "@types/uuid": "^9.0.7", "athena-express": "^7.1.5", "babel-loader": "^9.1.3", "corejs": "^1.0.0", "cross-env": "^7.0.3", "glob": "^10.3.10", "husky": "^8.0.3", "jest": "^29.7.0", "jest-extended": "^4.0.2", "noop2": "^2.0.0", "serverless": "^3.38.0", "serverless-layers": "2.5.1", "serverless-offline": "^13.3.0", "serverless-plugin-split-stacks": "^1.14.0", "serverless-webpack": "^5.15.1", "webpack": "^5.89.0", "webpack-node-externals": "^3.0.0", "webpackbar": "^7.0.0"}, "dependencies": {"athena-express": "^7.1.5", "aws-jwt-verify": "^5.1.0", "aws-multipart-parser": "^0.2.1", "aws-sdk": "^2.839.0", "axios": "^0.21.4", "cookie-parser": "^1.4.7", "date-fns": "^2.29.1", "dotenv": "^10.0.0", "excel4node": "^1.8.0", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.7.8", "pdfmake": "^0.2.6", "serverless-apigw-binary": "^0.4.4", "uuid": "^8.3.2", "yamljs": "^0.3.0", "yup": "^0.32.11"}}