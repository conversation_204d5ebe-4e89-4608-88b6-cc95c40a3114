import { message, json } from '../../shared/response'
import { CognitoIdentityServiceProvider, SecretsManager } from 'aws-sdk'
import { parseBody } from '../../shared/parsers'

const cognito = new CognitoIdentityServiceProvider({
  region: process.env.AWS_REGION_LOCATION
})

const secrets = new SecretsManager({
  region: process.env.AWS_REGION_LOCATION
})

async function sendDataToUser (status, statusString, data) {
  const msg = await message(statusString, data)
  return await json(msg, status)
}

export const handler = async (event, context) => {
  console.log('teste')
  // try {
  const body = parseBody(event)

  let userIntegrationKeys = await secrets.getSecretValue({
    SecretId: body.arnSecret
  }).promise()

  userIntegrationKeys = JSON.parse(userIntegrationKeys.SecretString)

  const { AuthenticationResult: { IdToken } } = await cognito.initiateAuth({
    ClientId: userIntegrationKeys.cognitoClient,
    AuthFlow: 'USER_PASSWORD_AUTH',
    AuthParameters: {
      USERNAME: userIntegrationKeys.user,
      PASSWORD: userIntegrationKeys.password
    }
  }).promise()

  return await sendDataToUser(200, 'success', IdToken)
  // } catch (error) {
  //   console.log(error)
  //   return await sendDataToUser(500, 'error', error)
  // }
}
