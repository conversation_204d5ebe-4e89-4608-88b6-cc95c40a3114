/**
 * Script para testar detecção de suporte a cookies HttpOnly
 * Simula o comportamento do frontend para verificar se cookies são suportados
 */

const http = require('http');

const BASE_URL = 'http://localhost:8000';
const API_PATH = '/dev';

let cookies = '';

console.log('🍪 Testando Detecção de Suporte a Cookies HttpOnly\n');

// Função helper para fazer requisições HTTP
function makeRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 8000,
      path: `${API_PATH}${path}`,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (cookies) {
      options.headers['Cookie'] = cookies;
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        // Captura cookies da resposta
        if (res.headers['set-cookie']) {
          const newCookies = res.headers['set-cookie'];
          console.log(`   📥 Cookies recebidos: ${newCookies.length} cookie(s)`);
          
          // Simula comportamento do browser - armazena cookies
          cookies = newCookies.map(cookie => cookie.split(';')[0]).join('; ');
        }
        
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: responseData ? JSON.parse(responseData) : null
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function testCookieDetection() {
  try {
    console.log('1️⃣ Verificando configurações de autenticação...');
    const configResponse = await makeRequest('GET', '/auth/config');
    
    if (configResponse.statusCode === 200) {
      console.log('   ✅ Configurações obtidas');
      console.log(`   🔧 httpOnlySupported: ${configResponse.data.auth.httpOnlySupported}`);
      console.log(`   🔧 withCredentials: ${configResponse.data.auth.withCredentials}`);
      console.log(`   🔧 tokenStorage: ${configResponse.data.auth.tokenStorage}`);
    } else {
      console.log(`   ❌ Erro ao obter configurações: ${configResponse.statusCode}`);
    }
    
    console.log('\n2️⃣ Testando suporte a cookies...');
    const cookieTestResponse = await makeRequest('GET', '/auth/check-cookie-support');
    
    if (cookieTestResponse.statusCode === 200) {
      console.log('   ✅ Teste de cookie executado');
      console.log(`   🍪 httpOnlySupported: ${cookieTestResponse.data.httpOnlySupported}`);
      console.log(`   🍪 cookieTestSet: ${cookieTestResponse.data.cookieTestSet}`);
      
      // Verifica se cookie foi realmente definido
      const hasCookies = cookies.length > 0;
      console.log(`   📋 Cookies armazenados localmente: ${hasCookies ? 'Sim' : 'Não'}`);
      
      if (hasCookies) {
        console.log(`   📝 Cookies: ${cookies}`);
      }
    } else {
      console.log(`   ❌ Erro no teste de cookie: ${cookieTestResponse.statusCode}`);
    }
    
    console.log('\n3️⃣ Simulando configuração do frontend...');
    
    // Simula detecção de suporte baseada nos testes
    const frontendConfig = {
      httpOnlySupported: cookieTestResponse.data?.httpOnlySupported || false,
      withCredentials: true,
      cookiesReceived: cookies.length > 0,
      shouldUseCookies: cookieTestResponse.data?.httpOnlySupported && cookies.length > 0
    };
    
    console.log('   📊 Configuração recomendada para o frontend:');
    console.log(`   {`);
    console.log(`     httpOnlySupported: ${frontendConfig.httpOnlySupported},`);
    console.log(`     withCredentials: ${frontendConfig.withCredentials},`);
    console.log(`     cookiesReceived: ${frontendConfig.cookiesReceived},`);
    console.log(`     shouldUseCookies: ${frontendConfig.shouldUseCookies}`);
    console.log(`   }`);
    
    console.log('\n4️⃣ Exemplo de configuração do Axios:');
    console.log(`   axios.defaults.withCredentials = ${frontendConfig.shouldUseCookies};`);
    
    if (frontendConfig.shouldUseCookies) {
      console.log('\n✅ Sistema pronto para usar cookies HttpOnly!');
      console.log('   O frontend deve configurar withCredentials: true');
    } else {
      console.log('\n⚠️  Fallback para Authorization headers');
      console.log('   O frontend deve usar localStorage + Authorization header');
    }
    
  } catch (error) {
    console.error('❌ Erro no teste:', error.message);
  }
}

// Executa o teste
testCookieDetection();
