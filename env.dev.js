const env = require('dotenv').config({ path: './.env.dev' })
module.exports = env.parsed || {
  STAGE: 'dev',
  API_NAME: 'dsm-back-end',
  VERSION: '-v4',
  AWS_REGION_LOCATION: 'us-east-1',
  ACCOUNT_ID: '************',
  JWT_DECRIPTION_CREDENTIALS: 'arn:aws:secretsmanager:us-east-1:************:secret:dsm-token-encription-credentials-Fk8enl',
  DSM_API_SECREAT_MANAGER: 'arn:aws:secretsmanager:us-east-1:************:secret:dsm-integration-crm-user-nn1ZiE',
  DSM_API_WAF_ARN: 'arn:aws:secretsmanager:us-east-1:************:secret:waf-token-N4i1xm',
  USER_POOL_ID: 'us-east-1_VCf8aHRIZ',
  AWS_API_GATEWAY_COGNITO_NAME: 'hml-dsm'
}
