import { parsePath } from '../../shared/parsers'
import { message, json } from '../../shared/response'
import { readAll } from '../../model/dynamo'

async function sendDataToUser (status, statusString, data) {
  const msg = await message(statusString, data)
  return await json(msg, status)
}

export const handler = async (event, context) => {
  try {
    const { year, month } = parsePath(event)

    const { Items } = await readAll(`${process.env.FINOPS_STAGE}-dolar`)

    const dolar = Items.find(obj => parseInt(obj.year) === parseInt(year) && parseInt(obj.month) === parseInt(month))

    if (dolar) {
      delete dolar.id

      return await sendDataToUser(200, 'success', dolar)
    }

    return await sendDataToUser(200, 'success', 'Não encontrado')
  } catch (error) {
    return await sendDataToUser(500, 'error', error)
  }
}
