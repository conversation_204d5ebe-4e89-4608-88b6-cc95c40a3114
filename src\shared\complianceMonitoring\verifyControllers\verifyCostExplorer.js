import { accessKeysCustomerAccount } from "../../accessKeysCustomerAccount";
import { getStartEndDates } from "../getStartEndDates";

const AWS = require("aws-sdk");

const region = "us-east-1";

export async function verifyCostExplorer(account) {
  console.log("\n\nVerifying CUR...");

  const newSession = await accessKeysCustomerAccount(account);

  if (newSession) {
    const cur = new AWS.CostExplorer({
      region,
      credentials: {
        accessKeyId: newSession.access_key_id,
        secretAccessKey: newSession.secret_key_id,
        sessionToken: newSession.session_token,
      },
      MaxResults: 3,
    });

    const timePeriod = await getStartEndDates();
    console.log({ timePeriod });
    const categories = await cur
      .getCostCategories({
        TimePeriod: timePeriod,
      })
      .promise()
      .catch((err) => {
        console.log("Error getting categories", { err });
      });

    console.log({ categories });

    await cur
      .listCostCategoryDefinitions({})
      .promise()
      .catch(() => {
        return false;
      });
    if (!cur) return false;

    return true;
  }
  return false;
}
