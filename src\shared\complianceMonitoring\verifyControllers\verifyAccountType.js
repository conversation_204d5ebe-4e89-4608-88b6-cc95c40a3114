import { accessKeysCustomerAccount } from "../../accessKeysCustomerAccount";

const AWS = require("aws-sdk");

const region = "us-east-1";

export async function verifyAccountType(account) {
  console.log("\nVerifying if 'AccountType' tag is set to 'Critical'...");

  const newSession = await accessKeysCustomerAccount(account);

  if (newSession) {
    const organizations = new AWS.Organizations({
      region,
      credentials: {
        accessKeyId: newSession.access_key_id,
        secretAccessKey: newSession.secret_key_id,
        sessionToken: newSession.session_token,
      },
      MaxResults: 3,
    });

    let accountsInOrg = await organizations
      .listAccounts({})
      .promise()
      .catch((err) => {
        console.log("Error listing accounts in org!", { err });
        return false;
      });

    console.log({ accountsInOrg });

    if (!accountsInOrg) return false;

    accountsInOrg = accountsInOrg["Accounts"];

    for (let i = 0; i < accountsInOrg.length; i++) {
      const customerAccount = accountsInOrg[i];

      let res = await organizations
        .listTagsForResource({ ResourceId: customerAccount["Id"] })
        .promise()
        .catch(() => {
          return false;
        });

      console.log(
        `\n\tAccount: ${customerAccount["Id"]} - ${customerAccount["Name"]}`
      );
      console.log("\n\tverifyAccounType Tags: ", res.Tags);

      if (!res) {
        return false;
      }

      let existsAccountType = false;

      for (let i = 0; i < res.Tags.length; i++) {
        console.log(
          `  res.Tags[${i}]: `,
          res.Tags[i],
          res.Tags[i]["Key"],
          res.Tags[i]["Value"]
        );
        if (
          res.Tags[i]["Key"].toLowerCase().trim() === "accounttype" &&
          res.Tags[i]["Value"].toLowerCase().trim() === "critical"
        ) {
          existsAccountType = true;
        }
      }

      if (!existsAccountType) {
        console.log(
          `\tTag 'AccountType' = 'Critical' is not set on account ${customerAccount["Id"]} - ${customerAccount["Name"]}\n`
        );
        return false;
      }
    }
    return true;
  }
  return false;
}
