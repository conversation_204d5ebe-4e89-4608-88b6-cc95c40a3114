const AWS = require("aws-sdk");

const region = "us-east-1";
const sts = new AWS.STS({
  region,
  accessKeyId: `${process.env.FINOPS_SWITCH_ACCOUNT_ACCESS_KEY}`,
  secretAccessKey: `${process.env.FINOPS_SWITCH_ACCOUNT_SECRET_KEY}`,
});

export async function getAccountId() {
  console.log("Getting account id...");
  const id = await sts
    .getAccessKeyInfo({
      AccessKeyId: `${process.env.FINOPS_SWITCH_ACCOUNT_ACCESS_KEY}`,
    })
    .promise();
  return id.Account;
}
