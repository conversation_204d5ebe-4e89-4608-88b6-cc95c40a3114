export class CustomerEntity {
  constructor(dynamoDB, tableName) {
    this.dynamoDB = dynamoDB;
    this.tableName = tableName;
  }

  async updateDynamoDBAccount(itemId, accountIndex, account) {
    const updateParams = {
      TableName: this.tableName,
      Key: { id: itemId },
      UpdateExpression: `SET accounts[${accountIndex}] = :account`,
      ExpressionAttributeValues: {
        ":account": account,
      },
    };

    try {
      await this.dynamoDB.update(updateParams).promise();
    } catch (error) {
      console.error(
        `Erro ao atualizar a conta no DynamoDB para ID ${itemId}, índice ${accountIndex}:`,
        error
      );
      throw error;
    }
  }

  async scanDynamoDB() {
    let lastEvaluatedKey = null;
    let items = [];

    do {
      const params = {
        TableName: this.tableName,
        FilterExpression: "#hasActiveContracts = :value",
        ExpressionAttributeNames: {
          "#hasActiveContracts": "has_active_contracts",
        },
        ExpressionAttributeValues: { ":value": 1 },
        ExclusiveStartKey: lastEvaluatedKey,
        Limit: 100,
      };

      try {
        const scanResult = await this.dynamoDB.scan(params).promise();
        items = [...items, ...scanResult.Items];
        lastEvaluatedKey = scanResult.LastEvaluatedKey;
      } catch (error) {
        console.error("Erro ao realizar o scan no DynamoDB:", error);
        throw error;
      }
    } while (lastEvaluatedKey);

    console.log(`Total de itens processados: ${items.length}`);
    return items;
  }

  generateReadCustomersByStatusInput({ status = 1, dynamodb }) {
    let params = {
      TableName: dynamodb,
      IndexName: "active-index",
      ExpressionAttributeNames: {
        "#active": "active",
      },
      KeyConditionExpression: "#active = :active",
      ExpressionAttributeValues: {
        ":active": parseInt(status),
      },
    };

    return params;
  }
  generateReadCustomersByStatusActiveInput({ status, dynamodb, isActive = 1 }) {
    let params = {
      TableName: dynamodb,
      IndexName: "has_active_contracts-index",
      ExpressionAttributeNames: {
        "#has_active_contracts": "has_active_contracts",
      },
      KeyConditionExpression: "#has_active_contracts = :has_active_contracts",
      FilterExpression: `active = :active`,
      ExpressionAttributeValues: {
        ":has_active_contracts": parseInt(status),
        ":active": parseInt(isActive),
      },
    };

    return params;
  }

  generateReadCustomersByStatusBillingInput({ dynamodb }) {
    let params = {
      TableName: dynamodb,
      IndexName: "billing-index",
      KeyConditionExpression: "has_active_contracts = :has_active_contracts",
      ExpressionAttributeValues: {
        ":has_active_contracts": 1,
      },
    };

    return params;
  }

  generateCNPJMask(cnpj) {
    if (!cnpj || cnpj === null) return "";
    return cnpj
      .toString()
      .replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, "$1.$2.$3/$4-$5");
  }

  generateReadCustomersByCNPJ({ cnpj, dynamodb }) {
    if (!cnpj || cnpj === null || cnpj === "")
      throw new Error("CNPJ is required");
    let params = {
      TableName: dynamodb,
      IndexName: "cnpj-index",
      ExpressionAttributeNames: {
        "#cnpj": "cnpj",
      },
      KeyConditionExpression: "#cnpj = :cnpj",
      ExpressionAttributeValues: {
        ":cnpj": cnpj,
      },
    };

    return params;
  }

  checkIfCustomerHasActiveContracts(contracts, customerId) {
    if (!contracts || !customerId) return false;
    return contracts.some((contract) => contract.customer.id === customerId);
  }

  checkIfCustomerIsProspect(allContracts, customerId) {
    if (!allContracts || !customerId) return false;
    return !allContracts.some(
      (contract) => contract.customer.id === customerId
    );
  }
}
