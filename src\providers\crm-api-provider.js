import axios from "axios";

import { SecretsManager } from "aws-sdk";

const secretClient = new SecretsManager({
  region: process.env.AWS_REGION_LOCATION,
});

export const crmApiProvider = async () => {
  const { SecretString } = await secretClient
    .getSecretValue({
      SecretId: process.env.DSM_API_SECREAT_MANAGER,
    })
    .promise();

  const { user, password } = JSON.parse(SecretString || "");

  return axios.create({
    baseURL: process.env.CRM_API_BASE_URL,
    auth: {
      password: password,
      username: user,
    },
  });
};
