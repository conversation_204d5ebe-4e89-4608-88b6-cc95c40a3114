import { IAM, STS } from "aws-sdk";
import { json, message } from "./response";

const region = process.env.AWS_REGION_LOCATION;

async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data);
  return await json(msg, status);
}

export const createRoleOnCustomerAccount = async (account) => {
  try {
    const stsDSMAccount = new STS({
      region,
    });
    const credentialsRootAccount = await stsDSMAccount
      .assumeRole({
        RoleArn: "arn:aws:iam::************:role/jump-access-roles",
        RoleSessionName: "darede-************",
      })
      .promise();

    console.log("credentialsRootAccount: ", credentialsRootAccount);

    const stsRootAccount = new STS({
      region,
      credentials: {
        accessKeyId: credentialsRootAccount.Credentials.AccessKeyId,
        secretAccessKey: credentialsRootAccount.Credentials.SecretAccessKey,
        sessionToken: credentialsRootAccount.Credentials.SessionToken,
      },
    });
    try {
      const credentialsCustomerAccount = await stsRootAccount
        .assumeRole({
          RoleArn: "arn:aws:iam::" + account + ":role/darede-full",
          RoleSessionName: "darede-" + account,
        })
        .promise();

      console.log("Customer: ", credentialsCustomerAccount);

      const iam = new IAM({
        region,
        credentials: {
          accessKeyId: credentialsCustomerAccount.Credentials.AccessKeyId,
          secretAccessKey:
            credentialsCustomerAccount.Credentials.SecretAccessKey,
          sessionToken: credentialsCustomerAccount.Credentials.SessionToken,
        },
      });
      try {
        await iam
          .createRole({
            AssumeRolePolicyDocument: JSON.stringify({
              Version: "2012-10-17",
              Statement: [
                {
                  Effect: "Allow",
                  Action: "sts:AssumeRole",
                  Principal: {
                    AWS: process.env.ARN_JUMP_ACCESS,
                    Service: ["lambda.amazonaws.com"],
                  },
                },
              ],
            }),

            RoleName: "darede-switch-role",
          })
          .promise();
        try {
          await iam
            .createPolicy({
              PolicyDocument: JSON.stringify({
                Version: "2012-10-17",
                Statement: [
                  {
                    Sid: "VisualEditor0",
                    Effect: "Allow",
                    Action: [
                      "iam:CreateRole",
                      "iam:AttachRolePolicy",
                      "iam:DetachRolePolicy",
                      "iam:DeleteRole",
                    ],
                    Resource: `arn:aws:iam::${account}:role/*`,
                  },
                ],
              }),
              PolicyName: "IAM-switch-role-access",
            })
            .promise()
            .then(async (resultAttachRole) => {
              await iam
                .attachRolePolicy({
                  PolicyArn: resultAttachRole.Policy.Arn,
                  RoleName: "darede-switch-role",
                })
                .promise();
            })
            .catch(async (e) => {
              console.log("Erro no atach role: ", e);
              return await sendDataToUser(
                500,
                "Erro ao realizar attach policy na conta do cliente: ",
                e
              );
            });
        } catch (e) {
          console.log("Erro ao criar policy: ", e);
          return await sendDataToUser(
            500,
            "Erro ao criar policy na conta do cliente: ",
            e
          );
        }
      } catch (e) {
        console.log(e);
        return await sendDataToUser(
          500,
          "Erro ao criar role na conta do cliente: ",
          e
        );
      }
    } catch (e) {
      console.log(
        "Erro ao dar assume role na conta do cliente: (provavelmente, este é um erro na configuração da role darede-full na conta do cliente) ",
        e
      );
      return await sendDataToUser(
        500,
        "Erro no createRoleOnCustomerAccount",
        e
      );
    }
    return await sendDataToUser(201, "Role created on account: " + account, {});
  } catch (e) {
    console.log("Erro no createRoleOnCustomerAccount: ", e);
    return await sendDataToUser(500, "Erro no createRoleOnCustomerAccount", e);
  }
};
