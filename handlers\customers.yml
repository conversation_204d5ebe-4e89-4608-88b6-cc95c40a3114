read-customer:
  handler: src/functions/customers/readCustomers.handler
  name: ${self:custom.dotenv.STAGE}-read-customer${self:custom.dotenv.VERSION}
  description: Função para retornar um cliente com base no status
  memorySize: 128
  timeout: 900
  events:
    - http:
        path: /customers/read/{form}
        method: get
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}

update-has-active-contracts:
  handler: src/functions/customers/updateHasActiveContracts.handler
  name: ${self:custom.dotenv.STAGE}-update-has-active-contracts${self:custom.dotenv.VERSION}
  description: Função para retornar um cliente com base no status
  memorySize: 128
  timeout: 900
  events:
    - http:
        path: /customers/updateHasActiveContracts/{customerId}
        method: put
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}

read-customerV2:
  handler: src/functions/customers/getCustomers.handler
  name: ${self:custom.dotenv.STAGE}-read-customers${self:custom.dotenv.VERSION}
  description: Função para leitura de dados de customers da tabela do DynamoDB
  memorySize: 128
  events:
    - http:
        path: /read/customers
        method: get
        cors: true
        authorizer:
          name: ${self:custom.dotenv.AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${self:custom.dotenv.AWS_REGION_LOCATION}:${self:custom.dotenv.ACCOUNT_ID}:userpool/${self:custom.dotenv.USER_POOL_ID}


