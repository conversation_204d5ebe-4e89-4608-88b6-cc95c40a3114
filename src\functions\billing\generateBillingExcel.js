import { parseQueryString, parsePath } from '../../shared/parsers'
import {
    responseWithSuccess,
    responseWithError,
    responseWithBadRequest,
    responseWithNotFound
} from '../../shared/response'

import xl from 'excel4node'

import { makeS3 } from '../../shared/services/s3-service'
import { makeLambda } from '../../shared/services/lambda-service'

const s3 = makeS3()
const lambda = makeLambda()

const API_NAME = process.env.API_NAME
const REPORTS_BUCKET_NAME = process.env.REPORTS_BUCKET_NAME

function onlyNumber(value) {
    if (value)
        return value.toString().replace(/\D/gm, "")
    else
        return ''
}

export const handler = async (event, context) => {
    try {
        let { year, month } = parseQueryString(event)
        let { payer } = parsePath(event)

        if (event.isInvoke) {
            year = event.year
            month = event.month
            payer = event.payer
        }

        if (!payer || !onlyNumber(payer))
            return responseWithBadRequest('invalid payer account')

        if (!year || !onlyNumber(year))
            return responseWithBadRequest('invalid year')

        if (!month || !onlyNumber(month))
            return responseWithBadRequest('invalid month')

        month = Number(month)
        if (month > 12)
            return responseWithBadRequest('invalid month')

        const fileKey = `billing/${year}-${month}/${payer}.xlsx`

        const entities = await getEntityAccounts({ month, year, payer })
        const wb = new xl.Workbook();

        const titleStyle = wb.createStyle({
            font: {
                size: 16,
                bold: true
            },
            alignment: {
                horizontal: 'center',
            },
        });

        const entityStyle = wb.createStyle({
            font: {
                size: 14,
                bold: true,
                alignment: 'right'
            },
            numberFormat: '$#,##0.00; ($#,##0.00); -',
        });
        const summaryTitle = wb.createStyle({
            font: {
                size: 14,
                bold: true,
                alignment: 'right'
            },
            numberFormat: '$#,##0.00; ($#,##0.00); -',
        });
        const serviceStyle = wb.createStyle({
            font: {
                size: 12,
                bold: true,
                color: '#FFC000'
            }
        });

        const regionStyle = wb.createStyle({
            font: {
                size: 10,
                bold: true
            }
        });

        const regionNegativeStyle = wb.createStyle({
            font: {
                size: 10,
                bold: true,
                color: '#FF0000'
            }
        });

        const descriptionStyle = wb.createStyle({
            font: {
                size: 10
            }
        });

        const ws = wb.addWorksheet('Serviços');
        const services = await getServicesOfAllAccounts({ year, month, payer })


        if (Object.keys(services).length == 0)
            return responseWithNotFound("No entities in this payer account")

        await renderServicesPage(ws, services, serviceStyle, regionStyle, descriptionStyle, entityStyle, year, month, titleStyle, summaryTitle, regionNegativeStyle)

        for (let entityIndex = 0; entityIndex < entities.length; entityIndex++) {
            const entity = entities[entityIndex];
            const accounts = entity.accounts

            for (let accountIndex = 0; accountIndex < accounts.length; accountIndex++) {
                const account = accounts[accountIndex];

                let isMarketPlace = !account.entity.includes("AWS")

                const worksheetName = account.account === payer ? `${account.account} - PAYER` : isMarketPlace ? `MKT Place - ${account.entity}` : account.account

                const ws = wb.addWorksheet(worksheetName);

                const getAllServicesMktPlace = async (entity) => {
                    let res = await getServicesOfAllAccounts({
                        year,
                        month,
                        payer,
                        isMarketPlace: 'true',
                        entity
                    })
                    return res.find(i => i.name === "AWS").accounts
                }

                const allServices = await getServicesAccount({ account: account.account, year, month, payer });

                const { services } = allServices;

                let rowPointer = 1;

                ws.cell(rowPointer, 1, rowPointer, 2, true)
                    .string(`Relatório de Billing - ${month}/${year}`)
                    .style(titleStyle)

                rowPointer++;
                rowPointer++;

                let entityName = isMarketPlace ? `Market Place - ${entity.name}` : `${entity.name}`

                ws.cell(rowPointer, 1, rowPointer, 2, true)
                    .string(entityName)
                    .style(entityStyle)

                rowPointer++;
                rowPointer++;

                ws.cell(rowPointer, 1)
                    .string(`Conta - ${account.account}`)
                    .style(entityStyle)

                ws.cell(rowPointer, 2)
                    .number(account.cost)
                    .style(entityStyle)

                rowPointer++;

                if (services || mktPlaceServices) {
                    let renderServices = isMarketPlace ? await getAllServicesMktPlace(account.entity) : services

                    renderServices.forEach(service => {
                        ws.cell(rowPointer, 1, rowPointer, 2, true)
                            .string(service.name)
                            .style(serviceStyle)

                        rowPointer++;

                        service.regions.forEach(region => {
                            ws.cell(rowPointer, 1, rowPointer, 2, true)
                                .string(region.location)
                                .style(regionStyle)

                            rowPointer++;

                            region.items.forEach(item => {
                                ws.cell(rowPointer, 1)
                                    .string(item.description)
                                    .style(descriptionStyle)

                                ws.cell(rowPointer, 2)
                                    .number(item.cost)
                                    .style(descriptionStyle)

                                rowPointer++;
                            })

                            ws.cell(rowPointer, 2)
                                .string(`TOTAL REGIÃO: $ ${region.cost.toFixed(2)}`)
                                .style(regionStyle)
                        })

                        rowPointer++;

                        ws.cell(rowPointer, 1)
                            .string(`TOTAL SERVIÇO: $ ${service.cost.toFixed(2)}`)
                            .style(regionStyle)


                        rowPointer++;
                        rowPointer++;

                    });
                }
            }
        }

        const buffer = await new Promise(resolve => {
            wb.writeToBuffer().then(function (buffer) {
                resolve(buffer)
            });
        })

        let url

        try {

            await s3.deleteObject({
                Bucket: REPORTS_BUCKET_NAME,
                Key: fileKey,
            }).promise()

            await s3.putObject({
                Bucket: REPORTS_BUCKET_NAME,
                Body: buffer,
                Key: fileKey,
            }).promise()

            url = await getFileOnBucket({ fileKey })
            return responseWithSuccess(url, 'Query successfully')

        } catch (error) {
            console.log(error)
        }




    } catch (error) {
        console.log(error)
        return responseWithError(error)
    }
}

async function renderServicesPage(ws, entities, serviceStyle, regionStyle, descriptionStyle, entityStyle, year, month, titleStyle, summaryTitle, regionNegativeStyle) {
    let rowPointer = 1

    const costs = entities.find(i => i.name === "costs")

    const taxes = entities.filter(i => i.name === "Taxes")
    const savingsPlan = entities.filter(i => i.name === "Savings Plans")

    ws.cell(rowPointer, 1, rowPointer, 2, true).string(`Relatório de Billing - ${month}/${year}`).style(titleStyle);
    rowPointer++;
    rowPointer++;


    ws.cell(rowPointer, 1, rowPointer, 2, true).string('Resumo:').style(summaryTitle);
    rowPointer++;

    if (costs.costsByInvoices.length > 0) {
        costs.costsByInvoices.forEach((invoice) => {
            const isNegative = invoice.cost < 0
            const invoiceName = invoice.bill_invoice_id ? `Custos AWS - Invoice ${invoice.bill_invoice_id}` : 'Custos AWS'

            const absoluteCost = Math.abs(invoice.cost)
            const costDescription = `${isNegative ? '- ' : ''} $${absoluteCost.toFixed(2)}`

            ws.cell(rowPointer, 1).string(invoiceName).style(descriptionStyle)
            ws.cell(rowPointer, 2).string(costDescription).style(isNegative ? regionNegativeStyle : regionStyle)
            rowPointer++;
        })
    } else {
        ws.cell(rowPointer, 1).string('Cost AWS Services').style(descriptionStyle)
        ws.cell(rowPointer, 2).string(`$${costs.costWithoutTaxes.toFixed(2)}`).style(regionStyle)
        rowPointer++;
    }

    if (costs.costsByInvoicesMarketPlace.length > 0) {
        costs.costsByInvoicesMarketPlace.forEach((market) => {
            const marketName = market.bill_invoice_id ? `Market Place - Invoice ${market.bill_invoice_id}` : 'Market Place'

            ws.cell(rowPointer, 1).string(marketName).style(descriptionStyle)
            ws.cell(rowPointer, 2).string(`$${market.cost.toFixed(2)}`).style(regionStyle)
            rowPointer++;
        })
    }

    ws.cell(rowPointer, 1).string('Taxas').style(descriptionStyle)
    ws.cell(rowPointer, 2).string(`$${costs.taxes.toFixed(2)}`).style(regionStyle)
    rowPointer++;

    ws.cell(rowPointer, 1).string('Savings Plans').style(descriptionStyle)
    ws.cell(rowPointer, 2).string(`- $${costs.savings_plan.toFixed(2)}`).style(regionNegativeStyle)
    rowPointer++;

    ws.cell(rowPointer, 1).string('TOTAL').style(regionStyle)
    ws.cell(rowPointer, 2).string(`$${costs.costWithMarketPlace.toFixed(2)}`).style(regionStyle)
    rowPointer++;
    rowPointer++;

    taxes.forEach(entity => {

        ws.cell(rowPointer, 1).string("Taxes").style(serviceStyle);
        ws.cell(rowPointer, 2).string(`TOTAL: $${costs.taxes.toFixed(2)}`).style(regionStyle)
        rowPointer++;

        entity.accounts.forEach(service => {
            ws.cell(rowPointer, 1).string(service.name).style(descriptionStyle)
            ws.cell(rowPointer, 2).string(`$${service.cost.toFixed(2)}`).style(regionStyle)
            rowPointer++;
        })
        rowPointer++;

    })

    savingsPlan.forEach(entity => {
        ws.cell(rowPointer, 1).string("Savings Plans").style(serviceStyle);
        ws.cell(rowPointer, 2).string(`TOTAL: $${costs.savings_plan.toFixed(2)}`).style(regionStyle)
        rowPointer++;

        entity.accounts.forEach(service => {
            ws.cell(rowPointer, 1).string(service.name).style(descriptionStyle)
            ws.cell(rowPointer, 2).string(`$${service.cost.toFixed(2)}`).style(regionStyle)
            rowPointer++;
        })
    })

    entities.filter(i => i.name !== "Savings Plans" && i.name !== "Taxes" && i.name !== "costs").forEach(entity => {
        rowPointer++;
        ws.cell(rowPointer, 1).string(`Serviços:`).style(entityStyle)
        ws.cell(rowPointer, 2).string(`TOTAL: $${costs.totalCost.toFixed(2)}`).style(regionStyle)
        rowPointer++;
        entity.accounts.forEach(service => {
            ws.cell(rowPointer, 1, rowPointer, 2, true).string(service.name).style(serviceStyle);
            rowPointer++;
            service.regions.forEach(region => {
                ws.cell(rowPointer, 1, rowPointer, 2, true).string(region.location).style(regionStyle);
                rowPointer++;
                region.items.forEach(item => {
                    ws.cell(rowPointer, 1).string(item.description).style(descriptionStyle);
                    ws.cell(rowPointer, 2).number(item.cost).style(descriptionStyle);
                    rowPointer++;
                });
                ws.cell(rowPointer, 2).string(`TOTAL REGIÃO: $ ${region.cost.toFixed(2)}`).style(regionStyle);
            });
            rowPointer++;
            ws.cell(rowPointer, 1).string(`TOTAL SERVIÇO: $ ${service.cost.toFixed(2)}`).style(regionStyle);
            rowPointer++;
            rowPointer++;
        });
    })
}

async function getEntityAccounts({ year, month, payer }) {
    const { Payload } = await lambda.invoke({
        FunctionName: `${API_NAME}-get-account-costs-by-payer`,
        Payload: JSON.stringify({
            isInvoke: true,
            year: year,
            month: month,
            payer: payer,
        })
    }).promise()

    const { body } = JSON.parse(Payload)
    const entities = JSON.parse(body)

    return entities
}

async function getServicesAccount({ account = null, year, month, payer, isMarketPlace, entity }) {
    const { Payload } = await lambda.invoke({
        FunctionName: `${API_NAME}-get-account-cost-by-service`,
        Payload: JSON.stringify({
            isInvoke: true,
            year,
            month,
            payer,
            account,
            isMarketPlace,
            entity
        })
    }).promise()


    const { body } = JSON.parse(Payload)
    // const { services } = JSON.parse(body)
    return JSON.parse(body)
}



async function getServicesOfAllAccounts({ year, month, payer, isMarketPlace = false, entity = null }) {
    const allServices = await getServicesAccount({ year, month, payer, isMarketPlace, entity })
    const {
        services,
        costWithoutDiscount,
        costWithoutTaxes,
        savings_plan,
        taxes,
        cost,
        costsByInvoices,
        costsByInvoicesMarketPlace,
        costWithMarketPlace
    } = allServices


    if (!services)
        return []


    const entities = services.reduce((result, service) => {
        const entity = service.entity
        const position = result.findIndex(r => r.name === entity)

        if (position > -1) {
            result[position].cost += service.cost
            result[position].accounts.push(service)
            return result
        }

        result.push({
            name: entity,
            cost: service.cost,
            accounts: [service]
        })

        return result
    }, [])

    entities.push({
        name: "costs",
        costWithoutDiscount: costWithoutDiscount,
        costWithoutTaxes: costWithoutTaxes,
        savings_plan: savings_plan,
        taxes: taxes,
        totalCost: cost,
        costsByInvoices: costsByInvoices,
        costsByInvoicesMarketPlace: costsByInvoicesMarketPlace,
        costWithMarketPlace: costWithMarketPlace
    })

    return entities
}


async function getFileOnBucket({ fileKey }) {
    try {
        const metadata = await s3.headObject({
            Bucket: REPORTS_BUCKET_NAME,
            Key: fileKey
        }).promise()

        if (metadata) {
            return await s3.getSignedUrlPromise('getObject', {
                Bucket: REPORTS_BUCKET_NAME,
                Key: fileKey
            })
        }

        return null

    } catch (error) {
        return null
    }
}