/**
 * Teste rápido para verificar se o deploy foi realizado
 * Execute: node scripts/quick-deploy-test.js
 */

const axios = require('axios');

const BASE_URL = 'https://pdu9dbemj1.execute-api.us-east-1.amazonaws.com/dev';

async function quickTest() {
  console.log('⚡ Teste rápido do status do deploy\n');
  
  const origin = 'https://dev.dsm.darede.com.br';
  
  // Teste principal: Preflight do endpoint problemático
  console.log('🔍 Testando preflight OPTIONS /read/id/user...');
  
  try {
    const response = await axios.options(`${BASE_URL}/read/id/user`, {
      headers: {
        'Origin': origin,
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type,DynamoDB'
      },
      validateStatus: () => true,
      timeout: 10000
    });
    
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200) {
      console.log('🎉 DEPLOY REALIZADO COM SUCESSO!');
      console.log('✅ Preflight funcionando');
      console.log(`✅ CORS Origin: ${response.headers['access-control-allow-origin']}`);
      
      // Teste adicional: requisição real
      console.log('\n🔍 Testando requisição real...');
      const realResponse = await axios.get(`${BASE_URL}/read/id/user`, {
        headers: {
          'Origin': origin,
          'DynamoDB': 'dev-permissions'
        },
        validateStatus: () => true,
        timeout: 10000
      });
      
      if (realResponse.status === 401) {
        console.log('✅ Endpoint protegido funcionando (401 Unauthorized)');
        console.log('\n🎯 CORREÇÕES APLICADAS COM SUCESSO!');
        console.log('   - CORS funcionando');
        console.log('   - Autenticação aplicada');
        console.log('   - Preflight permitido');
      }
      
    } else if (response.status === 403) {
      console.log('❌ Deploy ainda não realizado');
      console.log('   Authorizer do API Gateway ainda ativo');
    } else {
      console.log(`⚠️  Status inesperado: ${response.status}`);
    }
    
  } catch (error) {
    if (error.response?.data?.message === 'Missing Authentication Token') {
      console.log('❌ DEPLOY AINDA NÃO REALIZADO');
      console.log('   API Gateway authorizer ainda ativo');
    } else {
      console.log(`❌ Erro: ${error.message}`);
    }
  }
}

quickTest().catch(console.error);
