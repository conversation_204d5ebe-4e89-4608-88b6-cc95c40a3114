version: 0.2
phases:
  install:
    commands:
      - n 18.20.3
      - node -v
      - npm -v
      - rm -rf .serverless node_modules package-lock.json
  pre_build:
    commands:
      - echo Stack started...
      - echo "FINOPS_STAGE=${FINOPS_STAGE}" >> .env.${STAGE}
      - echo "STAGE=${STAGE}" >> .env.${STAGE}
      - echo "LOCAL_PORT=${LOCAL_PORT}" >> .env.${STAGE}
      - echo "AWS_API_GATEWAY_COGNITO_NAME=${AWS_API_GATEWAY_COGNITO_NAME}" >> .env.${STAGE}
      - echo "LAYER_BUCKET=${LAYER_BUCKET}" >> .env.${STAGE}
      - echo "VERSION=${VERSION}" >> .env.${STAGE}
      - echo "AWS_REGION_LOCATION=${AWS_REGION_LOCATION}" >> .env.${STAGE}
      - echo "API_NAME=${API_NAME}" >> .env.${STAGE}
      - echo "ACCOUNT_ID=${ACCOUNT_ID}" >> .env.${STAGE}
      - echo "USER_POOL_ID=${USER_POOL_ID}" >> .env.${STAGE}
      - echo "AWS_SECRET_VALUE_ACCOUNTS=${AWS_SECRET_VALUE_ACCOUNTS}" >> .env.${STAGE}
      - echo "AWS_SECRET_GOOGLE_AUTH_TOKEN=${AWS_SECRET_GOOGLE_AUTH_TOKEN}" >> .env.${STAGE}
      - echo "REPORTS_BUCKET_NAME=${REPORTS_BUCKET_NAME}" >> .env.${STAGE}
      - echo "FINOPS_SWITCH_ACCOUNT_ACCESS_KEY=${FINOPS_SWITCH_ACCOUNT_ACCESS_KEY}" >> .env.${STAGE}
      - echo "FINOPS_SWITCH_ACCOUNT_SECRET_KEY=${FINOPS_SWITCH_ACCOUNT_SECRET_KEY}" >> .env.${STAGE}
      - echo "ROOT_ACCOUNT_ID=${ROOT_ACCOUNT_ID}" >> .env.${STAGE}
      - echo "DSM_API_URL=${DSM_API_URL}" >> .env.${STAGE}
      - echo "TEAMS_WEBHOOK_URL=${TEAMS_WEBHOOK_URL}" >> .env.${STAGE}
      - echo "TEAMS_WEBHOOK_HEADER=${TEAMS_WEBHOOK_HEADER}" >> .env.${STAGE}
      - echo "DSM_API_SECREAT_MANAGER=${DSM_API_SECREAT_MANAGER}" >> .env.${STAGE}
      - echo "DSM_API_WAF_ARN=${DSM_API_WAF_ARN}" >> .env.${STAGE}
      - echo "OTRS_CALCULATE_BASE_URL=${OTRS_CALCULATE_BASE_URL}" >> .env.${STAGE}
      - echo "SENDER_EMAIL=${SENDER_EMAIL}" >> .env.${STAGE}
      - echo "AREA_SECREATS_ARN=${AREA_SECREATS_ARN}" >> .env.${STAGE}
      - echo "CREATE_EVENT_STEP_FUNCTION=${CREATE_EVENT_STEP_FUNCTION}" >> .env.${STAGE}
      - echo "TABLES_TO_SEND_EVENT=${TABLES_TO_SEND_EVENT}" >> .env.${STAGE}
      - echo "OTRS_ACCOUNT=${OTRS_ACCOUNT}" >> .env.${STAGE}
      - echo "DISPARITY_CONTRACT_FUNCTION=${DISPARITY_CONTRACT_FUNCTION}" >> .env.${STAGE}
      - echo "CRM_API_BASE_URL=${CRM_API_BASE_URL}" >> .env.${STAGE}
      - echo "BUCKET_REASONS_TO_DEACTIVATE_CONTRACT=${BUCKET_REASONS_TO_DEACTIVATE_CONTRACT}" >> .env.${STAGE}
      - echo "TABLE_REASONS_TO_DEACTIVATE_CONTRACT=${TABLE_REASONS_TO_DEACTIVATE_CONTRACT}" >> .env.${STAGE}
      - echo "API_OTRS_BASE_URL=${API_OTRS_BASE_URL}" >> .env.${STAGE}
      - echo "AWS_SECRET_CREDENTIALS_OTRS_API=${AWS_SECRET_CREDENTIALS_OTRS_API}" >> .env.${STAGE}
      - echo "BILLING_ROLE_ARN=${BILLING_ROLE_ARN}" >> .env.${STAGE}
      - echo "SWITCH_ROLE_NOTIFICATION_SNS_ARN=${SWITCH_ROLE_NOTIFICATION_SNS_ARN}" >> .env.${STAGE}
      - echo "ARN_JUMP_ACCESS=${ARN_JUMP_ACCESS}" >> .env.${STAGE}
      - echo "ROLE_NAME=${ROLE_NAME}" >> .env.${STAGE}
      - cat .env.${STAGE}
  build:
    on-failure: ABORT
    commands:
      - echo Installing source NPM dependencies...
      - npm install --force
      - cd layers/awsCoreLayer/nodejs && npm install && cd ../../..
      - cd layers/utilsLayer/nodejs && npm install && cd ../../..
      - cd layers/documentGenerationLayer/nodejs && npm install && cd ../../..
      - ls
    finally:
      - echo Success on installing npm dependencies...
  post_build:
    on-failure: ABORT
    commands:
      - echo Deploying application on API Gateway...
      - npm run deploy
    finally:
      - echo Success on deploy application on API Gateway...
