import { STS } from "aws-sdk";

const sts = new STS({ region: process.env.AWS_REGION_LOCATION });

export async function assumeRoleOnRootAccount() {
    const { Credentials } = await sts.assumeRole({
        RoleArn:
            "arn:aws:iam::************:role/ses-to-send-email-dosystems-landing-pages",
        RoleSessionName: "darede-************",
    }).promise()

    return {
        accessKeyId: Credentials.AccessKeyId,
        secretAccessKey: Credentials.SecretAccessKey,
        sessionToken: Credentials.SessionToken,
    };
}
