import { makeS3 } from "../../shared/services/s3-service";
import { errorValidator } from "../../shared/validators";
import { parseBody } from "../../shared/parsers";

import {
  STATUS_CODE,
  responseWithError,
  responseWithSuccess,
  responseWithBadRequest,
} from "../../shared/response";

const s3 = makeS3();

export const handler = async (event) => {
  try {
    const body = parseBody(event);

    console.log({ body });

    const bucketName = body.bucketName;
    const fileKeys = body.fileKeys;

    console.log({ bucketName });
    console.log({ fileKeys });

    if (fileKeys.length === 0) {
      return responseWithSuccess("Não há objetos para serem deletados!");
    }

    for (let i = 0; i < fileKeys.length; i++) {
      const params = {
        Bucket: bucketName,
        Key: fileKeys[i],
      };

      console.log({ params });

      const responseS3 = await s3.deleteObject(params).promise();

      console.log({ responseS3 });
    }

    return responseWithSuccess(
      "Deleção realizada com sucesso, objetos considerados: "
    );
  } catch (error) {
    const { statusCode, message } = errorValidator(error);

    if (STATUS_CODE.BAD_REQUEST === statusCode)
      return responseWithBadRequest(message);

    return responseWithError(message);
  }
};
