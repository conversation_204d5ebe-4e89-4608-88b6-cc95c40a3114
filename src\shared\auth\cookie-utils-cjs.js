/**
 * Utilitários para gerenciamento de cookies em ambiente <PERSON> (CommonJS)
 */

// Configurações de cookies
const COOKIE_CONFIG = {
  ACCESS_TOKEN: {
    name: 'dsm_access_token',
    maxAge: 8 * 60 * 60 * 1000, // 8 horas em millisegundos
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    path: '/'
  },
  REFRESH_TOKEN: {
    name: 'dsm_refresh_token',
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 dias em millisegundos
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    path: '/'
  }
};

/**
 * Cria um header Set-Cookie para Lambda response
 * @param {string} name - Nome do cookie
 * @param {string} value - Valor do cookie
 * @param {Object} options - Opções do cookie
 * @returns {string} String do header Set-Cookie
 */
const createCookieHeader = (name, value, options = {}) => {
  const config = { ...COOKIE_CONFIG.ACCESS_TOKEN, ...options };
  
  let cookieString = `${name}=${value}`;
  
  if (config.maxAge) {
    cookieString += `; Max-Age=${Math.floor(config.maxAge / 1000)}`;
  }
  
  if (config.httpOnly) {
    cookieString += '; HttpOnly';
  }
  
  if (config.secure) {
    cookieString += '; Secure';
  }
  
  if (config.sameSite) {
    cookieString += `; SameSite=${config.sameSite}`;
  }
  
  if (config.path) {
    cookieString += `; Path=${config.path}`;
  }
  
  if (config.domain) {
    cookieString += `; Domain=${config.domain}`;
  }
  
  return cookieString;
};

/**
 * Cria header para deletar um cookie
 * @param {string} name - Nome do cookie a ser deletado
 * @param {Object} options - Opções adicionais
 * @returns {string} String do header Set-Cookie para deletar
 */
const createDeleteCookieHeader = (name, options = {}) => {
  const config = { ...COOKIE_CONFIG.ACCESS_TOKEN, ...options };
  
  let cookieString = `${name}=; Max-Age=0; Expires=Thu, 01 Jan 1970 00:00:00 GMT`;
  
  if (config.path) {
    cookieString += `; Path=${config.path}`;
  }
  
  if (config.domain) {
    cookieString += `; Domain=${config.domain}`;
  }
  
  return cookieString;
};

/**
 * Extrai cookies do header da requisição Lambda
 * @param {Object} event - Evento Lambda
 * @returns {Object} Objeto com os cookies parseados
 */
const parseCookies = (event) => {
  const cookies = {};
  
  if (!event.headers) return cookies;
  
  // Verifica diferentes formatos de header de cookie
  const cookieHeader = event.headers.Cookie || 
                      event.headers.cookie || 
                      event.headers['Set-Cookie'] ||
                      event.headers['set-cookie'];
  
  if (!cookieHeader) return cookies;
  
  // Parse dos cookies
  const cookieStrings = Array.isArray(cookieHeader) ? cookieHeader : [cookieHeader];
  
  cookieStrings.forEach(cookieString => {
    cookieString.split(';').forEach(cookie => {
      const [name, ...rest] = cookie.trim().split('=');
      if (name && rest.length > 0) {
        cookies[name] = decodeURIComponent(rest.join('='));
      }
    });
  });
  
  return cookies;
};

/**
 * Obtém o token de acesso dos cookies
 * @param {Object} event - Evento Lambda
 * @returns {string|null} Token de acesso ou null se não encontrado
 */
const getAccessTokenFromCookies = (event) => {
  const cookies = parseCookies(event);
  return cookies[COOKIE_CONFIG.ACCESS_TOKEN.name] || null;
};

/**
 * Obtém o refresh token dos cookies
 * @param {Object} event - Evento Lambda
 * @returns {string|null} Refresh token ou null se não encontrado
 */
const getRefreshTokenFromCookies = (event) => {
  const cookies = parseCookies(event);
  return cookies[COOKIE_CONFIG.REFRESH_TOKEN.name] || null;
};

/**
 * Cria headers para definir ambos os tokens (access e refresh)
 * @param {string} accessToken - Token de acesso
 * @param {string} refreshToken - Refresh token
 * @returns {Array} Array de headers Set-Cookie
 */
const createAuthCookieHeaders = (accessToken, refreshToken) => {
  const headers = [];
  
  if (accessToken) {
    headers.push(createCookieHeader(
      COOKIE_CONFIG.ACCESS_TOKEN.name,
      accessToken,
      COOKIE_CONFIG.ACCESS_TOKEN
    ));
  }
  
  if (refreshToken) {
    headers.push(createCookieHeader(
      COOKIE_CONFIG.REFRESH_TOKEN.name,
      refreshToken,
      COOKIE_CONFIG.REFRESH_TOKEN
    ));
  }
  
  return headers;
};

/**
 * Cria headers para deletar todos os cookies de autenticação
 * @returns {Array} Array de headers Set-Cookie para deletar
 */
const createLogoutCookieHeaders = () => {
  return [
    createDeleteCookieHeader(COOKIE_CONFIG.ACCESS_TOKEN.name),
    createDeleteCookieHeader(COOKIE_CONFIG.REFRESH_TOKEN.name)
  ];
};

/**
 * Obtém configurações de cookie baseadas no ambiente
 * @returns {Object} Configurações de cookie
 */
const getCookieConfig = () => {
  return {
    ...COOKIE_CONFIG,
    // Ajusta configurações baseadas no ambiente
    ACCESS_TOKEN: {
      ...COOKIE_CONFIG.ACCESS_TOKEN,
      secure: process.env.NODE_ENV === 'production',
      domain: process.env.COOKIE_DOMAIN || undefined
    },
    REFRESH_TOKEN: {
      ...COOKIE_CONFIG.REFRESH_TOKEN,
      secure: process.env.NODE_ENV === 'production',
      domain: process.env.COOKIE_DOMAIN || undefined
    }
  };
};

module.exports = {
  createCookieHeader,
  createDeleteCookieHeader,
  parseCookies,
  getAccessTokenFromCookies,
  getRefreshTokenFromCookies,
  createAuthCookieHeaders,
  createLogoutCookieHeaders,
  getCookieConfig
};
