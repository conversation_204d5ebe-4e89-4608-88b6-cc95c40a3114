#!/usr/bin/env node

/**
 * Script para verificar quais funções não têm layers configuradas
 * mas precisam de pacotes da utilsLayer
 */

const fs = require('fs');
const path = require('path');
const YAML = require('yamljs');

console.log('🔍 Verificando funções sem layers configuradas');
console.log('==============================================\n');

// Pacotes que indicam necessidade da utilsLayer
const UTILS_LAYER_PACKAGES = [
  'jsonwebtoken',
  'cookie-parser', 
  'dotenv',
  'axios',
  'uuid',
  'date-fns',
  'nodemailer',
  'yamljs',
  'yup'
];

// Pacotes que indicam necessidade da awsCoreLayer
const AWS_CORE_PACKAGES = [
  'aws-sdk',
  'aws-multipart-parser',
  'athena-express'
];

// Pacotes que indicam necessidade da documentGenerationLayer
const DOC_GEN_PACKAGES = [
  'pdfmake',
  'excel4node'
];

/**
 * Verifica se um arquivo de função usa pacotes específicos
 */
function checkFunctionDependencies(functionPath) {
  try {
    const content = fs.readFileSync(functionPath, 'utf8');
    
    const needsUtils = UTILS_LAYER_PACKAGES.some(pkg => 
      content.includes(`require('${pkg}')`) || 
      content.includes(`require("${pkg}")`) ||
      content.includes(`from '${pkg}'`) ||
      content.includes(`from "${pkg}"`)
    );
    
    const needsAwsCore = AWS_CORE_PACKAGES.some(pkg => 
      content.includes(`require('${pkg}')`) || 
      content.includes(`require("${pkg}")`) ||
      content.includes(`from '${pkg}'`) ||
      content.includes(`from "${pkg}"`)
    );
    
    const needsDocGen = DOC_GEN_PACKAGES.some(pkg => 
      content.includes(`require('${pkg}')`) || 
      content.includes(`require("${pkg}")`) ||
      content.includes(`from '${pkg}'`) ||
      content.includes(`from "${pkg}"`)
    );
    
    return {
      needsUtils,
      needsAwsCore,
      needsDocGen,
      usedPackages: [
        ...UTILS_LAYER_PACKAGES.filter(pkg => content.includes(pkg)),
        ...AWS_CORE_PACKAGES.filter(pkg => content.includes(pkg)),
        ...DOC_GEN_PACKAGES.filter(pkg => content.includes(pkg))
      ]
    };
  } catch (error) {
    return { needsUtils: false, needsAwsCore: false, needsDocGen: false, usedPackages: [] };
  }
}

/**
 * Verifica configuração de layers nos handlers YAML
 */
function checkHandlerLayers() {
  const handlersDir = './handlers';
  const handlerFiles = fs.readdirSync(handlersDir).filter(file => file.endsWith('.yml'));
  
  const issues = [];
  
  for (const handlerFile of handlerFiles) {
    const handlerPath = path.join(handlersDir, handlerFile);
    const handlerContent = fs.readFileSync(handlerPath, 'utf8');
    
    try {
      const handlerConfig = YAML.parse(handlerContent);
      
      for (const [functionName, functionConfig] of Object.entries(handlerConfig)) {
        if (!functionConfig.handler) continue;
        
        const functionPath = `./src/functions/${functionConfig.handler.replace('.handler', '.js')}`;
        const dependencies = checkFunctionDependencies(functionPath);
        
        const hasLayers = functionConfig.layers && functionConfig.layers.length > 0;
        const hasUtilsLayer = hasLayers && functionConfig.layers.some(layer => 
          layer.Ref === 'UtilsLambdaLayer' || layer === 'UtilsLambdaLayer'
        );
        const hasAwsCoreLayer = hasLayers && functionConfig.layers.some(layer => 
          layer.Ref === 'AwsCoreLambdaLayer' || layer === 'AwsCoreLambdaLayer'
        );
        const hasDocGenLayer = hasLayers && functionConfig.layers.some(layer => 
          layer.Ref === 'DocumentGenerationLambdaLayer' || layer === 'DocumentGenerationLambdaLayer'
        );
        
        // Verifica se há problemas
        const problems = [];
        
        if (dependencies.needsUtils && !hasUtilsLayer) {
          problems.push('❌ Precisa de UtilsLambdaLayer');
        }
        
        if (dependencies.needsAwsCore && !hasAwsCoreLayer) {
          problems.push('❌ Precisa de AwsCoreLambdaLayer');
        }
        
        if (dependencies.needsDocGen && !hasDocGenLayer) {
          problems.push('❌ Precisa de DocumentGenerationLambdaLayer');
        }
        
        if (problems.length > 0) {
          issues.push({
            file: handlerFile,
            function: functionName,
            handler: functionConfig.handler,
            problems,
            usedPackages: dependencies.usedPackages,
            currentLayers: hasLayers ? functionConfig.layers : []
          });
        }
      }
    } catch (error) {
      console.log(`⚠️ Erro ao processar ${handlerFile}: ${error.message}`);
    }
  }
  
  return issues;
}

// Executa verificação
const issues = checkHandlerLayers();

if (issues.length === 0) {
  console.log('✅ Todas as funções têm layers configuradas corretamente!');
  console.log('✅ Nenhum problema encontrado.');
} else {
  console.log(`❌ Encontrados ${issues.length} problemas de configuração de layers:\n`);
  
  issues.forEach((issue, index) => {
    console.log(`${index + 1}. 📁 ${issue.file} → ${issue.function}`);
    console.log(`   Handler: ${issue.handler}`);
    console.log(`   Problemas:`);
    issue.problems.forEach(problem => console.log(`     ${problem}`));
    console.log(`   Pacotes usados: ${issue.usedPackages.join(', ') || 'Nenhum detectado'}`);
    console.log(`   Layers atuais: ${issue.currentLayers.length > 0 ? JSON.stringify(issue.currentLayers) : 'Nenhuma'}`);
    console.log('');
  });
  
  console.log('🔧 CORREÇÕES SUGERIDAS:');
  console.log('=======================');
  
  issues.forEach((issue, index) => {
    const suggestedLayers = [];
    
    if (issue.problems.some(p => p.includes('UtilsLambdaLayer'))) {
      suggestedLayers.push('{ Ref: UtilsLambdaLayer }');
    }
    
    if (issue.problems.some(p => p.includes('AwsCoreLambdaLayer'))) {
      suggestedLayers.push('{ Ref: AwsCoreLambdaLayer }');
    }
    
    if (issue.problems.some(p => p.includes('DocumentGenerationLambdaLayer'))) {
      suggestedLayers.push('{ Ref: DocumentGenerationLambdaLayer }');
    }
    
    console.log(`${index + 1}. ${issue.function}:`);
    console.log(`   layers:`);
    suggestedLayers.forEach(layer => console.log(`     - ${layer}`));
    console.log('');
  });
}

console.log('📊 RESUMO:');
console.log(`- Funções verificadas: ${issues.length > 0 ? 'Várias' : 'Todas'}`);
console.log(`- Problemas encontrados: ${issues.length}`);
console.log(`- Status: ${issues.length === 0 ? '✅ OK' : '❌ Requer correção'}`);
