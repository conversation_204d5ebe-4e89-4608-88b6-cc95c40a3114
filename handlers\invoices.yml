get-invoices:
  handler: src/functions/invoices/get-invoices.handler
  name: ${env:STAGE}-get-invoices${env:VERSION}
  description: Função para buscar os invoices dos clientes em um periodo de data
  memorySize: 128
  timeout: 30
  events:
    - schedule:
        rate: rate(1 day)
        name: ${env:STAGE}-get-invoices-schedule${env:VERSION}
    - http:
        path: /invoices
        method: post
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

read-invoices:
  handler: src/functions/invoices/read-invoices.handler
  name: ${env:STAGE}-read-invoices${env:VERSION}
  description: Função para buscar os invoices dos clientes em um periodo de data
  memorySize: 128
  timeout: 30
  events:
    - http:
        path: /read-invoices
        method: get
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}


