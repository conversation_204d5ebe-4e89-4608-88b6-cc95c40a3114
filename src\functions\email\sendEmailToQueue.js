import * as Yup from 'yup'
import { parse } from 'aws-multipart-parser'
import { v4 as uuid } from 'uuid'

import {
    STATUS_CODE,
    responseWithError,
    responseWithSuccess,
    responseWithBadRequest
} from '../../shared/response'

import { errorValidator } from '../../shared/validators'

import { makeSQS } from '../../shared/services/sqs-service'
import { makeLambda } from '../../shared/services/lambda-service'
import { makeS3 } from '../../shared/services/s3-service'

const API_NAME = process.env.API_NAME
const AWS_REGION_LOCATION = process.env.AWS_REGION_LOCATION
const ACCOUNT_ID = process.env.ACCOUNT_ID

const FILE_QUEUE_NAME = process.env.EMAIL_AUTOMATION_FILES_QUEUE_NAME
const INFO_QUEUE_NAME = process.env.EMAIL_AUTOMATION_INFO_QUEUE_NAME

const BUCKET_NAME = process.env.EMAIL_AUTOMATION_BUCKET_NAME

let bodyValidade = Yup.object().shape({
    from: Yup.string().required(),
    subject: Yup.string().required(),
    emails: Yup.string().required(),
    html: Yup.string().required()
})

const sqs = makeSQS()
const lambda = makeLambda()
const s3 = makeS3()

export const handler = async (event) => {
    try {
        let { from, subject, emails, html } = parse(event)

        const decriptSubject = Buffer.from(subject, 'base64').toString('utf8')
        const decriptHtml = Buffer.from(html, 'base64').toString('utf8')

        await bodyValidade.validate(
            { 
                from, 
                subject: decriptSubject, 
                emails, 
                html: decriptHtml 
            },
            { abortEarly: false } // Ao estar desativado retorna todos os erros de uma só vez
        )

        let queueFileURL = await verifyStatusQueue(FILE_QUEUE_NAME)
        if (!queueFileURL) {
            queueFileURL = await createQueue(FILE_QUEUE_NAME)
            await addQueueTriggerOnLambda(FILE_QUEUE_NAME)
        }

        let queueInfoURL = await verifyStatusQueue(INFO_QUEUE_NAME)
        if (!queueInfoURL)
            queueInfoURL = await createQueue(INFO_QUEUE_NAME)

        const messageGroupId = uuid()

        await publishInfos({
            from,
            subject: decriptSubject,
            emails,
            messageGroupId,
            queueInfoURL
        })
        await publishImages({
            html: decriptHtml,
            messageGroupId,
            queueFileURL
        })

        await uploadtTemplate({
            html: decriptHtml,
            messageGroupId
        })

        return responseWithSuccess(null, 'Email enviado com sucesso')

    } catch (error) {
        const { statusCode, message } = errorValidator(error)

        if (STATUS_CODE.BAD_REQUEST === statusCode)
            return responseWithBadRequest(message)

        return responseWithError(message)
    };
}

async function publishInfos({ from, subject, emails, messageGroupId, queueInfoURL }) {
    return await publishMessage({
        message: JSON.stringify({
            from,
            subject,
            emails,
            groupdId: messageGroupId
        }),
        messageGroupId,
        deduplicationId: `${messageGroupId}-info`,
        queueURL: queueInfoURL
    })
}

async function verifyStatusQueue(queueName) {
    const queueList = await listQueues()
    const queueURL = generateQueueURL(queueName)

    if (queueList.QueueUrls)
        return queueList.QueueUrls.find(url => url === queueURL)

    return null
}

async function createQueue(queueName) {
    const queue = await sqs.createQueue({
        QueueName: queueName,
        Attributes: {
            FifoQueue: 'true',
        }
    }).promise()

    return queue.QueueUrl
}

async function addQueueTriggerOnLambda(queueName) {
    const queueARN = generateArn(queueName, 'sqs')

    const triggers = await lambda.listEventSourceMappings({
        FunctionName: `${API_NAME}-upload-to-bucket`,
    }).promise()

    let triggerStatus = ''
    const trigger = triggers.EventSourceMappings.find(event => event.EventSourceArn === queueARN)
    if (trigger) {
        const response = await lambda.deleteEventSourceMapping({
            UUID: trigger.UUID
        }).promise()

        triggerStatus = response.State
        while (triggerStatus === 'Deleting') {
            waitFewSeconds(2000)
            try {
                const eventSource = await lambda.getEventSourceMapping({
                    UUID: trigger.UUID
                }).promise()

                triggerStatus = eventSource.State
            } catch (error) {
                triggerStatus = 'OK'
            }
        }
    }

    return await lambda.createEventSourceMapping({
        FunctionName: `${API_NAME}-upload-to-bucket`,
        EventSourceArn: queueARN
    }).promise()
}

async function publishImages({ html, messageGroupId, queueFileURL }) {
    const images = html.toString().match(/data:image\/[a-zA-Z0-9+\/=]+;base64,[a-zA-Z0-9+\/=]+/g)
    if (images) {
        for (let index = 0; index < images.length; index++) {
            let image = images[index];
            let contentType = image.match(/image\/[a-zA-Z0-9+\/=]+/g).join("")

            const [type, extension] = contentType.split("/")

            const imageName = `${messageGroupId}-${index}`
            html = html.replace(image, imageName)

            const message = formactMessage({
                data: image,
                fileName: `images/${imageName}.${extension}`,
                contentType: contentType
            })

            await publishMessage({
                message,
                messageGroupId,
                deduplicationId: imageName,
                queueURL: queueFileURL
            })
        }
    }
}

async function uploadtTemplate({ html, messageGroupId }) {
    const templateName = `template/${messageGroupId}`

    let params = {
        Bucket: BUCKET_NAME,
        Body: html,
        Key: `${templateName}.html`
    }
    
    const responseS3 = await s3.upload(params).promise();

    return responseS3
}

async function publishMessage({ message, messageGroupId, deduplicationId, queueURL }) {
    return await sqs.sendMessage({
        MessageBody: message,
        QueueUrl: queueURL,
        MessageGroupId: messageGroupId,
        MessageDeduplicationId: deduplicationId
    }).promise()
}

function formactMessage({ data, fileName, contentType }) {
    return JSON.stringify({ data, fileName, contentType })
}

function generateArn(queue, type) {
    return `arn:aws:${type}:${AWS_REGION_LOCATION}:${ACCOUNT_ID}:${queue}`
}

function generateQueueURL(queue) {
    return `https://sqs.${AWS_REGION_LOCATION}.amazonaws.com/${ACCOUNT_ID}/${queue}`
}

async function listQueues() {
    return await sqs.listQueues().promise()
}

async function waitFewSeconds(seconds) {
    return new Promise((resolve) => {
        setTimeout(() => resolve(true), seconds)
    })
}