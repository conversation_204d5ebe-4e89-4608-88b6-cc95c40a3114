import {
  readAccountsByCustomerDsmID,
  readAllAccounts,
  readPaginate,
} from "../../model/dynamo";
import { DatabaseEntity } from "../../entities/database-entity";
import { mockedCustomerRegister } from "../../__mocks__/dynamo/mockData";

describe("Testing the readAccountsByCustomerDsmID", () => {
  it("Should return accounts by customer dsm_id", async () => {
    const tableName = `hml-customers`;
    const dsm_id = "1";
    const dbEntitySpy = jest.spyOn(
      DatabaseEntity.prototype,
      "getCustomersByScan"
    );
    dbEntitySpy.mockResolvedValueOnce({
      Items: [
        {
          dsm_id: "1",
          names: { fantasy_name: "Customer1" },
          accounts: mockedCustomerRegister.accounts,
        },
        {
          dsm_id: "2",
          names: { fantasy_name: "Customer2" },
          accounts: mockedCustomerRegister.accounts,
        },
      ],
      lastEvaluatedKey: undefined,
    });
    const response = await readAccountsByCustomerDsmID(tableName, dsm_id);
    expect(dbEntitySpy).toHaveBeenCalledWith(tableName);
    expect(response).toEqual({
      owner_id: "1",
      owner_name: "Customer1",
      accounts: mockedCustomerRegister.accounts,
    });
  });
  it("Should return error message when the dsm_id parameter was invalid", async () => {
    const tableName = `hml-customers`;
    const invalid_dsm_id = undefined;
    await expect(
      readAccountsByCustomerDsmID(tableName, invalid_dsm_id)
    ).rejects.toThrow(
      `Houve um erro inesperado!\nNão foi possível obter as contas do cliente "${invalid_dsm_id}"`
    );
  });
});

describe("Testing the readAllAccounts", () => {
  it("Should return all accounts", async () => {
    const tableName = `hml-customers`;

    const dbEntitySpy = jest.spyOn(
      DatabaseEntity.prototype,
      "getCustomersByScan"
    );

    dbEntitySpy.mockResolvedValueOnce({
      Items: [
        {
          dsm_id: "1",
          names: { fantasy_name: "Customer1" },
          accounts: mockedCustomerRegister.accounts,
        },
        {
          dsm_id: "2",
          names: { fantasy_name: "Customer2" },
          accounts: mockedCustomerRegister.accounts,
        },
      ],
      lastEvaluatedKey: undefined,
    });

    const expectedResponse = [
      {
        owner_id: "1",
        owner_name: "Customer1",
        accounts: mockedCustomerRegister.accounts,
      },
      {
        owner_id: "2",
        owner_name: "Customer2",
        accounts: mockedCustomerRegister.accounts,
      },
    ];
    const response = await readAllAccounts(tableName);

    expect(dbEntitySpy).toHaveBeenCalledWith(tableName);
    expect(response).toEqual(expectedResponse);
  });
  it("Should return an error message when the tableName parameter was invalid", async () => {
    const tableName = undefined;

    await expect(readAllAccounts(tableName)).rejects.toThrow(
      `A tabela "${tableName}" é inválida ou não existe!\nNão foi possível buscar as contas AWS dos clientes`
    );
  });
});
