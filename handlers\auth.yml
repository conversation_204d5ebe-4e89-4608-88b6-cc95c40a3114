# set-token:
#   handler: src/functions/auth/set-token.handler
#   name: ${env:STAGE}-set-token${env:VERSION}
#   description: Função para armazenar token JWT em cookies HttpOnly seguros (DESABILITADO - HttpOnly)
#   memorySize: 128
#   timeout: 30
#   events:
#     - http:
#         path: /auth/set-token
#         method: post
#         cors: true

# set-auth-cookie:
#   handler: src/functions/auth/setCookie.handler
#   name: ${env:STAGE}-set-auth-cookie${env:VERSION}
#   description: Função para definir cookie de autenticação HttpOnly (DESABILITADO - HttpOnly)
#   memorySize: 128
#   events:
#     - http:
#         path: /auth/set-cookie
#         method: post
#         cors: true
#     - http:
#         path: /auth/set-cookie
#         method: options
#         cors: true

# refresh-token:
#   handler: src/functions/auth/refresh.handler
#   name: ${env:STAGE}-refresh-token${env:VERSION}
#   description: Função para renovar token de acesso usando refresh token (DESABILITADO - HttpOnly)
#   memorySize: 128
#   timeout: 30
#   events:
#     - http:
#         path: /auth/refresh
#         method: post
#         cors: true
#     - http:
#         path: /auth/refresh
#         method: options
#         cors: true

# verify-auth:
#   handler: src/functions/auth/verify.handler
#   name: ${env:STAGE}-verify-auth${env:VERSION}
#   description: Função para verificar autenticação via cookie (DESABILITADO - HttpOnly)
#   memorySize: 128
#   timeout: 30
#   events:
#     - http:
#         path: /auth/verify
#         method: get
#         cors: true
#     - http:
#         path: /auth/verify
#         method: options
#         cors: true

# logout:
#   handler: src/functions/auth/logout.handler
#   name: ${env:STAGE}-logout${env:VERSION}
#   description: Função para realizar logout e limpar cookies de autenticação (DESABILITADO - HttpOnly)
#   memorySize: 128
#   timeout: 30
#   events:
#     - http:
#         path: /auth/logout
#         method: post
#         cors: true
#     - http:
#         path: /auth/logout
#         method: options
#         cors: true

# check-cookie-support:
#   handler: src/functions/auth/check-cookie-support.handler
#   name: ${env:STAGE}-check-cookie-support${env:VERSION}
#   description: Função para verificar suporte a cookies HttpOnly no cliente (DESABILITADO - HttpOnly)
#   memorySize: 128
#   timeout: 30
#   events:
#     - http:
#         path: /auth/check-cookie-support
#         method: get
#         cors: true

# config:
#   handler: src/functions/auth/config.handler
#   name: ${env:STAGE}-auth-config${env:VERSION}
#   description: Função para fornecer configurações de autenticação para o frontend (DESABILITADO - HttpOnly)
#   memorySize: 128
#   timeout: 30
#   events:
#     - http:
#         path: /auth/config
#         method: get
#         cors: true

# cognito-to-cookie:
#   handler: src/functions/auth/cognito-to-cookie.handler
#   name: ${env:STAGE}-cognito-to-cookie${env:VERSION}
#   description: Função para converter token Cognito em cookies HttpOnly seguros (DESABILITADO - HttpOnly)
#   memorySize: 128
#   timeout: 30
#   events:
#     - http:
#         path: /auth/cognito-to-cookie
#         method: post
#         cors: true

