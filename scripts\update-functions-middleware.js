/**
 * Script para atualizar funções para usar middleware correto
 */

const fs = require('fs');
const path = require('path');

// Lista de funções que precisam ser atualizadas
const FUNCTIONS_TO_UPDATE = [
  // DynamoDB
  'src/functions/dynamodb/delete.js',
  'src/functions/dynamodb/readAuditsSwitchRole.js',
  'src/functions/dynamodb/read-paginate.js',
  
  // Customers
  'src/functions/customers/readCustomers.js',
  'src/functions/customers/updateHasActiveContracts.js',
  'src/functions/customers/getCustomers.js',
  
  // Switch Role
  'src/functions/switchRole/invokeStateMachine.js',
  'src/functions/switchRole/checkDaredeFullRole.js',
  'src/functions/switchRole/checkSolicitationExistence.js',
  'src/functions/switchRole/checkIfRoleAdminExists.js',
  
  // Cognito
  'src/functions/cognito/updateStatus.js',
  'src/functions/cognito/updateRole.js',
  
  // Webhook
  'src/functions/webhook/webhookSend.js'
];

function updateFunction(filePath) {
  console.log(`🔧 Atualizando ${filePath}...`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`   ⚠️  Arquivo não encontrado: ${filePath}`);
    return false;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;
  
  // Verificar se já tem os imports necessários
  const hasAuthCorsImport = content.includes('withAuthCors');
  const hasSecureAuthImport = content.includes('withSecureAuth');
  
  if (!hasAuthCorsImport || !hasSecureAuthImport) {
    // Encontrar a linha de imports
    const lines = content.split('\n');
    let importLineIndex = -1;
    
    // Procurar pela última linha de import
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith('import ') || lines[i].startsWith('const ') && lines[i].includes('require(')) {
        importLineIndex = i;
      }
    }
    
    if (importLineIndex >= 0) {
      // Adicionar imports após a última linha de import
      const newImports = [];
      if (!hasAuthCorsImport) {
        newImports.push('import { withAuthCors } from "../../shared/cors/cors-middleware";');
      }
      if (!hasSecureAuthImport) {
        newImports.push('import { withSecureAuth } from "../../shared/auth/auth-middleware-secrets";');
      }
      
      lines.splice(importLineIndex + 1, 0, ...newImports);
      content = lines.join('\n');
      updated = true;
      console.log(`   ✅ Imports adicionados`);
    }
  }
  
  // Verificar se já tem middleware aplicado
  const hasMiddlewareApplied = content.includes('withAuthCors(withSecureAuth(');
  
  if (!hasMiddlewareApplied) {
    // Procurar por export const handler
    const exportHandlerRegex = /export const handler = async \(([^)]*)\) => \{/;
    const match = content.match(exportHandlerRegex);
    
    if (match) {
      const originalHandler = match[0];
      const params = match[1];
      
      // Criar novo nome para o handler
      const functionName = path.basename(filePath, '.js');
      const handlerName = `${functionName}Handler`;
      
      // Substituir export const handler por const handlerName
      content = content.replace(
        exportHandlerRegex,
        `const ${handlerName} = async (${params}) => {`
      );
      
      // Adicionar log de autenticação
      content = content.replace(
        `const ${handlerName} = async (${params}) => {`,
        `const ${handlerName} = async (${params}) => {\n  console.log('${functionName}: Usuário autenticado:', event.user?.email);`
      );
      
      // Adicionar export com middleware no final
      const middlewareExport = `
 
export const handler = withAuthCors(withSecureAuth(${handlerName}, {
  requireAuth: true,
  autoRefresh: true,
  requiredPermissions: [],
  requiredRole: null
}));`;
      
      content += middlewareExport;
      updated = true;
      console.log(`   ✅ Middleware aplicado`);
    } else {
      console.log(`   ⚠️  Padrão de export não encontrado`);
    }
  }
  
  if (updated) {
    fs.writeFileSync(filePath, content);
    console.log(`   ✅ Arquivo atualizado com sucesso`);
    return true;
  } else {
    console.log(`   ℹ️  Arquivo já atualizado`);
    return false;
  }
}

function main() {
  console.log('🚀 Iniciando atualização das funções...\n');
  
  let updatedCount = 0;
  let totalCount = 0;
  
  FUNCTIONS_TO_UPDATE.forEach(filePath => {
    totalCount++;
    if (updateFunction(filePath)) {
      updatedCount++;
    }
    console.log(''); // Linha em branco
  });
  
  console.log('📊 Resumo:');
  console.log(`   Total de funções: ${totalCount}`);
  console.log(`   Funções atualizadas: ${updatedCount}`);
  console.log(`   Funções já atualizadas: ${totalCount - updatedCount}`);
  
  if (updatedCount > 0) {
    console.log('\n✅ Atualização concluída! As funções agora usam middleware correto.');
  } else {
    console.log('\nℹ️  Todas as funções já estavam atualizadas.');
  }
}

main();
