import { getSecretAccounts } from "./secret";

import aws from "aws-sdk";
import AthenaExpress from "athena-express";

import { parseQueryString } from "../../shared/parsers";
import {
  responseWithSuccess,
  responseWithError,
  responseWithBadRequest,
} from "../../shared/response";

function onlyNumber(value) {
  if (value) return value.toString().replace(/\D/gm, "");
  else return "";
}
const defaultCredentials = aws.config.credentials;

export const handler = async (event, context) => {
  try {
    let {
      year,
      month,
      payer,
      account,
      entity = null,
      isMarketPlace = false,
    } = parseQueryString(event);

    const { isInvoke } = event;
    if (isInvoke) {
      year = event.year;
      month = event.month;
      payer = event.payer;
      account = event.account;
      (isMarketPlace = event.isMarketPlace || false),
        (entity = event.entity || null);
    }

    const accountID = account ? onlyNumber(account) : "";

    if (!payer || !onlyNumber(payer))
      return responseWithBadRequest("invalid payer account");

    if (!year || !onlyNumber(year))
      return responseWithBadRequest("invalid year");

    if (!month || !onlyNumber(month))
      return responseWithBadRequest("invalid month");

    const secret = await getSecretAccounts("************");
    console.log("Billing account assume role", secret);
    if (!secret)
      return responseWithBadRequest("payer account is not configured");

    aws.config.update({
      credentials: {
        accessKeyId: secret.access_key_id,
        secretAccessKey: secret.secret_key_id,
        sessionToken: secret.session_token,
      },
      region: process.env.AWS_REGION_LOCATION,
    });

    const athena = new AthenaExpress({
      aws,
      db: `default`,
      s3: `s3://prod-customer-billing-result`,
    });

    const savingsPlan = await getSavingsPlan({
      year,
      month,
      payer,
      account,
      dbConnection: athena,
      db_athena: "customer_billing",
    });

    const taxes = await getTaxes({
      year,
      month,
      payer,
      account,
      dbConnection: athena,
      db_athena: "customer_billing",
    });

    const costsByInvoices = await getCostsByInvoices({
      year,
      month,
      payer,
      account,
      dbConnection: athena,
      db_athena: "customer_billing",
      isMarketPlace: false,
    });

    const costsByInvoicesMarketPlace = await getCostsByInvoices({
      year,
      month,
      payer,
      account,
      dbConnection: athena,
      db_athena: "customer_billing",
      isMarketPlace: true,
    });

    console.log(savingsPlan);
    console.log(costsByInvoices);

    costsByInvoices.forEach((invoice) => {
      const invoiceID = invoice["bill_invoice_id"];
      const savingsFromInvoice = savingsPlan.filter(
        (s) => s.bill_invoice_id === invoiceID
      );
      const totalSavingsFromInvoice = savingsFromInvoice.reduce(
        (total, saving) => total + saving.cost,
        0
      );

      const taxesFromInvoice = taxes.filter(
        (t) => t.bill_invoice_id === invoiceID
      );
      const totalTaxesFromInvoice = taxesFromInvoice.reduce(
        (total, tax) => total + tax.cost,
        0
      );

      invoice["cost"] += totalSavingsFromInvoice - totalTaxesFromInvoice;
    });

    let whereCoditions = [];
    whereCoditions.push(`AND year='${year}'`);
    whereCoditions.push(`AND account = '${payer}'`);
    whereCoditions.push(`AND month='${month}'`);
    whereCoditions.push(`AND bill_bill_type <> 'Refund'`);

    if (accountID)
      whereCoditions.push(`AND line_item_usage_account_id = '${accountID}'`);
    else whereCoditions.push(`AND bill_payer_account_id = '${payer}'`);

    if (isMarketPlace == "true")
      whereCoditions.push(
        `AND line_item_line_item_description LIKE '%Marketplace%'`
      );
    else
      whereCoditions.push(
        `AND line_item_line_item_description NOT LIKE '%Marketplace%'`
      );

    if (entity) whereCoditions.push(`AND line_item_legal_entity = '${entity}'`);

    let query = "";
    let queryString = "";

    try {
      console.log(`Querying billing with spp discount`);

      queryString = `
            SELECT * FROM
            (
                SELECT
                    CASE 
                        WHEN product_product_name IS NULL THEN line_item_product_code 
                        WHEN product_product_name = '' THEN line_item_product_code 
                        ELSE product_product_name
                    END AS service_name,
                    product_location AS location,
                    ROUND(SUM(COALESCE(line_item_unblended_cost, 0)), 2) - SUM(COALESCE(discount_spp_discount, 0)) AS cost,
                    ROUND(SUM(savings_plan_total_commitment_to_date), 2) AS savings_plan,
                    line_item_line_item_description AS description,
                    ${
                      accountID
                        ? "line_item_legal_entity AS entity,"
                        : `'AWS' AS entity,`
                    }
                    month
                FROM customer_billing
                WHERE 1=1
                    ${whereCoditions.join(" ")}
                GROUP BY
                    line_item_line_item_description,
                    month,
                    product_location,
                    line_item_product_code,
                    ${accountID ? "line_item_legal_entity," : ``}
                    product_product_name
            )
            WHERE cost <> 0
            ORDER BY service_name;
        `;

      console.log(queryString);

      query = await athena.query(queryString);
    } catch (error) {
      console.log(error);
      console.log(`Querying billing without spp discount`);

      queryString = `
            SELECT * FROM
            (
                SELECT
                    CASE 
                        WHEN product_product_name IS NULL THEN line_item_product_code 
                        WHEN product_product_name = '' THEN line_item_product_code 
                        ELSE product_product_name
                    END AS service_name,
                    product_location AS location,
                    ROUND(SUM(COALESCE(line_item_unblended_cost, 0)), 2) AS cost,
                    ROUND(SUM(savings_plan_total_commitment_to_date), 2) AS savings_plan,
                    line_item_line_item_description AS description,
                    'AWS' AS entity,
                    month
                FROM customer_billing
                WHERE 1=1
                    ${whereCoditions.join(" ")}
                GROUP BY 
                    line_item_product_code,
                    product_product_name,
                    line_item_line_item_description,
                    month,
                    product_location
            )
            WHERE cost <> 0
            ORDER BY service_name;
        `;

      query = await athena.query(queryString);

      console.log(queryString);
    }

    let services = [];
    if (query.Items.length > 0) {
      services = query.Items.reduce((arr, item) => {
        let service_name = item.service_name;
        let entity = item.entity;

        if (
          service_name
            .toUpperCase()
            .includes("ComputeSavingsPlans".toUpperCase())
        ) {
          service_name = "Savings Plans for AWS Compute usage";
        }

        if (item.description.toUpperCase().includes("DATA TRANSFER"))
          service_name = "Data Transfer";

        // if (item.description.toUpperCase().includes("TAX FOR") || item.description.toUpperCase().includes("SPP"))
        //     return arr

        const servicePosition = arr.findIndex(
          (s) => s.name === service_name && s.entity === entity
        );
        const location = item.location ? item.location : "No region";

        if (servicePosition > -1) {
          const service = arr[servicePosition];
          const regionPosition = service.regions.findIndex(
            (r) => r.location === location
          );

          let regions = service.regions;
          if (regionPosition > -1) {
            let region = regions[regionPosition];

            region.cost += Number(item.cost);
            region.savings_plan += Number(item.savings_plan);

            region.items.push({
              description: item.description,
              cost: Number(item.cost),
              savings_plan: Number(item.savings_plan),
            });

            regions[regionPosition] = region;
          } else {
            regions.push({
              location: location,
              cost: Number(item.cost),
              savings_plan: Number(item.savings_plan),
              items: [
                {
                  description: item.description,
                  cost: Number(item.cost),
                  savings_plan: Number(item.savings_plan),
                },
              ],
            });
          }

          service.cost += Number(item.cost);
          service.savings_plan += Number(item.savings_plan);
          service.regions = regions;
          arr[servicePosition] = service;

          return arr;
        }

        const service = {
          name: service_name,
          cost: Number(item.cost),
          savings_plan: Number(item.savings_plan),
          month: item.month,
          entity: entity,
          regions: [
            {
              location: location,
              cost: Number(item.cost),
              savings_plan: Number(item.savings_plan),
              items: [
                {
                  description: item.description,
                  cost: Number(item.cost),
                  savings_plan: Number(item.savings_plan),
                },
              ],
            },
          ],
        };

        arr.push(service);

        return arr;
      }, []);
    }

    services.sort((a, b) => a.name.localeCompare(b.name));

    let totalTaxes = taxes.reduce((total, tax) => (total += tax.cost), 0);
    let totalCostByAccount = services.reduce(
      (total, service) => total + service.cost,
      0
    );
    let totalSavingsPlanByAccount = savingsPlan.reduce(
      (total, savings) => total + savings.cost,
      0
    );
    let totalMarketPlace = costsByInvoicesMarketPlace.reduce(
      (total, market) => total + market.cost,
      0
    );

    async function filterSPPFromServices(services) {
      let regionsToUpdate = [];

      services.forEach((service) => {
        service.regions.forEach((region, j) => {
          region.items.forEach((item, i) => {
            if (item.description.includes("SPP")) {
              region.items.splice(i, 1);
            }
            if (region.items.length === 0) {
              service.regions.splice(j, 1);
            }
          });
        });
        if (service.regions.length > 0) regionsToUpdate.push(service);
      });

      return regionsToUpdate;
    }

    services = await filterSPPFromServices(services);

    if (totalCostByAccount < 0) totalCostByAccount = 0;
    assumeDSMAccount();
    return responseWithSuccess(
      {
        payer: payer,
        account: account,
        taxes: totalTaxes,
        services: services,
        cost: totalCostByAccount,
        costsByInvoices: costsByInvoices,
        savings_plan: totalSavingsPlanByAccount,
        costWithMarketPlace: totalCostByAccount + totalMarketPlace,
        costWithoutDiscount: totalCostByAccount + totalSavingsPlanByAccount,
        costsByInvoicesMarketPlace: costsByInvoicesMarketPlace,
        costWithoutTaxes:
          totalCostByAccount - totalTaxes + totalSavingsPlanByAccount,
      },
      "Query successfully"
    );
  } catch (error) {
    assumeDSMAccount();
    console.log(error);
    return responseWithError(error);
  }
};

async function getSavingsPlan({ dbConnection, year, month, payer, db_athena }) {
  try {
    let savings = [];

    let query = await dbConnection.query(`
        SELECT
            ROUND(SUM(COALESCE(line_item_unblended_cost, 0)), 2) AS cost,
            savings_plan_offering_type as service_name,
            month,
            bill_invoice_id
        FROM ${db_athena}
        WHERE 1=1
            AND account = '${payer}'
            AND year='${year}'
            AND month='${month}'
            AND bill_payer_account_id = '${payer}'
            AND line_item_line_item_type LIKE '%SavingsPlanNegation%'
            AND bill_bill_type <> 'Refund'
        GROUP BY 
            line_item_line_item_type,
            savings_plan_offering_type,
            month,
            bill_invoice_id
        `);

    if (query.Items.length > 0) {
      query.Items.forEach((item) => {
        let service_name = item.service_name;
        let entity = "Savings Plans";

        // ComputeSavingsPlans = Charges covered by Compute Savings Plans in the AWS Billing
        // EC2InstanceSavingsPlans = Charges covered by EC2 Instance Savings Plans in the AWS Billing
        if (
          service_name === "ComputeSavingsPlans" &&
          service_name === "EC2InstanceSavingsPlans"
        )
          return;

        const positiveCost = item.cost * -1;

        savings.push({
          name: service_name,
          cost: positiveCost,
          savings_plan: 0,
          month: item.month,
          entity: entity,
          regions: [],
          bill_invoice_id: item.bill_invoice_id,
        });
      });
    }

    return savings;
  } catch (error) {
    return [];
  }
}

async function getTaxes({ dbConnection, year, month, payer, db_athena }) {
  let taxes = [];

  let query = await dbConnection.query(`
        SELECT
            ROUND(SUM(COALESCE(line_item_unblended_cost, 0)), 2) AS cost,
            'Tax to be collected' AS service_name,
            bill_invoice_id
        FROM ${db_athena}
        WHERE 1=1
        AND account = '${payer}'
            AND year='${year}'
            AND month='${month}'
            AND bill_payer_account_id = '${payer}'
            AND line_item_line_item_description LIKE '%Tax for product code%'
            AND bill_bill_type <> 'Refund'
        GROUP BY 
            line_item_line_item_type,
            bill_invoice_id
        `);

  if (query.Items.length > 0) {
    query.Items.forEach((item) => {
      let service_name = item.service_name;
      let entity = "Taxes";

      taxes.push({
        name: service_name,
        cost: item.cost,
        savings_plan: 0,
        month: item.month,
        entity: entity,
        regions: [],
        bill_invoice_id: item.bill_invoice_id,
      });
    });
  }

  return taxes;
}

async function getCostsByInvoices({
  dbConnection,
  year,
  month,
  payer,
  db_athena,
  isMarketPlace,
}) {
  try {
    let query = await dbConnection.query(`
        SELECT
            ROUND(SUM(COALESCE(line_item_unblended_cost, 0)), 2) - SUM(COALESCE(discount_spp_discount, 0)) AS cost,
            month,
            bill_invoice_id
        FROM ${db_athena}
        WHERE 1=1
        AND account = '${payer}'
            AND year='${year}'
            AND month='${month}'
            AND bill_payer_account_id = '${payer}'
            AND bill_bill_type <> 'Refund'
            ${
              isMarketPlace
                ? `AND line_item_line_item_description LIKE '%Marketplace%'`
                : `AND line_item_line_item_description NOT LIKE '%Marketplace%'`
            } 
        GROUP BY
            month,
            bill_invoice_id
    `);

    return query.Items;
  } catch (error) {
    return [];
  }
}

function assumeDSMAccount() {
  aws.config.update({
    credentials: {
      accessKeyId: defaultCredentials.accessKeyId,
      sessionToken: defaultCredentials.sessionToken,
      secretAccessKey: defaultCredentials.secretAccessKey,
    },
    region: aws.config.region,
  });
}
