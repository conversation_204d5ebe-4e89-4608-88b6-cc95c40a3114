/**
 * Utilitário para detectar e configurar suporte a cookies HttpOnly no frontend
 * Use este arquivo no seu projeto React/Frontend
 */

/**
 * Detecta suporte a cookies HttpOnly e configura Axios automaticamente
 * @param {Object} axiosInstance - Instância do Axios para configurar
 * @param {string} baseURL - URL base da API (ex: 'https://api.dsm.darede.com.br')
 * @returns {Promise<Object>} Configuração detectada
 */
export async function detectAndConfigureAuth(axiosInstance, baseURL) {
  console.log('🔍 Detectando suporte a cookies HttpOnly...');
  
  try {
    // 1. Verificar configurações do servidor
    const configResponse = await axiosInstance.get(`${baseURL}/dev/auth/config`);
    const serverConfig = configResponse.data;
    
    console.log('📋 Configurações do servidor:', {
      httpOnlySupported: serverConfig.auth.httpOnlySupported,
      withCredentials: serverConfig.auth.withCredentials,
      tokenStorage: serverConfig.auth.tokenStorage
    });
    
    // 2. Testar suporte a cookies
    const cookieTestResponse = await axiosInstance.get(`${baseURL}/dev/auth/check-cookie-support`);
    const cookieTest = cookieTestResponse.data;
    
    console.log('🍪 Teste de cookies:', {
      httpOnlySupported: cookieTest.httpOnlySupported,
      cookieTestSet: cookieTest.cookieTestSet
    });
    
    // 3. Determinar configuração final
    const shouldUseCookies = serverConfig.auth.httpOnlySupported && 
                            cookieTest.httpOnlySupported && 
                            cookieTest.cookieTestSet;
    
    // 4. Configurar Axios
    if (shouldUseCookies) {
      axiosInstance.defaults.withCredentials = true;
      console.log('✅ Configurado para usar cookies HttpOnly');
      console.log('   withCredentials: true');
    } else {
      axiosInstance.defaults.withCredentials = false;
      console.log('⚠️ Fallback para Authorization headers');
      console.log('   withCredentials: false');
    }
    
    // 5. Retornar configuração
    const finalConfig = {
      httpOnlySupported: shouldUseCookies,
      withCredentials: shouldUseCookies,
      tokenStorage: shouldUseCookies ? 'cookies' : 'localStorage',
      fallbackToHeaders: !shouldUseCookies,
      serverConfig,
      cookieTest
    };
    
    console.log('🎯 Configuração final:', finalConfig);
    
    return finalConfig;
    
  } catch (error) {
    console.error('❌ Erro ao detectar suporte a cookies:', error);
    
    // Fallback seguro
    axiosInstance.defaults.withCredentials = false;
    
    return {
      httpOnlySupported: false,
      withCredentials: false,
      tokenStorage: 'localStorage',
      fallbackToHeaders: true,
      error: error.message
    };
  }
}

/**
 * Configura interceptors do Axios para logging de debug
 * @param {Object} axiosInstance - Instância do Axios
 * @param {Object} authConfig - Configuração de autenticação
 */
export function setupAxiosInterceptors(axiosInstance, authConfig) {
  // Request interceptor para logging
  axiosInstance.interceptors.request.use(
    (config) => {
      const isInternal = config.url.includes('api.dsm.darede.com.br');
      
      console.log('[DEBUG] Configuração da requisição:', {
        url: config.url,
        isInternal,
        httpOnlySupported: authConfig.httpOnlySupported,
        withCredentials: config.withCredentials || axiosInstance.defaults.withCredentials,
        method: config.method.toUpperCase()
      });
      
      return config;
    },
    (error) => {
      console.error('[DEBUG] Erro na requisição:', error);
      return Promise.reject(error);
    }
  );
  
  // Response interceptor para refresh automático
  axiosInstance.interceptors.response.use(
    (response) => {
      return response;
    },
    async (error) => {
      const originalRequest = error.config;
      
      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;
        
        try {
          console.log('🔄 Token expirado, tentando renovar...');
          
          if (authConfig.httpOnlySupported) {
            // Usar endpoint de refresh com cookies
            await axiosInstance.post('/dev/auth/refresh');
            console.log('✅ Token renovado via cookies');
          } else {
            // Implementar refresh com localStorage se necessário
            console.log('⚠️ Refresh via localStorage não implementado');
            throw new Error('Token expirado');
          }
          
          // Repetir requisição original
          return axiosInstance(originalRequest);
          
        } catch (refreshError) {
          console.error('❌ Erro ao renovar token:', refreshError);
          
          // Redirecionar para login
          if (typeof window !== 'undefined') {
            window.location.href = '/login';
          }
          
          return Promise.reject(refreshError);
        }
      }
      
      return Promise.reject(error);
    }
  );
}

/**
 * Função principal para inicializar autenticação
 * Use esta função no início da sua aplicação
 * @param {Object} axiosInstance - Instância do Axios
 * @param {string} baseURL - URL base da API
 * @returns {Promise<Object>} Configuração de autenticação
 */
export async function initializeAuth(axiosInstance, baseURL = 'https://pdu9dbemj1.execute-api.us-east-1.amazonaws.com/dev') {
  console.log('🚀 Inicializando sistema de autenticação...');
  
  try {
    // 1. Detectar e configurar suporte a cookies
    const authConfig = await detectAndConfigureAuth(axiosInstance, baseURL);
    
    // 2. Configurar interceptors
    setupAxiosInterceptors(axiosInstance, authConfig);
    
    // 3. Salvar configuração globalmente (opcional)
    if (typeof window !== 'undefined') {
      window.__AUTH_CONFIG__ = authConfig;
    }
    
    console.log('✅ Sistema de autenticação inicializado com sucesso');
    
    return authConfig;
    
  } catch (error) {
    console.error('❌ Erro ao inicializar autenticação:', error);
    throw error;
  }
}

/**
 * Hook React para usar a configuração de autenticação
 * @returns {Object} Configuração de autenticação
 */
export function useAuthConfig() {
  if (typeof window !== 'undefined' && window.__AUTH_CONFIG__) {
    return window.__AUTH_CONFIG__;
  }
  
  return {
    httpOnlySupported: false,
    withCredentials: false,
    tokenStorage: 'localStorage',
    fallbackToHeaders: true
  };
}
