import { STS } from "aws-sdk";

const region = process.env.AWS_REGION_LOCATION;

export const checkDaredeFullRole = async (account) => {
  try {
    const sts = new STS({ region });

    // Assume a role na conta raiz
    const credentialsRootAccount = await sts
      .assumeRole({
        RoleArn: process.env.ARN_JUMP_ACCESS,
        RoleSessionName: `darede-${process.env.ROOT_ACCOUNT_ID}`,
      })
      .promise();

    const stsRootAccount = new STS({
      region,
      credentials: {
        accessKeyId: credentialsRootAccount.Credentials.AccessKeyId,
        secretAccessKey: credentialsRootAccount.Credentials.SecretAccessKey,
        sessionToken: credentialsRootAccount.Credentials.SessionToken,
      },
    });

    // Tenta assumir a role 'darede-full' na conta do cliente
    await stsRootAccount
      .assumeRole({
        RoleArn: `arn:aws:iam::${account}:role/${process.env.ROLE_NAME}`,
        RoleSessionName: `darede-${account}`,
      })
      .promise();

    return {
      success: true,
      message: "A role darede-full existe e pode ser assumida.",
    };
  } catch (error) {
    return {
      success: false,
      message: "A role darede-full NÃO existe ou não pode ser assumida.",
      error: error.message,
    };
  }
};
