# Environment Configuration - Local Development
NODE_ENV=development
STAGE=local

# API Configuration
API_NAME=dsm-back-end
VERSION=v1
LOCAL_PORT=8000

# AWS Configuration
AWS_REGION_LOCATION=us-east-1
ACCOUNT_ID=************

# AWS Secrets Manager ARNs
JWT_DECRIPTION_CREDENTIALS=arn:aws:secretsmanager:us-east-1:************:secret:dsm-token-encription-credentials-Fk8enl
DSM_API_SECREAT_MANAGER=arn:aws:secretsmanager:us-east-1:************:secret:dsm-api-credentials-XXXXX
DSM_API_WAF_ARN=arn:aws:secretsmanager:us-east-1:************:secret:dsm-waf-token-XXXXX

# Cognito Configuration
USER_POOL_ID=us-east-1
AWS_API_GATEWAY_COGNITO_NAME=dsm-cognito-authorizer

# Development Features
ENABLE_DEBUG_LOGS=true
ENABLE_DETAILED_LOGGING=true
ENABLE_CORS_DEBUG=true

# External APIs (usando dev para testes locais)
DSM_API_URL=https://api.dev.dsm.com.br
