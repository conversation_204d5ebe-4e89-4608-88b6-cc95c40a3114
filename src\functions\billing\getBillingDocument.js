import { parseQueryString, parsePath } from '../../shared/parsers'
import {
    responseWithSuccess,
    responseWithError,
    responseWithBadRequest,
    responseWithNotFound
} from '../../shared/response'

import { makeS3 } from '../../shared/services/s3-service'
import { makeLambda } from '../../shared/services/lambda-service'

const s3 = makeS3()
const lambda = makeLambda()

const API_NAME = process.env.API_NAME
const REPORTS_BUCKET_NAME = process.env.REPORTS_BUCKET_NAME

function onlyNumber(value) {
    const newValue = value ? value.toString() : ''

    return newValue.replace(/\D/gm, "")
}

export const handler = async (event, context) => {
    try {
        let { year, month } = parseQueryString(event)
        let { payer , fileFormat} = parsePath(event)
        let checkFileFormat = fileFormat == "excel" || fileFormat == "pdf"

        if(fileFormat == 'excel')
            fileFormat = 'xlsx'

        if (event.isInvoke) {
            year = event.year
            month = event.month
            payer = event.payer
        }

        if(!checkFileFormat)
            return responseWithBadRequest('invalid file format')

        if (!payer || !onlyNumber(payer))
            return responseWithBadRequest('invalid payer account')

        if (!year || !onlyNumber(year))
            return responseWithBadRequest('invalid year')

        if (!month || !onlyNumber(month))
            return responseWithBadRequest('invalid month')

        month = Number(month)
        if (month > 12)
            return responseWithBadRequest('invalid month')

        const fileKey = `billing/${year}-${month}/${payer}.${fileFormat}`
        let url = await getFileOnBucket({ fileKey })

        if (url){
            return responseWithSuccess({ url }, 'Query successfully')
        }else{
            return responseWithNotFound(`${payer}.${fileFormat} does not exist`)
        }

      
    } catch (error) {
        console.log(error)
        return responseWithError(error)
    }
}

async function getFileOnBucket({ fileKey }) {
    try {
        const metadata = await s3.headObject({
            Bucket: REPORTS_BUCKET_NAME,
            Key: fileKey
        }).promise()

        if (metadata) {
            return await s3.getSignedUrlPromise('getObject', {
                Bucket: REPORTS_BUCKET_NAME,
                Key: fileKey
            })
        }

        return null

    } catch (error) {
        return null
    }
}

 