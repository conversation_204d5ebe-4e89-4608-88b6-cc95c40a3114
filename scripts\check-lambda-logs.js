#!/usr/bin/env node

/**
 * Script para verificar logs do CloudWatch da função set-token
 */

const AWS = require('aws-sdk');

// Configuração do AWS SDK
AWS.config.update({
  region: process.env.AWS_REGION_LOCATION || 'us-east-1'
});

const cloudWatchLogs = new AWS.CloudWatchLogs();

/**
 * Busca logs recentes da função set-token
 */
async function getSetTokenLogs() {
  try {
    console.log('🔍 Buscando logs da função set-token...\n');
    
    const logGroupName = '/aws/lambda/dev-set-token';
    
    // Busca streams de log recentes
    const streams = await cloudWatchLogs.describeLogStreams({
      logGroupName,
      orderBy: 'LastEventTime',
      descending: true,
      limit: 5
    }).promise();
    
    console.log(`📋 Encontrados ${streams.logStreams.length} streams de log`);
    
    for (const stream of streams.logStreams) {
      console.log(`\n📄 Stream: ${stream.logStreamName}`);
      console.log(`   Última atividade: ${new Date(stream.lastEventTime).toISOString()}`);
      
      // Busca eventos do stream
      const events = await cloudWatchLogs.getLogEvents({
        logGroupName,
        logStreamName: stream.logStreamName,
        startTime: Date.now() - (30 * 60 * 1000), // Últimos 30 minutos
        limit: 50
      }).promise();
      
      console.log(`   Eventos encontrados: ${events.events.length}`);
      
      // Mostra eventos recentes
      events.events.forEach(event => {
        const timestamp = new Date(event.timestamp).toISOString();
        const message = event.message.trim();
        
        if (message.includes('ERROR') || message.includes('Error') || message.includes('error')) {
          console.log(`   ❌ [${timestamp}] ${message}`);
        } else if (message.includes('WARN') || message.includes('Warning')) {
          console.log(`   ⚠️ [${timestamp}] ${message}`);
        } else if (message.includes('START') || message.includes('END') || message.includes('REPORT')) {
          console.log(`   ℹ️ [${timestamp}] ${message}`);
        } else {
          console.log(`   📝 [${timestamp}] ${message}`);
        }
      });
    }
    
  } catch (error) {
    console.error('❌ Erro ao buscar logs:', error.message);
    
    if (error.code === 'ResourceNotFoundException') {
      console.log('\n💡 Possíveis causas:');
      console.log('- Função não foi deployada ainda');
      console.log('- Nome da função está incorreto');
      console.log('- Função não foi executada recentemente');
    }
  }
}

/**
 * Busca logs de erro específicos
 */
async function searchErrorLogs() {
  try {
    console.log('\n🔍 Buscando erros específicos...\n');
    
    const logGroupName = '/aws/lambda/dev-set-token';
    
    // Busca por padrões de erro
    const errorPatterns = [
      'ERROR',
      'Error',
      'error',
      'Exception',
      'Cannot find module',
      'jwt-utils-secrets',
      'Secrets Manager',
      'Internal server error'
    ];
    
    for (const pattern of errorPatterns) {
      try {
        const result = await cloudWatchLogs.filterLogEvents({
          logGroupName,
          filterPattern: pattern,
          startTime: Date.now() - (60 * 60 * 1000), // Última hora
          limit: 10
        }).promise();
        
        if (result.events.length > 0) {
          console.log(`🔍 Padrão "${pattern}" encontrado (${result.events.length} eventos):`);
          result.events.forEach(event => {
            const timestamp = new Date(event.timestamp).toISOString();
            console.log(`   [${timestamp}] ${event.message.trim()}`);
          });
          console.log('');
        }
      } catch (err) {
        // Ignora erros de padrão específico
      }
    }
    
  } catch (error) {
    console.error('❌ Erro ao buscar logs de erro:', error.message);
  }
}

/**
 * Verifica se a função existe
 */
async function checkFunctionExists() {
  try {
    console.log('🔍 Verificando se a função existe...\n');
    
    const lambda = new AWS.Lambda();
    
    const functionName = 'dev-set-token';
    
    const func = await lambda.getFunction({
      FunctionName: functionName
    }).promise();
    
    console.log('✅ Função encontrada:');
    console.log(`   Nome: ${func.Configuration.FunctionName}`);
    console.log(`   Runtime: ${func.Configuration.Runtime}`);
    console.log(`   Última modificação: ${func.Configuration.LastModified}`);
    console.log(`   Timeout: ${func.Configuration.Timeout}s`);
    console.log(`   Memory: ${func.Configuration.MemorySize}MB`);
    
    if (func.Configuration.Layers) {
      console.log(`   Layers: ${func.Configuration.Layers.length}`);
      func.Configuration.Layers.forEach(layer => {
        console.log(`     - ${layer.Arn}`);
      });
    } else {
      console.log('   ⚠️ Nenhuma layer configurada');
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ Função não encontrada:', error.message);
    return false;
  }
}

/**
 * Função principal
 */
async function main() {
  console.log('🚀 DIAGNÓSTICO DE LOGS DA FUNÇÃO SET-TOKEN');
  console.log('==========================================\n');
  
  // Verifica se a função existe
  const functionExists = await checkFunctionExists();
  
  if (!functionExists) {
    console.log('\n❌ Função não encontrada. Verifique se o deploy foi bem-sucedido.');
    return;
  }
  
  // Busca logs recentes
  await getSetTokenLogs();
  
  // Busca erros específicos
  await searchErrorLogs();
  
  console.log('\n📊 RESUMO:');
  console.log('- Verifique os logs acima para identificar o erro específico');
  console.log('- Procure por mensagens de erro relacionadas a:');
  console.log('  * Secrets Manager');
  console.log('  * jwt-utils-secrets');
  console.log('  * Cannot find module');
  console.log('  * Timeout');
  console.log('  * Memory');
}

// Executa apenas se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
}
