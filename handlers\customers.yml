read-customer:
  handler: src/functions/customers/readCustomers.handler
  name: ${env:STAGE}-read-customer${env:VERSION}
  description: Função para retornar um cliente com base no status
  memorySize: 128
  timeout: 900
  events:
    - http:
        path: /customers/read/{form}
        method: get
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

update-has-active-contracts:
  handler: src/functions/customers/updateHasActiveContracts.handler
  name: ${env:STAGE}-update-has-active-contracts${env:VERSION}
  description: Função para retornar um cliente com base no status
  memorySize: 128
  timeout: 900
  events:
    - http:
        path: /customers/updateHasActiveContracts/{customerId}
        method: put
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

read-customerV2:
  handler: src/functions/customers/getCustomers.handler
  name: ${env:STAGE}-read-customers${env:VERSION}
  description: Função para leitura de dados de customers da tabela do DynamoDB
  memorySize: 128
  events:
    - http:
        path: /read/customers
        method: get
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}


