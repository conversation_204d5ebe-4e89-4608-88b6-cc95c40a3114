import { isWithinInterval, format } from "date-fns";
import { v4 } from "uuid";
import { put } from "../model/dynamo";

export class InvoiceEntity {
  _clientDynamo = null;
  constructor(clientDynamo) {
    this._clientDynamo = clientDynamo;
  }

  validateParams({ month, year }) {
    if (typeof month !== "number") throw new Error("Month is not a number");
    else if (month < 1 || month > 12) throw new Error("Month is invalid");

    if (typeof year !== "number") throw new Error("Year is invalid");

    return true;
  }

  async getInvoices(input) {
    console.log(input);
    try {
      const scanResults = { Items: [] };
      let items;

      do {
        items = await this._clientDynamo.query(input).promise();
        items.Items.forEach((item) => scanResults.Items.push(item));
        input.ExclusiveStartKey = items.LastEvaluatedKey;
      } while (typeof items.LastEvaluatedKey !== "undefined");

      return scanResults;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  formatInvoicesParameters(queryParams) {
    if (!queryParams) throw new Error("queryParams is required");
    let params = {
      TableName: `${process.env.FINOPS_STAGE}-finops`,
      IndexName: "full_date-index",
      ExpressionAttributeNames: {
        "#full_date": "full_date",
      },
      KeyConditionExpression: "#full_date = :full_date",
      ExpressionAttributeValues: {
        ":full_date": queryParams,
      },
    };
    return params;
  }

  formatInvoicesParametersByPayerAccount(payerAccount) {
    if (!payerAccount) {
      throw new Error("payerAccount is required");
    }

    let params = {
      TableName: `${process.env.FINOPS_STAGE}-finops`,
      IndexName: "payer_account-index",
      KeyConditionExpression: "#payer_account = :payer_account",
      ExpressionAttributeNames: {
        "#payer_account": "payer_account",
      },
      ExpressionAttributeValues: {
        ":payer_account": payerAccount,
      },
    };
    return params;
  }

  parseYearMonth(dateString) {
    const [year, month] = dateString.split("-");
    return new Date(parseInt(year), parseInt(month) - 1);
  }

  filterInvoicesByRange(startDate, endDate, invoiceList) {
    const startInterval = this.parseYearMonth(startDate);
    const endInterval = this.parseYearMonth(endDate);

    const invoicesFiltered = invoiceList.Items.filter((item) => {
      const invoiceDate = this.parseYearMonth(item.full_date);
      return isWithinInterval(invoiceDate, {
        start: startInterval,
        end: endInterval,
      });
    });

    return { Items: invoicesFiltered };
  }

  formatInvoices(query, body) {
    const invoices = query.Items.reduce((invoiceList, invoice) => {
      const existIndex = invoiceList.findIndex(
        (item) => item.payer_account === invoice.bill_payer_account_id
      );

      const savingPlans = invoice.savings || 0;
      const bundled_discount = invoice.discount_bundled_discount || 0;
      const edp_discount = invoice.discount_edp_discount || 0;
      const credit = invoice.credit || 0;
      const spp_discount = invoice.discount_spp_discount || 0;
      const tax = invoice.tax || 0;
      let otherDiscounts = 0;

      let total_pos = 0;
      let total_neg = 0;
      let charges = 0;

      if (invoice.unblended_cost > 0) total_pos = invoice.unblended_cost;
      else total_neg = invoice.unblended_cost;

      if (invoice.unblended_cost > 0 && invoice.bill_billing_entity === "AWS")
        charges = invoice.unblended_cost - tax;

      if (invoice.line_item_line_item_type.toLowerCase().includes("discount")) {
        if (
          !invoice.line_item_line_item_type.toLowerCase().includes("edp") &&
          !invoice.line_item_line_item_type.toLowerCase().includes("bundled") &&
          !invoice.line_item_line_item_type.toLowerCase().includes("spp")
        ) {
          otherDiscounts = invoice.unblended_cost;
        }
      }

      const account = {
        invoice_id: invoice.bill_invoice_id || "",
        payer_account_id: invoice.bill_payer_account_id || "",
        sub_account: invoice.line_item_usage_account_id,
        cost: invoice.unblended_cost,
        charges: charges,
        spp_discount: invoice.discount_spp_discount || 0,
        edp_discount: invoice.discount_edp_discount || 0,
        bundled_discount: invoice.discount_bundled_discount || 0,
        credit: invoice.credit || 0,
        savingPlans: invoice.savings || 0,
        tax: invoice.tax || 0,
        total_discount:
          Math.abs(invoice.discount_spp_discount || 0) +
          Math.abs(invoice.discount_edp_discount || 0) +
          Math.abs(invoice.discount_bundled_discount || 0) +
          Math.abs(invoice.credit || 0) +
          Math.abs(invoice.savings || 0) +
          Math.abs(otherDiscounts),
        legal_entity: invoice.line_item_legal_entity,
        line_item_type: invoice.line_item_line_item_type,
        bill_entity: invoice.bill_billing_entity,
        other_discounts: otherDiscounts || 0,
      };

      if (existIndex < 0) {
        invoiceList.push({
          id: v4(),
          full_date: `${parseInt(body.year)}-${parseInt(body.month)}`,
          year: parseInt(body.year),
          month: parseInt(body.month),
          total_pos: total_pos,
          total_neg: total_neg,
          total_spp_discount: spp_discount ? spp_discount : 0,
          total_edp_discount: invoice.discount_edp_discount
            ? invoice.discount_edp_discount
            : 0,
          total_bundled_discount: invoice.discount_bundled_discount
            ? invoice.discount_bundled_discount
            : 0,
          total_credit: credit ? credit : 0,
          total_saving_plans: invoice.savings,
          total_discount:
            Math.abs(spp_discount) +
            Math.abs(edp_discount) +
            Math.abs(bundled_discount) +
            Math.abs(credit) +
            Math.abs(savingPlans) +
            Math.abs(otherDiscounts),
          total_tax: invoice.tax,
          total_charges_AWS: charges,
          accounts: [account],
          created_at: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
          updated_at: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
          invoice_id: invoice.bill_invoice_id,
          payer_account: invoice.bill_payer_account_id,
          other_discounts: otherDiscounts,
        });
      } else {
        invoiceList[existIndex].total_credit += credit;
        invoiceList[existIndex].total_pos += total_pos;
        invoiceList[existIndex].total_neg += total_neg;
        invoiceList[existIndex].total_discount +=
          Math.abs(spp_discount) +
          Math.abs(edp_discount) +
          Math.abs(bundled_discount) +
          Math.abs(credit) +
          Math.abs(savingPlans) +
          Math.abs(otherDiscounts);
        invoiceList[existIndex].total_saving_plans += savingPlans;
        invoiceList[existIndex].total_spp_discount += spp_discount;
        invoiceList[existIndex].total_edp_discount += edp_discount;
        invoiceList[existIndex].total_bundled_discount += bundled_discount;
        invoiceList[existIndex].total_tax += tax;
        invoiceList[existIndex].total_charges_AWS += charges;
        invoiceList[existIndex].other_discounts += otherDiscounts;

        invoiceList[existIndex].accounts.push(account);
      }

      return invoiceList;
    }, []);

    return invoices;
  }

  async update(id, item) {
    const obj = {};
    Object.entries(item).forEach((o) => {
      if (o[0] !== "id" && (o[1] || typeof o[1] === "number")) {
        obj[o[0]] = { Action: "PUT", Value: o[1] };
      }
    });

    return await put(`${process.env.FINOPS_STAGE}-finops`, id, obj);
  }
}
