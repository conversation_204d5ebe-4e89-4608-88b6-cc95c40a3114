billing-data:
  handler: src/functions/billing/dataLoad.handler
  name: ${env:STAGE}-billing-data${env:VERSION}
  description: Função para carregar os dados de billing a cada 6h
  memorySize: 128
  timeout: 900
  events:
    - http:
        path: /billing/event/data
        method: post
        cors: true
    - schedule:
        rate: rate(1 hour)
        enabled: true

one-customer-check:
  handler: src/functions/billing/oneCustomer.handler
  name: ${env:STAGE}-one-customer-check${env:VERSION}
  description: Função para ler os dados de billing de 1 cliente
  memorySize: 128
  events:
    - http:
        path: /billing/one/customer
        method: post
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

get-billing-document:
  handler: src/functions/billing/getBillingDocument.handler
  name: ${env:STAGE}-get-billing-document${env:VERSION}
  description: Função para retornar documento PDF ou Excel de 1 cliente
  memorySize: 128
  events:
    - http:
        path: /billing/get/{payer}/{fileFormat}
        method: get
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

get-account-cost-by-service:
  handler: src/functions/billing/accountCostByService.handler
  name: ${env:STAGE}-get-account-cost-by-service${env:VERSION}
  description: Função para retornar os custos de uma conta por servicos
  memorySize: 128
  events:
    - http:
        path: /billing/service/cost
        method: get
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

get-account-costs-by-payer:
  handler: src/functions/billing/getAccountCostsByPayer.handler
  name: ${env:STAGE}-get-account-costs-by-payer${env:VERSION}
  description: Função para retornar lista de contas pertencentes a uma payer com seus respectivos custos
  memorySize: 128
  events:
    - http:
        path: /billing/accounts/payer/{payer}
        method: get
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

generate-billing-excel:
  handler: src/functions/billing/generateBillingExcel.handler
  name: ${env:STAGE}-generate-billing-excel${env:VERSION}
  description: Função para retornar lista de contas pertencentes a uma payer com seus respectivos custos
  memorySize: 128
  timeout: 30
  layers:
    - DocumentGenerationLambdaLayer
  events:
    - http:
        path: /billing/excel/{payer}
        method: get
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}

generate-billing-pdf:
  handler: src/functions/billing/generateBillingPDF.handler
  name: ${env:STAGE}-generate-billing-pdf${env:VERSION}
  description: Função para retornar lista de contas pertencentes a uma payer com seus respectivos custos
  memorySize: 128
  timeout: 30
  layers:
    - DocumentGenerationLambdaLayer
  events:
    - http:
        path: /billing/pdf/{payer}
        method: get
        cors: true
        authorizer:
          name: ${env:AWS_API_GATEWAY_COGNITO_NAME}
          arn: arn:aws:cognito-idp:${env:AWS_REGION_LOCATION}:${env:ACCOUNT_ID}:userpool/${env:USER_POOL_ID}


