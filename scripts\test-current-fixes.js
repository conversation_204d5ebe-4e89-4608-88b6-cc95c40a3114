/**
 * Script para testar as correções implementadas
 */

const axios = require('axios');

const BASE_URL = 'https://pdu9dbemj1.execute-api.us-east-1.amazonaws.com/dev';

async function testEndpoint(path, origin, headers = {}) {
  try {
    const response = await axios.get(`${BASE_URL}${path}`, {
      headers: {
        'Origin': origin,
        ...headers
      },
      validateStatus: () => true,
      timeout: 10000
    });
    
    return {
      status: response.status,
      success: response.status < 400,
      corsOrigin: response.headers['access-control-allow-origin'],
      corsCredentials: response.headers['access-control-allow-credentials'],
      message: response.data?.message || 'OK'
    };
  } catch (error) {
    return {
      status: 0,
      success: false,
      error: error.message,
      corsOrigin: null,
      corsCredentials: null
    };
  }
}

async function runTests() {
  console.log('🧪 Testando correções implementadas...\n');
  
  const origin = 'https://dev.dsm.darede.com.br';
  
  // Teste 1: Endpoint de configuração (deve funcionar)
  console.log('1️⃣ Testando /auth/config');
  const configTest = await testEndpoint('/auth/config', origin);
  console.log(`   Status: ${configTest.status} ${configTest.success ? '✅' : '❌'}`);
  console.log(`   CORS Origin: ${configTest.corsOrigin || 'Não definido'}`);
  console.log(`   CORS Credentials: ${configTest.corsCredentials || 'Não definido'}`);
  
  // Teste 2: Endpoint protegido (deve retornar 401 - Unauthorized)
  console.log('\n2️⃣ Testando /read/id/user (protegido)');
  const readTest = await testEndpoint('/read/id/user', origin, { 'DynamoDB': 'dev-permissions' });
  console.log(`   Status: ${readTest.status} ${readTest.status === 401 ? '✅' : '❌'}`);
  console.log(`   Mensagem: ${readTest.message}`);
  console.log(`   CORS Origin: ${readTest.corsOrigin || 'Não definido'}`);
  
  // Teste 3: Origem não permitida (localhost)
  console.log('\n3️⃣ Testando origem não permitida (localhost)');
  const localhostTest = await testEndpoint('/auth/config', 'http://localhost:3000');
  console.log(`   Status: ${localhostTest.status} ${localhostTest.status === 403 ? '✅' : '❌'}`);
  console.log(`   Mensagem: ${localhostTest.message}`);
  
  // Teste 4: Origem permitida (dsm.darede.com.br)
  console.log('\n4️⃣ Testando origem principal (dsm.darede.com.br)');
  const mainOriginTest = await testEndpoint('/auth/config', 'https://dsm.darede.com.br');
  console.log(`   Status: ${mainOriginTest.status} ${mainOriginTest.success ? '✅' : '❌'}`);
  console.log(`   CORS Origin: ${mainOriginTest.corsOrigin || 'Não definido'}`);
  
  console.log('\n📊 Resumo dos Testes:');
  console.log('='.repeat(40));
  console.log(`✅ CORS para dev.dsm.darede.com.br: ${configTest.success ? 'FUNCIONANDO' : 'FALHOU'}`);
  console.log(`✅ Autenticação no /read/id/user: ${readTest.status === 401 ? 'FUNCIONANDO' : 'FALHOU'}`);
  console.log(`✅ Bloqueio de origem não permitida: ${localhostTest.status === 403 ? 'FUNCIONANDO' : 'FALHOU'}`);
  console.log(`✅ CORS para origem principal: ${mainOriginTest.success ? 'FUNCIONANDO' : 'FALHOU'}`);
  
  if (configTest.success && readTest.status === 401 && mainOriginTest.success) {
    console.log('\n🎉 CORREÇÕES IMPLEMENTADAS COM SUCESSO!');
    console.log('   - CORS funcionando para subdomínios');
    console.log('   - Autenticação aplicada corretamente');
    console.log('   - Endpoint não retorna mais "Request aborted"');
  } else {
    console.log('\n⚠️  Algumas correções ainda precisam de ajustes');
  }
}

runTests().catch(console.error);
