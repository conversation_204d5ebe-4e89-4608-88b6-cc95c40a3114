/**
 * Script para identificar endpoints com problemas de CORS
 */

const fs = require('fs');
const path = require('path');
const yaml = require('yamljs');

function findCorsIssues() {
  console.log('🔍 Procurando endpoints com problemas de CORS...\n');
  
  const handlersDir = path.join(__dirname, '..', 'handlers');
  const files = fs.readdirSync(handlersDir).filter(file => file.endsWith('.yml'));
  
  const issues = [];
  
  files.forEach(file => {
    const filePath = path.join(handlersDir, file);
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Procurar por origin: "*" ou origin: ${...}
    if (content.includes('origin: "*"') || content.includes('origin: ${')) {
      console.log(`❌ ${file}: Usando CORS do Serverless com wildcard`);
      
      try {
        const yamlContent = yaml.parse(content);
        Object.keys(yamlContent).forEach(functionName => {
          const func = yamlContent[functionName];
          if (func.events) {
            func.events.forEach(event => {
              if (event.http && event.http.cors && 
                  (event.http.cors.origin === '*' || 
                   (typeof event.http.cors.origin === 'string' && event.http.cors.origin.includes('${')))) {
                issues.push({
                  file,
                  function: functionName,
                  path: event.http.path,
                  method: event.http.method,
                  issue: 'CORS wildcard ou variável'
                });
              }
            });
          }
        });
      } catch (e) {
        console.log(`   ⚠️  Erro ao parsear ${file}: ${e.message}`);
      }
    }
    
    // Procurar por authorizer ativo
    if (content.includes('authorizer: ${self:custom.authorizer}')) {
      console.log(`⚠️  ${file}: Tem authorizer ativo (pode bloquear preflight)`);
    }
  });
  
  console.log('\n📊 Resumo dos problemas encontrados:');
  console.log('='.repeat(50));
  
  issues.forEach(issue => {
    console.log(`${issue.file} -> ${issue.function}`);
    console.log(`   ${issue.method.toUpperCase()} ${issue.path}`);
    console.log(`   Problema: ${issue.issue}\n`);
  });
  
  console.log(`Total de endpoints com problemas: ${issues.length}`);
  
  return issues;
}

findCorsIssues();
