/**
 * Script de teste para o serviço de Secrets Manager
 * Valida funcionamento, performance e tratamento de erros
 */

const { 
  getSecret, 
  getJWTCredentials, 
  getAPICredentials, 
  getMultipleSecrets,
  cache,
  config 
} = require('../src/shared/secrets/secrets-manager');

const { 
  generateToken, 
  verifyToken,
  getJWTConfig,
  clearJWTCache,
  getJWTCacheStats 
} = require('../src/shared/auth/jwt-utils-secrets');

const { 
  testAPIConnection,
  getAPIConfig,
  clearAPICache,
  getAPICacheStats 
} = require('../src/shared/api/api-client-secrets');

/**
 * Testa configuração básica do ambiente
 */
async function testarConfiguracaoAmbiente() {
  console.log('🔧 Testando configuração do ambiente...\n');
  
  const requiredEnvVars = [
    'AWS_REGION_LOCATION',
    'JWT_DECRIPTION_CREDENTIALS',
    'DSM_API_SECREAT_MANAGER'
  ];
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('❌ Variáveis de ambiente ausentes:', missingVars);
    return false;
  }
  
  console.log('✅ Todas as variáveis de ambiente necessárias estão definidas');
  console.log(`   AWS Region: ${process.env.AWS_REGION_LOCATION}`);
  console.log(`   JWT Secret ID: ${process.env.JWT_DECRIPTION_CREDENTIALS}`);
  console.log(`   API Secret ID: ${process.env.DSM_API_SECREAT_MANAGER}`);
  
  return true;
}

/**
 * Testa recuperação de credenciais JWT
 */
async function testarCredenciaisJWT() {
  console.log('\n🔐 Testando credenciais JWT...\n');
  
  try {
    const startTime = Date.now();
    const credentials = await getJWTCredentials();
    const endTime = Date.now();
    
    console.log('✅ Credenciais JWT recuperadas com sucesso');
    console.log(`   Tempo de resposta: ${endTime - startTime}ms`);
    console.log(`   Algorithm: ${credentials.algorithm}`);
    console.log(`   Issuer: ${credentials.issuer}`);
    console.log(`   Expires In: ${credentials.expiresIn}`);
    console.log(`   Refresh Expires In: ${credentials.refreshExpiresIn}`);
    
    // Testa cache
    const cacheStartTime = Date.now();
    await getJWTCredentials();
    const cacheEndTime = Date.now();
    
    console.log(`   Cache hit tempo: ${cacheEndTime - cacheStartTime}ms`);
    
    return true;
  } catch (error) {
    console.error('❌ Erro ao recuperar credenciais JWT:', error.message);
    return false;
  }
}

/**
 * Testa recuperação de credenciais de API
 */
async function testarCredenciaisAPI() {
  console.log('\n🌐 Testando credenciais de API...\n');
  
  try {
    const startTime = Date.now();
    const credentials = await getAPICredentials();
    const endTime = Date.now();
    
    console.log('✅ Credenciais de API recuperadas com sucesso');
    console.log(`   Tempo de resposta: ${endTime - startTime}ms`);
    console.log(`   Base URL: ${credentials.baseUrl}`);
    console.log(`   Has Username: ${!!credentials.username}`);
    console.log(`   Has Password: ${!!credentials.password}`);
    console.log(`   Has API Key: ${!!credentials.apiKey}`);
    console.log(`   Timeout: ${credentials.timeout}ms`);
    
    // Testa cache
    const cacheStartTime = Date.now();
    await getAPICredentials();
    const cacheEndTime = Date.now();
    
    console.log(`   Cache hit tempo: ${cacheEndTime - cacheStartTime}ms`);
    
    return true;
  } catch (error) {
    console.error('❌ Erro ao recuperar credenciais de API:', error.message);
    return false;
  }
}

/**
 * Testa geração e verificação de tokens JWT
 */
async function testarTokensJWT() {
  console.log('\n🎫 Testando geração e verificação de tokens...\n');
  
  try {
    const userPayload = {
      sub: 'test-user-123',
      userId: 'test-user-123',
      email: '<EMAIL>',
      role: 'user',
      permissions: ['read'],
      name: 'Usuário Teste'
    };
    
    // Gera token
    const startTime = Date.now();
    const token = await generateToken(userPayload);
    const generateTime = Date.now() - startTime;
    
    console.log('✅ Token gerado com sucesso');
    console.log(`   Tempo de geração: ${generateTime}ms`);
    console.log(`   Token length: ${token.length} chars`);
    
    // Verifica token
    const verifyStartTime = Date.now();
    const decoded = await verifyToken(token);
    const verifyTime = Date.now() - verifyStartTime;
    
    console.log('✅ Token verificado com sucesso');
    console.log(`   Tempo de verificação: ${verifyTime}ms`);
    console.log(`   Email: ${decoded.email}`);
    console.log(`   Role: ${decoded.role}`);
    console.log(`   Expires: ${new Date(decoded.exp * 1000).toISOString()}`);
    
    return true;
  } catch (error) {
    console.error('❌ Erro ao testar tokens JWT:', error.message);
    return false;
  }
}

/**
 * Testa conectividade com API
 */
async function testarConectividadeAPI() {
  console.log('\n🔗 Testando conectividade com API...\n');
  
  try {
    const result = await testAPIConnection();
    
    if (result.success) {
      console.log('✅ Conectividade com API bem-sucedida');
      console.log(`   Status: ${result.status}`);
      console.log(`   Base URL: ${result.baseUrl}`);
      console.log(`   Response Time: ${result.responseTime}`);
    } else {
      console.log('⚠️ Conectividade com API falhou (pode ser esperado)');
      console.log(`   Erro: ${result.error}`);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Erro ao testar conectividade:', error.message);
    return false;
  }
}

/**
 * Testa performance e cache
 */
async function testarPerformanceCache() {
  console.log('\n⚡ Testando performance e cache...\n');
  
  try {
    // Limpa caches
    cache.clear();
    clearJWTCache();
    clearAPICache();
    
    console.log('🧹 Caches limpos');
    
    // Testa múltiplas chamadas para medir cache
    const iterations = 5;
    const jwtTimes = [];
    const apiTimes = [];
    
    for (let i = 0; i < iterations; i++) {
      // JWT
      const jwtStart = Date.now();
      await getJWTCredentials();
      jwtTimes.push(Date.now() - jwtStart);
      
      // API
      const apiStart = Date.now();
      await getAPICredentials();
      apiTimes.push(Date.now() - apiStart);
    }
    
    console.log('📊 Resultados de performance:');
    console.log(`   JWT - Primeira chamada: ${jwtTimes[0]}ms`);
    console.log(`   JWT - Média cache hits: ${(jwtTimes.slice(1).reduce((a, b) => a + b, 0) / (iterations - 1)).toFixed(2)}ms`);
    console.log(`   API - Primeira chamada: ${apiTimes[0]}ms`);
    console.log(`   API - Média cache hits: ${(apiTimes.slice(1).reduce((a, b) => a + b, 0) / (iterations - 1)).toFixed(2)}ms`);
    
    // Estatísticas dos caches
    console.log('\n📈 Estatísticas dos caches:');
    console.log('   Secrets Manager:', cache.stats());
    console.log('   JWT:', getJWTCacheStats());
    console.log('   API:', getAPICacheStats());
    
    return true;
  } catch (error) {
    console.error('❌ Erro ao testar performance:', error.message);
    return false;
  }
}

/**
 * Testa tratamento de erros
 */
async function testarTratamentoErros() {
  console.log('\n🚨 Testando tratamento de erros...\n');
  
  let errorsHandled = 0;
  
  // Teste 1: Segredo inexistente
  try {
    await getSecret('arn:aws:secretsmanager:us-east-1:123456789012:secret:inexistente');
  } catch (error) {
    console.log('✅ Erro capturado para segredo inexistente');
    errorsHandled++;
  }
  
  // Teste 2: Token inválido
  try {
    await verifyToken('token.invalido.aqui');
  } catch (error) {
    console.log('✅ Erro capturado para token inválido');
    errorsHandled++;
  }
  
  // Teste 3: Múltiplos segredos com falhas
  try {
    const result = await getMultipleSecrets([
      process.env.JWT_DECRIPTION_CREDENTIALS,
      'segredo-inexistente'
    ]);
    
    if (result.errors.length > 0) {
      console.log('✅ Erros parciais tratados corretamente em múltiplos segredos');
      errorsHandled++;
    }
  } catch (error) {
    console.log('✅ Erro capturado em múltiplos segredos');
    errorsHandled++;
  }
  
  console.log(`📊 Total de erros tratados corretamente: ${errorsHandled}`);
  
  return errorsHandled > 0;
}

/**
 * Executa todos os testes
 */
async function executarTodosTestes() {
  console.log('🧪 Iniciando testes do Secrets Manager\n');
  console.log('=' .repeat(60));
  
  const testes = [
    { nome: 'Configuração do Ambiente', funcao: testarConfiguracaoAmbiente },
    { nome: 'Credenciais JWT', funcao: testarCredenciaisJWT },
    { nome: 'Credenciais API', funcao: testarCredenciaisAPI },
    { nome: 'Tokens JWT', funcao: testarTokensJWT },
    { nome: 'Conectividade API', funcao: testarConectividadeAPI },
    { nome: 'Performance e Cache', funcao: testarPerformanceCache },
    { nome: 'Tratamento de Erros', funcao: testarTratamentoErros }
  ];
  
  const resultados = [];
  
  for (const teste of testes) {
    try {
      const sucesso = await teste.funcao();
      resultados.push({ nome: teste.nome, sucesso });
    } catch (error) {
      console.error(`❌ Erro no teste ${teste.nome}:`, error.message);
      resultados.push({ nome: teste.nome, sucesso: false });
    }
  }
  
  // Resumo final
  console.log('\n' + '=' .repeat(60));
  console.log('📋 RESUMO DOS TESTES\n');
  
  const sucessos = resultados.filter(r => r.sucesso).length;
  const total = resultados.length;
  
  resultados.forEach(resultado => {
    const status = resultado.sucesso ? '✅' : '❌';
    console.log(`${status} ${resultado.nome}`);
  });
  
  console.log(`\n🎯 Resultado: ${sucessos}/${total} testes passaram`);
  
  if (sucessos === total) {
    console.log('🎉 Todos os testes passaram! Secrets Manager está funcionando corretamente.');
  } else {
    console.log('⚠️ Alguns testes falharam. Verifique a configuração.');
  }
  
  return sucessos === total;
}

// Executa testes se o arquivo for executado diretamente
if (require.main === module) {
  executarTodosTestes()
    .then(success => process.exit(success ? 0 : 1))
    .catch(error => {
      console.error('💥 Erro fatal nos testes:', error);
      process.exit(1);
    });
}

module.exports = {
  testarConfiguracaoAmbiente,
  testarCredenciaisJWT,
  testarCredenciaisAPI,
  testarTokensJWT,
  testarConectividadeAPI,
  testarPerformanceCache,
  testarTratamentoErros,
  executarTodosTestes
};
