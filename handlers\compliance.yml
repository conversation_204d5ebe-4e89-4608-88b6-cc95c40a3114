compliance-status-monitoring:
  handler: src/functions/compliance/complianceStatusMonitoring.handler
  name: ${self:custom.dotenv.STAGE}-compliance-invoke${self:custom.dotenv.VERSION}
  description: Função que invoka o lambda que verifica os status das contas dos clientes
  memorySize: 128
  timeout: 900

compliance-check-accounts-status:
  handler: src/functions/compliance/checkAccountsStatus.handler
  name: ${self:custom.dotenv.STAGE}-compliance-check-accounts-status${self:custom.dotenv.VERSION}
  description: Função que verifica cada um dos itens de compliance na conta do cliente e atualiza os status na tabela STAGE-compliance-monitoring
  memorySize: 128
  timeout: 900

disparity-contracts-invoke:
  handler: src/functions/compliance/disparityContractsInvoke.handler
  name: ${self:custom.dotenv.STAGE}-disparity-contracts-invoke${self:custom.dotenv.VERSION}
  description: Função que realiza o invoke do script de compliance de disparidades de contratos
  memorySize: 128

