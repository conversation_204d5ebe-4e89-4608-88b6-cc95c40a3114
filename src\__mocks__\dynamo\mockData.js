export const mockedData = {
  id: "7ca9e64e-02fb-445e-a16d-6ba00ccb35e7",
  accounts: [
    {
      bill_entity: "AWS",
      cost: 0,
      legal_entity: "Amazon AWS Serviços Brasil Ltda.",
      sub_account: "************",
    },
    {
      bill_entity: "AWS",
      cost: 0,
      legal_entity: "Amazon AWS Serviços Brasil Ltda.",
      sub_account: "************",
    },
    {
      bill_entity: "AWS",
      cost: 0,
      legal_entity: "Amazon AWS Serviços Brasil Ltda.",
      sub_account: "************",
    },
    {
      bill_entity: "AWS",
      cost: 0,
      legal_entity: "Amazon AWS Serviços Brasil Ltda.",
      sub_account: "************",
    },
    {
      bill_entity: "AWS",
      cost: 0,
      legal_entity: "Amazon AWS Serviços Brasil Ltda.",
      sub_account: "************",
    },
    {
      bill_entity: "AWS",
      cost: 0,
      legal_entity: "Amazon AWS Serviços Brasil Ltda.",
      sub_account: "************",
    },
    {
      bill_entity: "AWS",
      cost: 0,
      legal_entity: "Amazon AWS Serviços Brasil Ltda.",
      sub_account: "************",
    },
    {
      bill_entity: "AWS",
      cost: 0,
      legal_entity: "Amazon AWS Serviços Brasil Ltda.",
      sub_account: "************",
    },
    {
      bill_entity: "AWS",
      cost: 0,
      legal_entity: "Amazon AWS Serviços Brasil Ltda.",
      sub_account: "************",
    },
    {
      bill_entity: "AWS",
      cost: 0,
      legal_entity: "Amazon AWS Serviços Brasil Ltda.",
      sub_account: "************",
    },
    {
      bill_entity: "AWS",
      cost: 0,
      legal_entity: "Amazon AWS Serviços Brasil Ltda.",
      sub_account: "************",
    },
    {
      bill_entity: "AWS",
      cost: 0,
      legal_entity: "Amazon AWS Serviços Brasil Ltda.",
      sub_account: "************",
    },
  ],
  created_at: "2023-04-04 16:22:37",
  identifications: {
    contract_id: "672f02cb-dd0b-47aa-924f-c13405c27cb4",
    customer_id: "e01824aa-16d7-4c4b-805d-0717b88c2ea4",
    customer_name: "Dosystems",
  },
  month: 4,
  payer_account: "************",
  total_discount: 0,
  total_neg: 0,
  total_pos: 0,
  total_saving_plans: 0,
  total_spp_discount: 0,
  updated_at: "2023-04-12 18:22:35",
  year: 2023,
};

export const mockedCustomerRegister = {
  id: "9aa5a728-304e-43e3-8994-937cb723f3da",
  accounts: [
    {
      id: "c44ceb32-6832-4913-8103-e89305ce16e1",
      account_id: "************",
      con_status: null,
      customer_id: "9aa5a728-304e-43e3-8994-937cb723f3da",
      payer: false,
      profile: "Plataforma-Darede-Insights",
      region: "us-east-1",
      role_exists: true,
      role_name: "darede-full",
      updated_at: "2023-03-30T21:59:45.436Z",
    },
    {
      id: "4973bb61-ebe8-4045-a813-099c22158efd",
      account_id: "************",
      con_status: null,
      created_at: "2023-07-17 18:08:33",
      customer_id: "9aa5a728-304e-43e3-8994-937cb723f3da",
      profile: "the-public-dosystems",
      region: "us-east-1",
      role_exists: true,
      role_name: "darede-full",
      updated_at: "2023-07-17 18:08:33",
    },
    {
      id: "08ad9ff3-5fc0-4fa1-aa7e-285fbc10f01d",
      account_id: "************",
      con_status: null,
      created_at: "2023-08-01 11:55:48",
      customer_id: "9aa5a728-304e-43e3-8994-937cb723f3da",
      profile: "Darede-Prod-New",
      region: "us-east-1",
      role_exists: true,
      role_name: "darede",
      updated_at: "2023-08-01 11:55:48",
    },
    {
      id: "ede922ee-d30d-4975-aa54-bab9a7cfabff",
      account_id: "************",
      con_status: null,
      created_at: "2023-08-25 17:39:45",
      customer_id: "9aa5a728-304e-43e3-8994-937cb723f3da",
      payer: true,
      profile: "pa_senai_spp",
      region: "us-east-1",
      role_exists: true,
      role_name: "darede-full",
      updated_at: "2023-08-25T20:59:50.193Z",
    },
    {
      id: "81f3a62a-72d1-43b5-80f2-c6bf44e97a6c",
      account_id: "************",
      con_status: null,
      customer_id: "9aa5a728-304e-43e3-8994-937cb723f3da",
      payer: true,
      profile: "TAM-Sandbox",
      region: "us-east-1",
      role_name: "darede-full",
      updated_at: "2023-10-03 16:39:49",
    },
    {
      id: "9b085b3f-7824-4758-a5df-f87270df5583",
      account_id: "************",
      con_status: null,
      created_at: "2023-10-20 10:36:35",
      customer_id: "9aa5a728-304e-43e3-8994-937cb723f3da",
      payer: false,
      profile: "aws-acc-secoffice-shared",
      region: "Norte Virginia",
      role_exists: false,
      role_name: "darede",
      updated_at: "2023-10-20 10:36:38",
    },
    {
      id: "8fa017ad-8f0c-4206-ac25-277d4dde92c0",
      account_id: "************",
      con_status: null,
      created_at: "2023-10-20 10:37:34",
      customer_id: "9aa5a728-304e-43e3-8994-937cb723f3da",
      payer: false,
      profile: "aws-acc-secoffice-dev",
      region: "Norte Virginia",
      role_exists: false,
      role_name: "ReadOnly",
      updated_at: "2023-10-20 10:37:35",
    },
    {
      id: "866119c8-f889-4cc4-8af7-4d8d2f4849a7",
      account_id: "************",
      con_status: null,
      created_at: "2023-10-20 10:38:02",
      customer_id: "9aa5a728-304e-43e3-8994-937cb723f3da",
      payer: false,
      profile: "SIEM-Darede",
      region: "Norte Virginia",
      role_exists: true,
      role_name: "ReadOnly",
      updated_at: "2023-10-20 10:38:04",
    },
    {
      id: "b628fa63-bf64-43ae-a9af-83e518560f81",
      account_id: "************",
      con_status: null,
      customer_id: "9aa5a728-304e-43e3-8994-937cb723f3da",
      payer: true,
      profile: "pa_marketplace_darede",
      region: "us-east-1",
      role_name: "darede-full",
      updated_at: "2023-10-20 10:44:47",
    },
    {
      id: "b1dfe0ef-011b-4cdd-a567-b0a7860efc87",
      account_id: "************",
      con_status: null,
      created_at: "2024-03-01 20:13:43",
      customer_id: "9aa5a728-304e-43e3-8994-937cb723f3da",
      payer: false,
      profile: "Logs",
      region: "us-east-1",
      role_exists: true,
      role_name: "darede-full",
      updated_at: "2024-03-01 20:13:43",
    },
    {
      id: "3275de5d-c879-492a-b171-5e1f7d7a7426",
      account_id: "************",
      con_status: null,
      customer_id: "9aa5a728-304e-43e3-8994-937cb723f3da",
      payer: true,
      profile: "Darede_Root",
      region: "us-east-1",
      role_exists: true,
      role_name: "darede",
      updated_at: "2024-03-07 14:54:52",
    },
  ],
  active: 1,
  address: {
    bairro: "",
    cep: "",
    cidade: "",
    complemento: "",
    logradouro: "",
    numero: "",
    pais: "",
    tipo_logradouro: "",
  },
  cnpj: "-",
  contacts: [],
  created_at: "2021-09-17T23:23:00.000Z",
  dsm_id: "CL000027",
  extent: {},
  hasActiveContract: 1,
  has_active_contracts: 1,
  identifications: {
    crm_id: 2410,
    itsm_id: "1350",
  },
  legal_nature: {},
  members: [],
  names: {
    fantasy_name: "Cliente Darede",
    name: "",
  },
  payment_percent: null,
  phones: {
    ddd1: "",
    ddd2: "",
    ddd_fax: "",
    fax: "",
    telefone1: "",
    telefone2: "",
  },
  social_capital: "",
  updated_at: "2023-11-09 00:15:23",
};
