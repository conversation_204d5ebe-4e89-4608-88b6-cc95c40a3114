import { message, json } from '../../shared/response'
import { parseBody } from '../../shared/parsers'
import { CognitoIdentityServiceProvider } from 'aws-sdk'

const cognito = new CognitoIdentityServiceProvider({
  region: process.env.AWS_REGION_LOCATION
})

async function sendDataToUser(status, statusString, data) {
  const msg = await message(statusString, data)
  return await json(msg, status)
}

export const handler = async (event, context) => {
  const { user, role, stage } = parseBody(event)

  try {
    await cognito.adminUpdateUserAttributes({
      UserAttributes: [
        {
          Name: `custom:${stage !== 'prod' ? stage + '_' : ''}role`,
          Value: role
        }
      ],
      UserPoolId: process.env.USER_POOL_ID,
      Username: user
    }).promise()

    return await sendDataToUser(200, 'success')
  } catch (error) {
    return await sendDataToUser(500, 'error', error)
  }
};