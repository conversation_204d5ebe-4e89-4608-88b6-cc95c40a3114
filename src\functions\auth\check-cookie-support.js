/**
 * Endpoint para verificar se o cliente suporta cookies HttpOnly
 * Este endpoint define um cookie de teste e retorna informações sobre suporte
 */

const { responseWithCookies, responseWithSuccess } = require('../../shared/response-cjs');
const { createCookieHeader } = require('../../shared/auth/cookie-utils-cjs');

exports.handler = async (event, context) => {
  console.log('Check cookie support endpoint called');
  
  try {
    // Cria um cookie de teste
    const testCookieHeader = createCookieHeader('dsm_test_cookie', 'test-value', {
      maxAge: 60 * 1000, // 1 minuto
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/'
    });
    
    const response = {
      httpOnlySupported: true,
      cookieTestSet: true,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      corsEnabled: true,
      credentialsSupported: true,
      message: 'Cookie de teste definido. Verifique se foi recebido pelo cliente.'
    };
    
    console.log('Cookie support check completed successfully');
    
    return responseWithCookies(response, [testCookieHeader]);
    
  } catch (error) {
    console.error('Erro no check cookie support:', error);
    
    return responseWithSuccess({
      httpOnlySupported: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
};
